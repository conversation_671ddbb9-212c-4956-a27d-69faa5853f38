name: vibeo
description: "Find your vibe"

publish_to: 'none'

version: 2.0.7+2

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.0.0
  bounce: ^1.0.2
  pinput: ^5.0.1
  video_player: ^2.9.2
  path_provider: ^2.1.5
  path: ^1.9.0
  logger: ^2.4.0
  stack_trace: ^1.11.1
  dio: ^5.7.0
  freezed_annotation: ^2.4.4
  firebase_auth: ^5.3.2
  firebase_core: ^3.7.0
  firebase_analytics: ^11.3.4
  firebase_crashlytics: ^4.1.4
  firebase_performance: ^0.10.0+9
  equatable: ^2.0.5
  google_sign_in: ^6.2.2
  sign_in_with_apple: ^6.1.3
  flutter_carousel_slider: ^1.1.0
  percent_indicator: ^4.2.3
  image_picker: ^1.1.2
  flutter_branch_sdk: ^8.4.1
  video_compress: ^3.1.4
  flutter_dotenv: ^5.2.1
  shimmer: ^3.0.0
  intl: ^0.20.1

  geolocator: ^13.0.2
  google_maps_flutter: ^2.10.0
  flutter_google_maps_webservices: ^1.1.1
  video_thumbnail: ^0.5.3
  gcloud: ^0.8.18
  googleapis_auth: ^1.6.0
  uuid: ^4.5.1
  extended_nested_scroll_view: ^6.2.1
  clipboard: ^0.1.3

  flutter_markdown: ^0.7.4+3
  like_button: ^2.0.5
  share_plus: ^10.1.2
  flutter_staggered_grid_view: ^0.7.0
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.3.3
  url_launcher: ^6.3.1
  image_gallery_saver: ^2.0.3
  cached_video_player_plus: ^3.0.3
  cached_network_image:


  sentry_flutter: ^8.14.1
  onesignal_flutter: ^5.0.0

  nfc_manager: ^3.5.0
  flutter_nfc_kit: ^3.6.0
  ndef: ^0.3.3
  super_cupertino_navigation_bar: ^2.1.2

  mapbox_maps_flutter: ^2.8.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  dart_code_linter: ^1.1.3
  flutter_lints: ^5.0.0
  very_good_analysis: ^7.0.0
  build_runner: ^2.4.13
  json_serializable: ^6.8.0

  freezed: ^2.5.7


flutter:

  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/animations/
    - assets/images/
    - assets/onboard/
    - assets/icons/
    - assets/music_genres/
    - .dev.env
    - .prod.env
    - assets/credentials.json
    - assets/files/
    - assets/filters/
    - assets/banner/
    - assets/venue/

  fonts:
    - family: PulpDisplay
      fonts:
        - asset: assets/fonts/PulpDisplay/PulpDisplay-Regular.ttf
        - asset: assets/fonts/PulpDisplay/PulpDisplay-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/PulpDisplay/PulpDisplay-Light.ttf
          weight: 300
        - asset: assets/fonts/PulpDisplay/PulpDisplay-Medium.ttf
          weight: 500
        - asset: assets/fonts/PulpDisplay/PulpDisplay-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/PulpDisplay/PulpDisplay-Bold.ttf
          weight: 700
        - asset: assets/fonts/PulpDisplay/PulpDisplay-ExtraBold.ttf
          weight: 900
