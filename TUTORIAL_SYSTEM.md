# Tutorial System Implementation

This document describes the tutorial system implemented for the Vibeo app, which provides interactive onboarding and feature discovery for users.

## Overview

The tutorial system consists of several components that work together to provide a smooth, interactive tutorial experience:

1. **Tutorial Storage Service** - Manages tutorial completion state
2. **Tutorial Manager** - Coordinates tutorial flow across the app
3. **Tutorial Overlay** - Displays tutorial steps with glassmorphism design
4. **Tutorial Steps** - Defines tutorial content and positioning
5. **Page Integration** - Integrates tutorials into specific app pages

## Components

### TutorialStorageService
- **Location**: `lib/services/tutorial/tutorial_storage_service.dart`
- **Purpose**: Manages tutorial completion status using Flutter secure storage
- **Key Methods**:
  - `markTutorialCompleted(String tutorialId)` - Mark a tutorial as completed
  - `isTutorialCompleted(String tutorialId)` - Check if tutorial is completed
  - `resetAllTutorials()` - Reset all tutorial progress

### TutorialManager
- **Location**: `lib/widgets/tutorial/tutorial_manager.dart`
- **Purpose**: Singleton that manages tutorial flow and state
- **Key Methods**:
  - `initialize(String userID)` - Initialize with user ID
  - `startTutorial()` - Start a tutorial sequence
  - `registerTargetKey()` - Register UI elements for highlighting
  - `startForYouTutorial()`, `startBeoTutorial()`, etc. - Page-specific tutorials

### TutorialOverlay
- **Location**: `lib/widgets/tutorial/tutorial_overlay.dart`
- **Purpose**: Displays tutorial steps with animations and glassmorphism design
- **Features**:
  - Dark overlay background
  - Target element highlighting
  - Animated tutorial content
  - Progress indicators
  - Skip functionality

### TutorialSteps
- **Location**: `lib/constants/tutorial/tutorial_steps.dart`
- **Purpose**: Defines tutorial content, positioning, and flow
- **Tutorial Types**:
  - For You Page Tutorial
  - Beo Page Tutorial
  - Bag Page Tutorial
  - Add to Cart Flow Tutorial
  - Navigation Tutorial

## Tutorial Flow

### Initial App Tutorial
1. User opens the app for the first time
2. HomePage initializes TutorialManager with user ID
3. If initial tutorial hasn't been completed, it starts automatically
4. Shows navigation tutorial and welcome message
5. Marks initial tutorial as completed

### Page-Specific Tutorials
1. User navigates to a new page (For You, Beo, Bag)
2. HomePage checks if page tutorial has been completed
3. If not completed, starts page-specific tutorial after a delay
4. Tutorial highlights relevant UI elements
5. Marks page tutorial as completed

### Add to Cart Tutorial
1. User taps on an offer tile for the first time
2. OfferTile triggers add to cart tutorial check
3. If not completed, shows tutorial about cart functionality
4. Guides user through the cart flow

## Integration Points

### HomePage
- Initializes TutorialManager
- Registers navigation bar keys
- Triggers page-specific tutorials on tab changes

### ForYouPage
- Registers keys for Live Tiles, By Area, By Music, Places Near You
- Enables tutorial highlighting for these sections

### BasketPage
- Registers keys for bag tabs and venue tiles
- Enables tutorial highlighting for cart functionality

### OfferTile
- Triggers add to cart tutorial on first offer addition
- Integrates with tutorial flow

### Settings Page
- Provides "Reset Tutorials" option
- Allows users to replay tutorials

## Tutorial Content

### For You Page Tutorial
1. Welcome message
2. Live venue videos explanation
3. Explore by area feature
4. Find your music feature
5. Places near you feature

### Beo Page Tutorial
1. Introduction to Beo AI
2. Chat functionality
3. Smart recommendations

### Bag Page Tutorial
1. Bag overview
2. Tab sections explanation
3. Venue grouping
4. Swipe to remove feature

### Add to Cart Tutorial
1. Finding offers
2. Adding to cart
3. Checking bag
4. Viewing venue offers

## Usage

### Starting a Tutorial Manually
```dart
await TutorialManager.instance.startForYouTutorial(context);
```

### Checking Tutorial Status
```dart
bool completed = await TutorialManager.instance.shouldShowTutorial('for_you_page');
```

### Registering Target Keys
```dart
TutorialManager.instance.registerTargetKey('live_tiles', _liveTilesKey);
```

### Resetting Tutorials
```dart
await TutorialManager.instance.resetAllTutorials();
```

## Design Features

- **Glassmorphism Design**: Tutorial overlays use blur effects and transparency
- **Smooth Animations**: Fade and scale animations for tutorial appearance
- **Target Highlighting**: White border and glow effects for highlighted elements
- **Progress Indicators**: Visual progress bars showing tutorial step progress
- **Skip Functionality**: Users can skip tutorials at any time

## Storage

Tutorial completion status is stored using Flutter Secure Storage with user-specific keys:
- Key format: `tutorial_status_{userID}`
- Stores array of completed tutorial IDs
- Persists across app sessions

## Future Enhancements

1. **Tutorial Analytics**: Track tutorial completion rates
2. **Dynamic Content**: Load tutorial content from server
3. **Conditional Tutorials**: Show tutorials based on user behavior
4. **Interactive Elements**: Add more interactive tutorial steps
5. **Accessibility**: Improve accessibility for tutorial overlays
