# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

.env
.prod.env
.dev.env

android/
linux/
macos/
web/
windows/

assets/credentials.json

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Flutter/Dart specific
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
build/
ios/Pods/
.pub-cache/
.pub/
coverage/

# Android specific
android/app/debug
android/app/profile
android/app/release
android/.gradle
android/local.properties
android/**/gradle-wrapper.jar
android/captures/

# iOS specific
ios/.generated/
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_export_environment.sh
ios/Flutter/Flutter.podspec
ios/*.mode1v3
ios/*.mode2v3
ios/*.moved-aside
ios/*.pbxuser
ios/*.perspectivev3
ios/**/*sync/
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig

# IDE specific
.idea/
.vscode/
*.iml
*.iws
.DS_Store

# Generated files
*.g.dart
*.freezed.dart
*.mocks.dart

# Logs
*.log
