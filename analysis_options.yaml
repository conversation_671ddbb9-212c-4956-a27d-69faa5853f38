include: package:very_good_analysis/analysis_options.yaml

linter:
  rules:
    # Disable alphabetical sorting
    sort_pub_dependencies: false
    directives_ordering: false
    sort_constructors_first: false
    sort_unnamed_constructors_first: false
    
    # Disable file naming restrictions
    file_names: false
    
    # Disable empty block warnings
    empty_constructor_bodies: false
    
    # Allow dynamic
    avoid_dynamic_calls: false
    
    # Other common rules to disable
    public_member_api_docs: false
    lines_longer_than_80_chars: false

analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/*.gr.dart"
    - "lib/generated/**"
  errors:
    invalid_annotation_target: ignore
    sort_pub_dependencies: ignore
    directives_ordering: ignore
    unnecessary_string_interpolations: ignore
    file_names: ignore  # Ensures no error for mismatched file and class names
    avoid_dynamic_calls: ignore

    strict_raw_type: ignore
    inference_failure_on_instance_creation: ignore
    depend_on_referenced_packages: ignore
    inference_failure_on_function_invocation: ignore
    avoid_redundant_argument_values: ignore
    omit_local_variable_types: ignore
    avoid_slow_async_io: ignore
    use_if_null_to_convert_nulls_to_bools: ignore
    use_is_even_rather_than_modulo: ignore
    no_default_cases: ignore
    avoid_catches_without_on_clauses: ignore
    constant_identifier_names: ignore

dart_code_linter:
  metrics:
    cyclomatic-complexity: 20
    number-of-parameters: 4
    maximum-nesting-level: 5
  metrics-exclude:
    - test/**
  rules-exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
  rules:
    # Removed avoid-dynamic from rules
    - avoid-passing-async-when-sync-expected
    - avoid-redundant-async
    - avoid-unnecessary-type-assertions
    - avoid-unnecessary-type-casts
    - avoid-unrelated-type-assertions
    - avoid-unused-parameters
    - avoid-nested-conditional-expressions
    - newline-before-return
    - no-boolean-literal-compare
    - prefer-trailing-comma
    - prefer-conditional-expressions
    - no-equal-then-else
    - prefer-moving-to-variable
