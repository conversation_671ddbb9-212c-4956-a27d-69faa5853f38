#!/bin/bash

# Text colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_message() {
    echo -e "${2}===> ${1}${NC}"
}

check_status() {
    if [ $? -eq 0 ]; then
        print_message "$1 ✅" "${GREEN}"
    else
        print_message "$2 ❌" "${RED}"
        exit 1
    fi
}

# Clean and get dependencies
print_message "Cleaning Flutter project..." "${YELLOW}"
flutter clean
flutter pub get
check_status "Project cleaned and dependencies updated" "Failed to clean/update project"

# Format code
print_message "Formatting code..." "${YELLOW}"
dart format .
check_status "Code formatted successfully" "Code formatting failed"

# Run static analysis
print_message "Running Flutter analyze..." "${YELLOW}"
flutter analyze
check_status "Flutter analyze passed" "Flutter analyze failed"

# Run dart_code_linter
if flutter pub global list | grep -q "dart_code_linter"; then
    print_message "Running dart_code_linter..." "${YELLOW}"
    dart run dart_code_linter:metrics analyze lib
    check_status "Linter analysis completed" "Linter analysis failed"
else
    print_message "Installing dart_code_linter..." "${YELLOW}"
    flutter pub global activate dart_code_linter
    dart run dart_code_linter:metrics analyze lib
    check_status "Linter analysis completed" "Linter analysis failed"
fi

# Run tests if they exist
if [ -d "test" ]; then
    print_message "Running tests..." "${YELLOW}"
    flutter test
    check_status "Tests passed successfully" "Tests failed"
else
    print_message "No tests directory found. Skipping tests." "${YELLOW}"
fi

# Start the app
print_message "All checks passed! Starting Flutter app..." "${GREEN}"
flutter run "$@"