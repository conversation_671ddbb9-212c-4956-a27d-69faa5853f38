import 'package:flutter_test/flutter_test.dart';
import 'package:vibeo/services/basket/basket_storage_service.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

// Mock implementation of FlutterSecureStorage for testing
class MockSecureStorage extends FlutterSecureStorage {
  final Map<String, String> _storage = {};

  @override
  Future<void> write({
    required String key,
    required String? value,
    String? namespace,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    if (value != null) {
      _storage[key] = value;
    }
  }

  @override
  Future<String?> read({
    required String key,
    String? namespace,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    return _storage[key];
  }

  @override
  Future<void> delete({
    required String key,
    String? namespace,
    IOSOptions? iOptions,
    AndroidOptions? aOptions,
    LinuxOptions? lOptions,
    WebOptions? webOptions,
    MacOsOptions? mOptions,
    WindowsOptions? wOptions,
  }) async {
    _storage.remove(key);
  }
}

void main() {
  // Initialize Flutter binding
  TestWidgetsFlutterBinding.ensureInitialized();
  group('BasketStorageService', () {
    late BasketStorageService basketStorageService;
    late MockSecureStorage mockStorage;
    const String testUserID = 'test-user-123';

    setUp(() {
      mockStorage = MockSecureStorage();
      basketStorageService =
          BasketStorageService(testUserID, storage: mockStorage);
    });

    test('addOfferToBasket should add offer with timestamp', () async {
      // Add an offer to the basket
      await basketStorageService.addOfferToBasket('venue1', 'offer1');

      // Get the basket items
      final basketItems = await basketStorageService.getBasketItems();

      // Verify the offer was added with a timestamp
      expect(basketItems.containsKey('venue1'), true);
      expect(basketItems['venue1']!.length, 1);

      final offerItem = basketItems['venue1']![0];
      expect(offerItem is Map, true);
      expect(offerItem['id'], 'offer1');
      expect(offerItem['timestamp'] != null, true);
    });

    test('getBasketItemsSorted should return items sorted by timestamp',
        () async {
      // Add offers with controlled timestamps
      await basketStorageService.addOfferToBasket('venue1', 'offer1');
      await Future.delayed(const Duration(milliseconds: 10));
      await basketStorageService.addOfferToBasket('venue1', 'offer2');
      await Future.delayed(const Duration(milliseconds: 10));
      await basketStorageService.addOfferToBasket('venue2', 'offer3');

      // Get sorted basket items
      final sortedItems = await basketStorageService.getBasketItemsSorted();

      // Verify venue1 offers are sorted by timestamp (newest first)
      expect(sortedItems['venue1']!.length, 2);
      expect(sortedItems['venue1']![0]['id'], 'offer2');
      expect(sortedItems['venue1']![1]['id'], 'offer1');

      // Verify venue2 offer is present
      expect(sortedItems['venue2']!.length, 1);
      expect(sortedItems['venue2']![0]['id'], 'offer3');
    });

    test('removeOfferFromBasket should remove the offer', () async {
      // Add offers
      await basketStorageService.addOfferToBasket('venue1', 'offer1');
      await basketStorageService.addOfferToBasket('venue1', 'offer2');

      // Remove one offer
      await basketStorageService.removeOfferFromBasket('venue1', 'offer1');

      // Verify only offer2 remains
      final basketItems = await basketStorageService.getBasketItems();
      expect(basketItems['venue1']!.length, 1);
      expect(basketItems['venue1']![0]['id'], 'offer2');
    });

    test('isOfferInBasket should correctly check if offer exists', () async {
      // Add an offer
      await basketStorageService.addOfferToBasket('venue1', 'offer1');

      // Check if offers exist
      final offer1Exists =
          await basketStorageService.isOfferInBasket('venue1', 'offer1');
      final offer2Exists =
          await basketStorageService.isOfferInBasket('venue1', 'offer2');

      // Verify results
      expect(offer1Exists, true);
      expect(offer2Exists, false);
    });

    test('getOfferIDsForVenue should return just the IDs', () async {
      // Add offers
      await basketStorageService.addOfferToBasket('venue1', 'offer1');
      await basketStorageService.addOfferToBasket('venue1', 'offer2');

      // Get offer IDs
      final offerIDs = await basketStorageService.getOfferIDsForVenue('venue1');

      // Verify results
      expect(offerIDs.length, 2);
      expect(offerIDs.contains('offer1'), true);
      expect(offerIDs.contains('offer2'), true);
    });
  });
}
