import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/venue/venue_model.dart';

part 'search_response.freezed.dart';
part 'search_response.g.dart';

@freezed
class SearchResponse with _$SearchResponse {
  factory SearchResponse({
    @Default([])
    @JsonKey(name: 'venues', fromJson: _parseVenues)
    List<VenueModel> venues,
    @Default([])
    @<PERSON>sonKey(name: 'feeds', fromJson: _parseFeeds)
    List<FeedModel> feeds,
  }) = _SearchResponse;

  factory SearchResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchResponseFromJson(json);
}

List<VenueModel> _parseVenues(List<dynamic>? venueList) {
  if (venueList == null) return [];
  return venueList.map((venue) {
    var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
    if (venueModel.thumbnails.isEmpty) {
      venueModel = venueModel.copyWith(
        thumbnails: [venueModel.imageLink],
      );
    }
    return venueModel;
  }).toList();
}

List<FeedModel> _parseFeeds(List<dynamic>? feedList) {
  if (feedList == null) return [];
  return feedList
      .map((feed) => FeedModel.fromJson(feed as Map<String, dynamic>))
      .toList();
}
