import 'package:vibeo/models/user/token_model.dart';
import 'package:vibeo/models/user/user_model.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';

class RegistrationResponse {
  final UserModel user;
  final TokenModel tokens;

  RegistrationResponse({
    required this.user,
    required this.tokens,
  });

  factory RegistrationResponse.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      throw BusinessLogicException(message: 'Response data is null');
    }

    // Handle nested user object
    final userData = json['user'];
    if (userData == null) {
      throw BusinessLogicException(
        message: 'User data is missing from response',
      );
    }

    return RegistrationResponse(
      user: UserModel.fromJson(userData as Map<String, dynamic>),
      tokens: TokenModel.fromJson(json['tokens'] as Map<String, dynamic>),
    );
  }
}
