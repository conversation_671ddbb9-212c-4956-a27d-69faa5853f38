class AuthResult {
  final bool success;
  final AuthUser? user;
  final String? errorMessage;

  AuthResult({
    required this.success,
    this.user,
    this.errorMessage,
  });
}

class PhoneAuthResult {
  final String verificationId;
  final bool codeSent;
  final String? errorMessage;

  PhoneAuthResult({
    required this.verificationId,
    required this.codeSent,
    this.errorMessage,
  });
}

class AuthUser {
  final String id;
  final String? email;
  final String? phoneNumber;
  final String? displayName;
  final String? photoUrl;
  final String? fullName;

  AuthUser({
    required this.id,
    this.email,
    this.phoneNumber,
    this.displayName,
    this.photoUrl,
    this.fullName,
  });
}
