import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vibeo/constants/constants.dart';
import 'package:vibeo/models/location/location.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

@freezed
class UserModel with _$UserModel {
  factory UserModel({
    @Json<PERSON>ey(name: 'uid') required String uid,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'email') required String email,
    @<PERSON>sonKey(name: 'fullName') required String fullName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'gender') required String gender,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'age') required int age,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'dob') required String dob,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phoneNumber') required String phoneNumber,
    @J<PERSON><PERSON>ey(name: 'genres') required List<String> genres,
    @Default('') @Json<PERSON>ey(name: 'extras') String? extras,
    @Default(noProfileImageUrl) @<PERSON>sonKey(name: 'photoURL') String photoURL,
    @Default(false) @<PERSON>son<PERSON>ey(name: 'dj') bool dj,
    @Json<PERSON>ey(name: 'location') Location? location,
    @Default(false) @Json<PERSON>ey(name: 'contentCreator') bool contentCreator,
    @Default(10) @JsonKey(name: 'feedRateLimit') int? feedRateLimit,
    @Default(false) @JsonKey(name: 'partner') bool isPartner,
    @Default('') @JsonKey(name: 'createdDate') String createdDate,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
}

extension UserModelX on UserModel {
  UserModel updateLocation(double latitude, double longitude) {
    return copyWith(
      location: Location(
        latitude: latitude,
        longitude: longitude,
      ),
    );
  }
}
