import 'package:vibeo/models/offer/offer_model.dart';

/// Utility class for parsing values
class ParseUtils {
  /// Parse a dynamic value to int
  static int parseNumToInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) return int.tryParse(value) ?? 0;
    if (value is double) return value.toInt();
    return 0;
  }

  /// Parse a dynamic value to double
  static double parseNumToDouble(dynamic value) {
    if (value == null) return 0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0;
  }

  static bool parseTextToBool(String value) {
    if (value == 'ACTIVE') return true;
    return false;
  }

  static String? parseText(String value) {
    if (value.trim() == '') return null;
    if (value == 'null') return null;
    if (value.isEmpty) return null;

    return value;
  }

  static VoucherType parseVoucherType(dynamic value) {
    if (value is String) {
      for (final enumMember in VoucherType.values) {
        if (enumMember.name == value) {
          // Here, "REGULAR" == "REGULAR" will be true
          return enumMember; // Returns VoucherType.REGULAR
        }
      }
    }
    return VoucherType.REGULAR; // Default
  }

  static VoucherCategory parseVoucherCategory(dynamic value) {
    if (value is String) {
      for (final enumMember in VoucherCategory.values) {
        if (enumMember.name == value) {
          // Here, "TIMEBASED" == "TIMEBASED" will be true
          return enumMember; // Returns VoucherCategory.TIMEBASED
        }
      }
    }
    return VoucherCategory.ONGOING; // Default
  }

  static String parseStartTime(dynamic value) {
    String rawTime = '';

    if (value is Map<String, dynamic> && value.isNotEmpty) {
      final firstDayEntry = value.values.first;
      if (firstDayEntry is List && firstDayEntry.isNotEmpty) {
        rawTime = firstDayEntry.first['startTime']?.toString() ?? '';
      }
    }

    return _formatTimeToAMPM(rawTime);
  }

  static String parseEndTime(dynamic value) {
    String rawTime = '';

    if (value is Map<String, dynamic> && value.isNotEmpty) {
      final firstDayEntry = value.values.first;
      if (firstDayEntry is List && firstDayEntry.isNotEmpty) {
        rawTime = firstDayEntry.first['endTime']?.toString() ?? '';
      }
    }

    return _formatTimeToAMPM(rawTime);
  }

  static String _formatTimeToAMPM(String timeStr) {
    if (timeStr.isEmpty) return '';

    try {
      final parts = timeStr.split(':');
      if (parts.length < 2) return '';

      int hour = int.parse(parts[0]);
      final minutes = parts[1].padLeft(2, '0');

      final period = hour >= 12 ? 'PM' : 'AM';
      if (hour > 12) hour -= 12;
      if (hour == 0) hour = 12; // Handle midnight (00:00)

      return '$hour:$minutes $period';
    } catch (e) {
      return '';
    }
  }

  static List<String> parseWeekDays(dynamic value) {
    final List<String> weekdays = [];
    const order = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    // Parse input
    if (value is String) {
      weekdays.addAll(value.split(',').map((e) => e.trim()));
    } else if (value is Map<String, dynamic>) {
      weekdays.addAll(
        value.keys
            .map((key) => key.capitalize().substring(0, 3))
            .where((day) => order.contains(day)),
      );
    }

    // Sort by weekday order
    weekdays.sort((a, b) => order.indexOf(a).compareTo(order.indexOf(b)));

    // Combine consecutive days
    return _combineWeekdays(weekdays, order);
  }

  static List<String> _combineWeekdays(List<String> days, List<String> order) {
    final combined = <String>[];
    String? currentStart;
    String? currentEnd;

    void addRange() {
      if (currentStart == currentEnd) {
        combined.add(currentStart!);
      } else {
        combined.add('$currentStart-$currentEnd');
      }
    }

    for (final day in days) {
      if (currentStart == null) {
        currentStart = day;
        currentEnd = day;
      } else {
        final currentIndex = order.indexOf(day);
        final prevIndex = order.indexOf(currentEnd!);

        if (currentIndex == prevIndex + 1) {
          currentEnd = day;
        } else {
          addRange();
          currentStart = day;
          currentEnd = day;
        }
      }
    }

    if (currentStart != null) addRange();
    return combined;
  }

  static dynamic readDetails(Map<dynamic, dynamic> json, _) =>
      json['voucherRedemptionTimeLimits'];
}

extension on String {
  String capitalize() {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
}
