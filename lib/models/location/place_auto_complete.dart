import 'package:vibeo/models/location/auto_complete_prediction.dart';

class PlaceAutoCompleteResponse {
  final String? status;
  final List<AutoCompletePrediction>? predictions;

  PlaceAutoCompleteResponse({this.status, this.predictions});

  factory PlaceAutoCompleteResponse.fromJson(Map<String, dynamic> json) {
    return PlaceAutoCompleteResponse(
      status: json['status'] as String?,
      predictions: json['predictions'] != null
          ? List<AutoCompletePrediction>.from(
              (json['predictions'] as List<Map<String, dynamic>>).map(
                AutoCompletePrediction.fromJson,
              ),
            )
          : null,
    );
  }
}
