import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/location/location.dart';
import 'package:vibeo/models/offer/offer_model.dart';

part 'venue_model.freezed.dart';
part 'venue_model.g.dart';

enum PerkVenueType {
  @JsonValue('Offers')
  offers,
  @JsonValue('Promoters')
  promoters,
  @JsonValue('Exclusive Offers')
  exclusiveOffers,
  @JsonValue('Events')
  events,
}

@freezed
class VenueModel with _$VenueModel {
  factory VenueModel({
    @Json<PERSON>ey(name: 'id') required String id,
    @Json<PERSON>ey(name: 'name') required String name,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'address') required String address,
    @Json<PERSON>ey(name: 'image_link') required String imageLink,
    @Json<PERSON>ey(name: 'gmap_link') required String gmapLink,
    @<PERSON>son<PERSON><PERSON>(name: 'location') required Location location,
    @Json<PERSON>ey(name: 'instagram_link') required String instagramLink,
    @Json<PERSON>ey(name: 'description') required String description,
    @Json<PERSON>ey(name: 'area') required String area,
    @Json<PERSON>ey(name: 'type') required String type,
    @Default('') @JsonKey(name: 'googlePlaceID') String? googlePlaceID,
    @Default([]) @JsonKey(name: 'thumbnails') List<String> thumbnails,
    @Default([]) @JsonKey(name: 'tags') List<String> tags,
    @Default(null) @JsonKey(includeFromJson: false) List<FeedModel>? feeds,
    @Default([])
    @JsonKey(name: 'perksType')
    List<PerkVenueType> perksType, // Updated to use PerkType enum
    @Default('') @JsonKey(name: 'latest_updated') String latestUpdated,
    @Default('') @JsonKey(name: 'phoneNumber') String phoneNumber,
    @Default(null) @JsonKey(name: 'milesAway') double? milesAway,
    @Default(false) @JsonKey(name: 'isLive') bool isLive,
    @Default(false) @JsonKey(name: 'exclusive') bool exclusive,
    @Default(null) @JsonKey(includeFromJson: false) List<OfferModel>? offers,
  }) = _VenueModel;

  factory VenueModel.fromJson(Map<String, dynamic> json) =>
      _$VenueModelFromJson(json);
}
