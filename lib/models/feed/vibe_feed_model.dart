import 'package:vibeo/models/feed/feed_model.dart';

class VibeFeedModel {
  final String venueName;
  final List<FeedModel> feeds;
  final bool isEndMarker;

  VibeFeedModel({
    required this.venueName,
    required this.feeds,
    this.isEndMarker = false,
  });

  factory VibeFeedModel.endMarker() {
    return VibeFeedModel(
      feeds: [],
      isEndMarker: true,
      venueName: '',
    );
  }

  factory VibeFeedModel.fromJson(Map<String, dynamic> json) {
    return VibeFeedModel(
      venueName: json['venueName'] as String,
      feeds: (json['feeds'] as List)
          .map((feed) => FeedModel.fromJson(feed as Map<String, dynamic>))
          .toList(),
      isEndMarker: (json['isEndMarker'] as bool?) ?? false,
    );
  }
}
