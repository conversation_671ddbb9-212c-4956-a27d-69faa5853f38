import 'dart:typed_data';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/models/models.dart';

part 'feed_model.freezed.dart';
part 'feed_model.g.dart';

enum PerksType { none, offer, promoter, exclusive, both, free }

@freezed
@JsonSerializable(explicitToJson: false)
class FeedModel with _$FeedModel {
  const FeedModel._();
  factory FeedModel({
    @Default(null)
    @Json<PERSON>ey(name: 'title', fromJson: _parseNullableString)
    String? title,
    @JsonKey(name: 'location', fromJson: _parseLocation) Location? location,
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'id', fromJson: _parseString) String id,
    @Default(null)
    @Json<PERSON>ey(name: 'googlePlaceID', fromJson: _parseString)
    String? googlePlaceID,
    @<PERSON>son<PERSON>ey(name: 'dateTime', fromJson: _parseDateTime) DateTime? dateTime,
    @JsonKey(name: 'music', fromJson: _parseNullableString) String? music,
    @JsonKey(name: 'artists', fromJson: _parseNullableString) String? artists,
    @JsonKey(name: 'genre', fromJson: _parseNullableString) String? genre,
    @Default(0) @JsonKey(name: 'likes', fromJson: _parseNumToInt) int likes,
    @Default('')
    @JsonKey(name: 'venueName', fromJson: _parseString)
    String venueName,
    @Default('') @JsonKey(name: 'area', fromJson: _parseString) String area,
    @Default('')
    @JsonKey(name: 'googleArea', fromJson: _parseString)
    String? googleArea,
    @Default('')
    @JsonKey(name: 'address', fromJson: _parseString)
    String address,
    @Default('')
    @JsonKey(name: 'videoURL', fromJson: _parseString)
    String videoURL,
    @Default('')
    @JsonKey(name: 'thumbnailURL', fromJson: _parseString)
    String thumbnailURL,
    @JsonKey(name: 'coverCharge', fromJson: _parseNullableString)
    String? coverCharge,
    @Default('') @JsonKey(name: 'userID', fromJson: _parseString) String userID,
    @Default(0) @JsonKey(name: 'views', fromJson: _parseNumToInt) int views,
    @JsonKey(name: 'usp', fromJson: _parseNullableString) String? usp,
    @Default(false)
    @JsonKey(name: 'liveMusic', fromJson: _parseBool)
    bool liveMusic,
    @JsonKey(name: 'venueID', fromJson: _parseNullableString) String? venueID,
    @Default(3)
    @JsonKey(name: 'vibeScore', fromJson: _parseVibeScoreToInt)
    int vibeScore,
    @JsonKey(name: 'venueType', fromJson: _parseNullableString)
    String? venueType,
    @Default(null) @JsonKey(name: 'milesAway') double? milesAway,
    @Default(null)
    @JsonKey(name: 'perksType', fromJson: _parsePerks)
    PerksType? perksType,
    @JsonKey(name: 'data', fromJson: _parseNullableString) String? data,
    @JsonKey(name: 'description', fromJson: _parseNullableString)
    String? description,
    @JsonKey(
      includeFromJson: false,
      includeToJson: false,
    )
    @Default([])
    List<Uint8List> thumbnails,
    @JsonKey(
      includeFromJson: false,
      includeToJson: false,
    )
    @Default([])
    List<OfferModel> offers,
    @JsonKey(
      includeFromJson: false,
      includeToJson: false,
    )
    @Default([])
    List<PromoterModel> promoters,
    @JsonKey(
      includeFromJson: false,
      includeToJson: false,
    )
    @Default(false)
    bool? isLive,
    @JsonKey(
      includeFromJson: false,
      includeToJson: false,
    )
    @Default(false)
    bool? shazamError,
  }) = _FeedModel;

  factory FeedModel.fromJson(Map<String, dynamic> json) {
    final model = _$FeedModelFromJson(json);
    return model.copyWith(
      isLive: isLiveTag(model.dateTime!),
    );
  }

  Map<String, dynamic> toJson() => _$FeedModelToJson(this);
}

String _parseString(dynamic value) {
  if (value == null || value.toString().toLowerCase() == 'null') return '';
  return value.toString();
}

PerksType _parsePerks(dynamic value) {
  if (value == null || value.toString().toLowerCase() == 'null') {
    return PerksType.none;
  }
  if (value is PerksType) return value;
  if (value is String) {
    switch (value.toLowerCase()) {
      case 'exclusive':
        return PerksType.exclusive;
      case 'offer':
        return PerksType.offer;
      case 'promote':
        return PerksType.promoter;
      case 'both':
        return PerksType.both;
      default:
        return PerksType.none;
    }
  }
  return PerksType.none;
}

String? _parseNullableString(dynamic value) {
  if (value == null || value.toString().toLowerCase() == 'null') return null;
  return value.toString();
}

Location? _parseLocation(Map<String, dynamic>? json) {
  if (json == null) return Location(latitude: null, longitude: null);

  double? parseToDouble(dynamic value) {
    if (value == null || value.toString().toLowerCase() == 'null') return 0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0;
    return 0;
  }

  return Location(
    latitude: parseToDouble(json['latitude']),
    longitude: parseToDouble(json['longitude']),
  );
}

DateTime? _parseDateTime(String? date) {
  if (date == null || date.toLowerCase() == 'null') return null;
  return DateTime.tryParse(date);
}

bool _parseBool(dynamic value) {
  if (value == null || value.toString().toLowerCase() == 'null') return false;
  if (value is bool) return value;
  if (value is String) return value.toLowerCase() == 'true';
  return false;
}

int _parseNumToInt(dynamic value) {
  if (value == null || value.toString().toLowerCase() == 'null') return 0;
  if (value is int) return value;
  if (value is Map) return (value['low'] as num?)?.toInt() ?? 0;
  if (value is String) return int.tryParse(value) ?? 0;
  if (value is double) return value.toInt();
  return 0;
}

int _parseVibeScoreToInt(dynamic value) {
  if (value == null || value.toString().toLowerCase() == 'null') return 3;
  if (value is int) return value;
  if (value is Map) return (value['low'] as num?)?.toInt() ?? 3;
  if (value is String) return int.tryParse(value) ?? 3;
  if (value is double) return value.toInt();
  return 3;
}
