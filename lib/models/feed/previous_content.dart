// models/previous_content.dart
class PreviousContent {
  final String? venueName;
  final String? area;
  final String? address;
  final String? venueType;
  final double vibeScore;
  final String? usp;
  final String? coverCharge;
  final bool liveMusic;
  final String? selectedPlaceID;
  final String? selectedGoogleArea;
  final Map<String, dynamic>? venueLocation;

  PreviousContent({
    this.venueName,
    this.area,
    this.address,
    this.venueType,
    this.vibeScore = 3.0,
    this.usp,
    this.coverCharge,
    this.liveMusic = false,
    this.venueLocation,
    this.selectedPlaceID,
    this.selectedGoogleArea,
  });

  Map<String, dynamic> toJson() => {
        'venueType': venueType,
        'area': area,
        'vibeScore': vibeScore,
        'usp': usp!.isNotEmpty ? usp : null,
        'coverCharge': coverCharge,
        'venueName': venueName,
        'address': address,
        'liveMusic': liveMusic,
        'venueLocation': venueLocation,
        'selectedPlaceID': selectedPlaceID,
        'selectedGoogleArea': selectedGoogleArea,
      }..removeWhere((_, value) => value == null);

  factory PreviousContent.fromJson(Map<String, dynamic> json) {
    return PreviousContent(
      venueType: json['venueType']?.toString(),
      area: json['area']?.toString(),
      vibeScore: (json['vibeScore'] as num?)?.toDouble() ?? 3.0,
      usp: json['usp']?.toString(),
      coverCharge: json['coverCharge']?.toString(),
      venueName: json['venueName']?.toString(),
      address: json['address']?.toString(),
      liveMusic: (json['liveMusic'] as bool?) ?? false,
      venueLocation: json['venueLocation'] is Map
          ? Map<String, dynamic>.from(json['venueLocation'] as Map)
          : null,
      selectedPlaceID: json['selectedPlaceID']?.toString(),
      selectedGoogleArea: json['selectedGoogleArea']?.toString(),
    );
  }
}
