import 'package:freezed_annotation/freezed_annotation.dart';

part 'redemption_confirmation.freezed.dart';
part 'redemption_confirmation.g.dart';

@freezed
class RedemptionConfirmation with _$RedemptionConfirmation {
  factory RedemptionConfirmation({
    required String transactionConfirmationTime,
    required String redemptionId,
    required String redemptionDate,
    required int quantity,
    required double price,
    required String userEmail,
    required String title,
    required String venueName,
  }) = _RedemptionConfirmation;

  factory RedemptionConfirmation.fromJson(Map<String, dynamic> json) =>
      _$RedemptionConfirmationFromJson(json);
}
