import 'package:freezed_annotation/freezed_annotation.dart';

part 'redemption_response.freezed.dart';
part 'redemption_response.g.dart';

@freezed
class RedemptionResponse with _$RedemptionResponse {
  factory RedemptionResponse({
    required String uniqueCode,
    required String status,
  }) = _RedemptionResponse;

  factory RedemptionResponse.fromJson(Map<String, dynamic> json) =>
      _$RedemptionResponseFromJson(json);
}
