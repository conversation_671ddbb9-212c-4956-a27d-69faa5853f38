import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/offer/redeemed_offer_data.dart';

/// Model for the redeemed orders API response
class RedeemedOrdersResponse {
  final int statusCode;
  final String message;
  final List<RedeemedVenueOrders> data;

  RedeemedOrdersResponse({
    required this.statusCode,
    required this.message,
    required this.data,
  });

  factory RedeemedOrdersResponse.fromJson(Map<String, dynamic> json) {
    return RedeemedOrdersResponse(
      statusCode: json['statusCode'] as int? ?? 0,
      message: json['message'] as String? ?? '',
      data: (json['data'] as List?)
              ?.map(
                (e) => RedeemedVenueOrders.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

/// Model for venue orders in the API response
class RedeemedVenueOrders {
  final String venueId;
  final String venueName;
  final List<OrderData> orders;

  RedeemedVenueOrders({
    required this.venueId,
    required this.venueName,
    required this.orders,
  });

  factory RedeemedVenueOrders.fromJson(Map<String, dynamic> json) {
    return RedeemedVenueOrders(
      venueId: json['venueId'] as String? ?? '',
      venueName: json['venueName'] as String? ?? '',
      orders: (json['orders'] as List?)
              ?.map(
                (e) => OrderData.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }

  /// Convert to VenueRedeemedOffers for UI display
  VenueRedeemedOffers toVenueRedeemedOffers() {
    final List<RedeemedOfferData> redeemedOffers = [];
    String latestDate = '';

    // Process all orders for this venue
    for (final order in orders) {
      for (final item in order.items) {
        // Create an OfferModel from the item data
        final offerModel = OfferModel(
          id: order.orderId,
          venueID: venueId,
          venueName: venueName,
          title: item.title,
          description: item.description,
          imageLink: item.imageLink,
          isRedeemedToday: true,
          redemptionType: OfferRedemptionType.regular,
        );

        // Create RedeemedOfferData
        final redeemedOffer = RedeemedOfferData(
          offer: offerModel,
          // VIBEPOINTS FEATURE COMMENTED OUT
          // vibePointsEarned: 75, // Default value
          redemptionDate: item.redeemedDate,
          redemptionCode: order.orderId,
        );

        redeemedOffers.add(redeemedOffer);

        // Track the latest redemption date
        if (latestDate.isEmpty || item.redeemedDate.compareTo(latestDate) > 0) {
          latestDate = item.redeemedDate;
        }
      }
    }

    return VenueRedeemedOffers(
      venueId: venueId,
      venueName: venueName,
      offers: redeemedOffers,
      latestRedemptionDate: latestDate,
    );
  }
}

/// Model for order data in the API response
class OrderData {
  final String orderId;
  final List<OrderItem> items;

  OrderData({
    required this.orderId,
    required this.items,
  });

  factory OrderData.fromJson(Map<String, dynamic> json) {
    return OrderData(
      orderId: json['orderId'] as String? ?? '',
      items: (json['items'] as List?)
              ?.map(
                (e) => OrderItem.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );
  }
}

/// Model for order items in the API response
class OrderItem {
  final String title;
  final String description;
  final String redeemedDate;
  final String imageLink;

  OrderItem({
    required this.title,
    required this.description,
    required this.redeemedDate,
    required this.imageLink,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      redeemedDate: json['redeemedDate'] as String? ?? '',
      imageLink: json['imageLink'] as String? ?? '',
    );
  }
}
