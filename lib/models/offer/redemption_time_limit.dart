import 'package:vibeo/models/utils/parse_utils.dart';

/// Class to represent the time limit for redemption
class RedemptionTimeLimit {
  final int limitId;
  final String dayOfWeek;
  final int maxRedemptions;
  final String startTime;
  final String endTime;

  RedemptionTimeLimit({
    required this.limitId,
    required this.dayOfWeek,
    required this.maxRedemptions,
    required this.startTime,
    required this.endTime,
  });

  factory RedemptionTimeLimit.fromJson(Map<String, dynamic> json) {
    return RedemptionTimeLimit(
      limitId: ParseUtils.parseNumToInt(json['limitId']),
      dayOfWeek: json['dayOfWeek'] as String,
      maxRedemptions: ParseUtils.parseNumToInt(json['maxRedemptions']),
      startTime: json['startTime'] as String,
      endTime: json['endTime'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'limitId': limitId,
      'dayOfWeek': dayOfWeek,
      'maxRedemptions': maxRedemptions,
      'startTime': startTime,
      'endTime': endTime,
    };
  }
}
