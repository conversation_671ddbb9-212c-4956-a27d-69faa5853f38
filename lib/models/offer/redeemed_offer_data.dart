import 'package:vibeo/models/offer/offer_model.dart';

/// Class to hold redeemed offer data with additional metadata
class RedeemedOfferData {
  final OfferModel offer;
  // VIBEPOINTS FEATURE COMMENTED OUT
  final int vibePointsEarned = 0; // Default to 0
  final String redemptionDate;
  final String redemptionCode;

  RedeemedOfferData({
    required this.offer,
    // VIBEPOINTS FEATURE COMMENTED OUT
    // required this.vibePointsEarned,
    required this.redemptionDate,
    required this.redemptionCode,
  });
}

/// Class to hold grouped redeemed offers by venue
class VenueRedeemedOffers {
  final String venueId;
  final String venueName;
  List<RedeemedOfferData> offers;
  String latestRedemptionDate;

  /// Calculate total vibe points earned for this venue
  // VIBEPOINTS FEATURE COMMENTED OUT
  int get totalVibePointsEarned => 0; // Default to 0
  // offers.fold(0, (sum, offer) => sum + offer.vibePointsEarned);

  VenueRedeemedOffers({
    required this.venueId,
    required this.venueName,
    required this.offers,
    required this.latestRedemptionDate,
  });
}
