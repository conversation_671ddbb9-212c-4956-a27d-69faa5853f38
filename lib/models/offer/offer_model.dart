import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vibeo/models/utils/parse_utils.dart';

part 'offer_model.freezed.dart';
part 'offer_model.g.dart';

enum OfferRedemptionType {
  regular, // Regular offer (just for info)
  freeRedeem, // Free redeem offer
  // VIBEPOINTS FEATURE COMMENTED OUT
  // vibePointsRedeem, // Redeem using vibepoints
  // vibePointsUnlock, // Needs vibepoints to unlock
  referUnlock, // Refer & unlock
}

enum VoucherType {
  REGULAR,
  UNPAID_REDEEMABLE,
  PAID_REDEEMABLE,
}

enum VoucherCategory {
  TIMEBASED,
  DROPS,
  ONGOING,
  LIMITED,
}

enum DayOfWeek {
  MONDAY,
  TUESDAY,
  WEDNESDAY,
  THURSDAY,
  FRIDAY,
  SATURDAY,
  SUNDAY,
}

@freezed
class OfferModel with _$OfferModel {
  factory OfferModel({
    @Default(0)
    @JsonKey(name: 'voucherId', fromJson: ParseUtils.parseNumToInt)
    int voucherId,
    @Default('') @JsonKey(name: 'uniqueCode') String id,
    @Default('') @JsonKey(name: 'b2cId') String venueID,
    @Default('') @JsonKey(name: 'venueName') String venueName,
    @Default('') @JsonKey(name: 'description') String description,
    @Default(false) @JsonKey(name: 'isRedeemedToday') bool isRedeemedToday,
    @Default(false) @JsonKey(name: 'redeemable') bool canRedeem,
    @Default(false) @JsonKey(name: 'locked') bool isLocked,
    @Default(1)
    @JsonKey(name: 'priority', fromJson: ParseUtils.parseNumToInt)
    int priority,
    @Default('') @JsonKey(name: 'title') String title,
    @Default('') @JsonKey(name: 'imageLink') String imageLink,
    @Default(true)
    @JsonKey(name: 'status', fromJson: ParseUtils.parseTextToBool)
    bool toShow,
    @Default(false) @JsonKey(name: 'showCountdown') bool showCountdown,
    @Default('') @JsonKey(name: 'startDate') String startDate,
    @Default('') @JsonKey(name: 'endDate') String endDate,
    @Default(null)
    @JsonKey(name: 'externalLink', fromJson: ParseUtils.parseText)
    String? externalLink,
    @Default(null)
    @JsonKey(name: 'price', fromJson: ParseUtils.parseNumToDouble)
    double? discountedValue,
    @Default(null)
    @JsonKey(name: 'valuePrice', fromJson: ParseUtils.parseNumToDouble)
    double? offerValue,
    @Default(null) @JsonKey(name: 'info') String? info,
    @Default(false) @JsonKey(name: 'exclusive') bool exclusive,
    // Voucher type and category
    @Default(VoucherType.REGULAR)
    @JsonKey(name: 'voucherType', fromJson: ParseUtils.parseVoucherType)
    VoucherType voucherType,
    @Default(VoucherCategory.ONGOING)
    @JsonKey(name: 'voucherCategory', fromJson: ParseUtils.parseVoucherCategory)
    VoucherCategory voucherCategory,
    // Redemption fields
    @Default(OfferRedemptionType.regular)
    @JsonKey(name: 'redemptionType')
    OfferRedemptionType redemptionType,
    // VIBEPOINTS FEATURE COMMENTED OUT
    @Default(0)
    @JsonKey(name: 'vibePointsCost', fromJson: ParseUtils.parseNumToInt)
    int vibePointsCost,
    @Default(0)
    @JsonKey(name: 'requiredReferrals', fromJson: ParseUtils.parseNumToInt)
    int requiredReferrals,
    // Time limits
    @Default('')
    @JsonKey(
      name: 'startTime',
      readValue: ParseUtils.readDetails,
      fromJson: ParseUtils.parseStartTime,
    )
    String startTime,
    @Default('')
    @JsonKey(
      name: 'endTime',
      readValue: ParseUtils.readDetails,
      fromJson: ParseUtils.parseEndTime,
    )
    String endTime,
    @Default([])
    @JsonKey(
      name: 'weekdays',
      readValue: ParseUtils.readDetails,
      fromJson: ParseUtils.parseWeekDays,
    )
    List<String> weekdays,
    @JsonKey(name: 'voucherRedemptionTimeLimits', includeToJson: false)
    Map<String, dynamic>? redemptionTimeLimits,
  }) = _OfferModel;

  factory OfferModel.fromJson(Map<String, dynamic> json) =>
      _$OfferModelFromJson(json);
}
