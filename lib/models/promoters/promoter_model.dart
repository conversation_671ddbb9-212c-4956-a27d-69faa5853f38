import 'package:freezed_annotation/freezed_annotation.dart';

part 'promoter_model.freezed.dart';
part 'promoter_model.g.dart';

@freezed
class PromoterModel with _$PromoterModel {
  factory PromoterModel({
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'id') String id,
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'title') String title,
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'subTitle') String subTitle,
    @Default('') @<PERSON>son<PERSON><PERSON>(name: 'description') String description,
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'image') String image,
    @Default('') @<PERSON><PERSON><PERSON><PERSON>(name: 'socialHandle') String socialHandle,
    @Default(true) @<PERSON><PERSON><PERSON><PERSON>(name: 'toShow') bool toShow,
  }) = _PromoterModel;

  factory PromoterModel.fromJson(Map<String, dynamic> json) =>
      _$PromoterModelFromJson(json);
}
