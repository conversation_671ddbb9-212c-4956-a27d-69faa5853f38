import 'package:vibeo/models/feed/feed_model.dart';

class BeoResponse {
  final Data? data;
  final String? error;

  BeoResponse({
    this.data,
    this.error,
  });

  factory BeoResponse.fromJson(Map<String, dynamic> json) {
    if (json['message'] != null) {
      return BeoResponse(error: json['message'].toString());
    }

    try {
      return BeoResponse(
        data: Data.fromJson(json),
      );
    } catch (e) {
      return BeoResponse(error: 'Failed to parse response');
    }
  }

  @override
  String toString() {
    return error ?? data?.overallSummary ?? '';
  }
}

class Data {
  final String overallSummary;
  final List<Feed?> feeds;
  final bool? setHomePage;
  final String? tag;

  Data({
    required this.overallSummary,
    required this.feeds,
    this.setHomePage,
    this.tag,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    List<Feed?> feedsList = [];

    if (json['feeds'] is List) {
      feedsList = (json['feeds'] as List)
          .map(
            (feed) => feed is Map<String, dynamic> ? Feed.fromJson(feed) : null,
          )
          .where((feed) => feed != null)
          .toList();
    }

    return Data(
      overallSummary: json['overall_summary']?.toString() ?? '',
      setHomePage: json['set_homepage'] as bool? ?? false,
      tag: json['tag']?.toString() ?? '',
      feeds: feedsList,
    );
  }
}

class Feed {
  final String summary;
  final FeedModel feedModel;
  final String feedID;

  Feed({
    required this.summary,
    required this.feedModel,
    required this.feedID,
  });

  static Feed? fromJson(Map<String, dynamic> json) {
    if (json['feedData'] == null ||
        json['feedData'] is! Map ||
        (json['feedData'] as Map).isEmpty) {
      return null;
    }

    return Feed(
      summary: json['summary']?.toString() ?? '',
      feedModel: FeedModel.fromJson(json['feedData'] as Map<String, dynamic>),
      feedID: json['feedID']?.toString() ?? '',
    );
  }
}
