import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vibeo/models/models.dart';

part 'artist_model.freezed.dart';
part 'artist_model.g.dart';

@freezed
class ArtistModel with _$ArtistModel {
  factory ArtistModel({
    @Default('') @<PERSON><PERSON><PERSON>ey(name: 'photoURL') String photoURL,
    @Default([]) @Json<PERSON>ey(name: 'tags') List<String> tags,
    @Default(false) @<PERSON>sonKey(name: 'dj') bool dj,
    @Default(false) @<PERSON>son<PERSON>ey(name: 'contentCreator') bool contentCreator,
    @Default('') @<PERSON>son<PERSON><PERSON>(name: 'uid') String uid,
    @Default('') @<PERSON>son<PERSON><PERSON>(name: 'fullName') String fullName,
    @Default('') @<PERSON>son<PERSON><PERSON>(name: 'gender') String gender,
    @<PERSON>son<PERSON>ey(name: 'feeds') List<FeedModel>? feeds,
  }) = _ArtistModel;

  factory ArtistModel.fromJson(Map<String, dynamic> json) =>
      _$ArtistModelFromJson(json);
}
