import 'package:flutter/material.dart';

class SizeUtils {
  static late MediaQueryData _mediaQueryData;
  static late double screenWidth;
  static late double screenHeight;
  static late double blockSizeHorizontal;
  static late double blockSizeVertical;
  static late EdgeInsets horizontalPadding;
  static late EdgeInsets exploreHorizontalPadding;
  static late double listBottomPadding;

  static void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;
    horizontalPadding = const EdgeInsets.symmetric(horizontal: 16);
    exploreHorizontalPadding = const EdgeInsets.symmetric(horizontal: 16);
    listBottomPadding = 150;
  }
}
