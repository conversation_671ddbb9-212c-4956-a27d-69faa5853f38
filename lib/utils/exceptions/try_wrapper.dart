import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/logic/exception/exception_bloc.dart';
import 'package:vibeo/logic/exception/exception_event.dart';

Future<T?> tryWrapperAsync<T>(
  Future<T> Function() function,
  BuildContext context, {
  void Function(Object error)? onError,
  T? defaultValue,
}) async {
  try {
    return await function();
  } catch (error, stackTrace) {
    String errorMessage;

    // Handle specific exceptions
    if (error is NetworkException) {
      errorMessage = error.message;
    } else if (error is TimeoutException) {
      errorMessage = error.message;
    } else if (error is ServerException) {
      errorMessage = error.message;
    } else if (error is UnauthorizedException) {
      errorMessage = error.message;
    } else if (error is BadRequestException) {
      errorMessage = error.message;
    } else if (error is NotFoundException) {
      errorMessage = error.message;
    } else if (error is ValidationException) {
      errorMessage = error.message;
    } else if (error is DatabaseException) {
      errorMessage = error.message;
    } else if (error is BusinessLogicException) {
      errorMessage = error.message;
    } else if (error is AuthenticationException) {
      errorMessage = error.message;
    } else if (error is DriveException) {
      errorMessage = error.message;
    } else {
      // For any other unhandled exceptions
      errorMessage = error.toString();
    }
    BlocProvider.of<GlobalErrorBloc>(context).add(
      ShowErrorEvent(
        errorMessage,
        [
          error,
          stackTrace,
        ],
      ),
    );

    if (onError != null) {
      onError(error);
    }
    if (defaultValue != null) {
      return defaultValue;
    }
    return null;
  }
}

extension TryWrapper<T> on Future<T> Function() {
  Future<T?> withTry(
    BuildContext context, {
    void Function(Object error)? onError,
    T? defaultValue,
  }) {
    return tryWrapperAsync(
      this,
      context,
      onError: onError,
      defaultValue: defaultValue,
    );
  }
}
