abstract class AppException implements Exception {
  final String message;
  final String? prefix;
  final String? stackTrace;

  AppException(this.message, {this.prefix, this.stackTrace});

  @override
  String toString() {
    return prefix != null ? '$prefix: $message' : message;
  }
}

// Network Related Exceptions
class NetworkException extends AppException {
  NetworkException({
    String message = 'No Internet Connection',
    String? stackTrace,
  }) : super(message, prefix: 'Network Error', stackTrace: stackTrace);
}

class TimeoutException extends AppException {
  TimeoutException({
    String message = 'Operation Timed Out',
    String? stackTrace,
  }) : super(message, prefix: 'Timeout Error', stackTrace: stackTrace);
}

// Server Related Exceptions
class ServerException extends AppException {
  ServerException({
    String message = 'Server Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Server Error', stackTrace: stackTrace);
}

class ServiceUnavailableException extends AppException {
  ServiceUnavailableException({
    String message = 'Service Temporarily Unavailable',
    String? stackTrace,
  }) : super(message, prefix: 'Service Error', stackTrace: stackTrace);
}

// Authentication Related Exceptions
class UnauthorizedException extends AppException {
  UnauthorizedException({
    String message = 'Unauthorized Request',
    String? stackTrace,
  }) : super(message, prefix: 'Authorization Error', stackTrace: stackTrace);
}

class TokenExpiredException extends AppException {
  TokenExpiredException({
    String message = 'Session Expired, Please Login Again',
    String? stackTrace,
  }) : super(message, prefix: 'Token Error', stackTrace: stackTrace);
}

// API Related Exceptions
class BadRequestException extends AppException {
  BadRequestException({
    String message = 'Invalid Request',
    String? stackTrace,
  }) : super(message, prefix: 'Bad Request', stackTrace: stackTrace);
}

class NotFoundException extends AppException {
  NotFoundException({
    String message = 'Requested Resource Not Found',
    String? stackTrace,
  }) : super(message, prefix: 'Not Found', stackTrace: stackTrace);
}

class ConflictException extends AppException {
  ConflictException({
    String message = 'Conflict Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Conflict Error', stackTrace: stackTrace);
}

class TooManyRequestsException extends AppException {
  TooManyRequestsException({
    String message = 'Too Many Requests',
    String? stackTrace,
  }) : super(message, prefix: 'Rate Limit Error', stackTrace: stackTrace);
}

// Data Related Exceptions
class DataParsingException extends AppException {
  DataParsingException({
    String message = 'Error Occurred While Parsing Data',
    String? stackTrace,
  }) : super(message, prefix: 'Parsing Error', stackTrace: stackTrace);
}

class ValidationException extends AppException {
  ValidationException({
    String message = 'Validation Failed',
    String? stackTrace,
  }) : super(message, prefix: 'Validation Error', stackTrace: stackTrace);
}

class CacheException extends AppException {
  CacheException({
    String message = 'Cache Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Cache Error', stackTrace: stackTrace);
}

// File Related Exceptions
class FileNotFoundException extends AppException {
  FileNotFoundException({
    String message = 'File Not Found',
    String? stackTrace,
  }) : super(message, prefix: 'File Error', stackTrace: stackTrace);
}

class FileOperationException extends AppException {
  FileOperationException({
    String message = 'Error During File Operation',
    String? stackTrace,
  }) : super(message, prefix: 'File Operation Error', stackTrace: stackTrace);
}

// Permission Related Exceptions
class PermissionDeniedAppException extends AppException {
  PermissionDeniedAppException({
    String message = 'Permission Denied',
    String? stackTrace,
  }) : super(message, prefix: 'Permission Error', stackTrace: stackTrace);
}

// Database Related Exceptions
class DatabaseException extends AppException {
  DatabaseException({
    String message = 'Database Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Database Error', stackTrace: stackTrace);
}

// State Related Exceptions
class StateException extends AppException {
  StateException({
    String message = 'Invalid State Operation',
    String? stackTrace,
  }) : super(message, prefix: 'State Error', stackTrace: stackTrace);
}

// Platform Related Exceptions
class PlatformException extends AppException {
  PlatformException({
    String message = 'Platform Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Platform Error', stackTrace: stackTrace);
}

// Custom Business Logic Exceptions
class BusinessLogicException extends AppException {
  BusinessLogicException({
    String message = 'Business Logic Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Business Error', stackTrace: stackTrace);
}

class UploadException extends AppException {
  UploadException({
    String message = 'Upload Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Upload Error', stackTrace: stackTrace);
}

class AuthenticationException extends AppException {
  AuthenticationException({
    String message = 'Authentication Logic Error Occurred',
    String? stackTrace,
  }) : super(message, prefix: 'Authentication Error', stackTrace: stackTrace);
}

class InvalidCredentialsException extends AppException {
  InvalidCredentialsException({
    String message = 'Invalid email or password',
    String? stackTrace,
  }) : super(message, prefix: 'Credentials Error', stackTrace: stackTrace);
}

class UserNotFoundException extends AppException {
  UserNotFoundException({
    String message = 'User not found',
    String? stackTrace,
  }) : super(message, prefix: 'User Error', stackTrace: stackTrace);
}

class EmailAlreadyInUseException extends AppException {
  EmailAlreadyInUseException({
    String message = 'Email is already registered',
    String? stackTrace,
  }) : super(message, prefix: 'Email Error', stackTrace: stackTrace);
}

class InvalidEmailException extends AppException {
  InvalidEmailException({
    String message = 'Invalid email format',
    String? stackTrace,
  }) : super(message, prefix: 'Email Error', stackTrace: stackTrace);
}

class UserDisabledException extends AppException {
  UserDisabledException({
    String message = 'User account has been disabled',
    String? stackTrace,
  }) : super(message, prefix: 'Account Error', stackTrace: stackTrace);
}

class SessionExpiredException extends AppException {
  SessionExpiredException({
    String message = 'Session has expired, please login again',
    String? stackTrace,
  }) : super(message, prefix: 'Session Error', stackTrace: stackTrace);
}

class GoogleSignInCancelledException extends AppException {
  GoogleSignInCancelledException({
    String message = 'Google sign in was cancelled',
    String? stackTrace,
  }) : super(message, prefix: 'Google Sign In', stackTrace: stackTrace);
}

class FeedVenueNotSelected extends AppException {
  FeedVenueNotSelected({
    String message = 'Please select a venue',
    String? stackTrace,
  }) : super(message, prefix: 'Venue error', stackTrace: stackTrace);
}

class DriveException extends AppException {
  DriveException({
    String message = 'Something went wrong',
    String? stackTrace,
  }) : super(message, prefix: 'App error', stackTrace: stackTrace);
}
