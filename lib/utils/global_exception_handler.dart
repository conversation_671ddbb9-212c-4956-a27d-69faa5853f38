import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/exception/exception_bloc.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

class GlobalExceptionHandler extends StatelessWidget {
  final Widget child;

  const GlobalExceptionHandler({
    required this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<GlobalErrorBloc, GlobalErrorState>(
      listenWhen: (previous, current) => previous != current,
      listener: (context, state) {
        if (state is GlobalErrorOccurred) {
          // Use ScaffoldMessenger from MaterialApp's context
          ScaffoldMessenger.of(context).clearSnackBars();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                state.message,
                style: AppTextStyles.bodyBold,
              ),
              backgroundColor: darkAppColors.errorColor,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      },
      child: child,
    );
  }
}
