import 'package:geolocator/geolocator.dart';

Future<void> handleLocationPermission() async {
  try {
    // First check if location services are enabled
    final bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return;
    }

    // Use a single permission check and request flow
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return;
    }
  } catch (e) {
    if (e is PermissionRequestInProgressException) {
      // Wait for the existing permission request to complete
      await Future.delayed(const Duration(milliseconds: 500));
    }
    rethrow;
  }
}

Future<Position?> getCurrentPosition() async {
  try {
    await handleLocationPermission();

    if (!await Geolocator.isLocationServiceEnabled()) {
      return null;
    }

    return await Geolocator.getCurrentPosition(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.best,
        distanceFilter: 10,
      ),
    );
  } catch (e) {
    return null;
  }
}

Future<bool> isLocationPermissionGranted() async {
  try {
    final permission = await Geolocator.checkPermission();
    final serviceEnabled = await Geolocator.isLocationServiceEnabled();

    return serviceEnabled &&
        (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always);
  } catch (e) {
    return false;
  }
}
