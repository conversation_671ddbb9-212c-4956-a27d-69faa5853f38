// validators.dart
import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

void showErrorSnackBar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(
        message,
        style: AppTextStyles.bodyBold,
      ),
      backgroundColor: darkAppColors.errorColor,
      duration: const Duration(seconds: 3),
    ),
  );
}

class Validators {
  static bool validatePhoneNumber(BuildContext context, String phone) {
    // Check if empty
    if (phone.isEmpty) {
      showErrorSnackBar(context, 'Phone number cannot be empty');
      return false;
    }

    // Check length
    if (phone.length != 10) {
      showErrorSnackBar(context, 'Phone number must be 10 digits');
      return false;
    }

    // Check if contains only numbers
    if (!RegExp(r'^[0-9]+$').hasMatch(phone)) {
      showErrorSnackBar(context, 'Phone number can only contain digits');
      return false;
    }

    return true;
  }

  static bool validateGenresList(BuildContext context, Set<String> genres) {
    if (genres.isEmpty || genres.length < 3) {
      showErrorSnackBar(context, 'Please select at least 3 genres');
    }

    return true;
  }

  static bool validateOTP(BuildContext context, String otp) {
    // Check if empty
    if (otp.isEmpty) {
      showErrorSnackBar(context, 'OTP cannot be empty');
      return false;
    }

    // Check length (assuming 6 digit OTP)
    if (otp.length != 6) {
      showErrorSnackBar(context, 'OTP must be 6 digits');
      return false;
    }

    // Check if contains only numbers
    if (!RegExp(r'^[0-9]+$').hasMatch(otp)) {
      showErrorSnackBar(context, 'OTP can only contain digits');
      return false;
    }

    return true;
  }

  static bool validateFirstName(BuildContext context, String name) {
    // Check if empty
    if (name.isEmpty) {
      showErrorSnackBar(context, 'First name cannot be empty');
      return false;
    }

    // Check minimum length
    if (name.length < 2) {
      showErrorSnackBar(context, 'First name must be at least 2 characters');
      return false;
    }

    // Check maximum length
    if (name.length > 50) {
      showErrorSnackBar(context, 'First name cannot exceed 50 characters');
      return false;
    }

    // Check if contains only letters and spaces
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(name)) {
      showErrorSnackBar(context, 'First name can only contain letters');
      return false;
    }

    return true;
  }

  static bool validateWheelDateOfBirth(
    BuildContext context, {
    required int month,
    required int day,
    required int year,
  }) {
    try {
      final DateTime dateOfBirth = DateTime(year, month + 1, day);
      final DateTime now = DateTime.now();

      // Check if date is in the future
      if (dateOfBirth.isAfter(now)) {
        showErrorSnackBar(context, 'Date of birth cannot be in the future');
        return false;
      }

      // Calculate age
      final Duration difference = now.difference(dateOfBirth);
      final int age = (difference.inDays / 365).floor();

      // Check if age is less than 120 years
      if (age > 120) {
        showErrorSnackBar(context, 'Invalid date of birth');
        return false;
      }

      // Check if age is at least 21 years
      if (age < 21) {
        showErrorSnackBar(context, 'You must be at least 21 years old');
        return false;
      }

      // Validate day for specific months
      if (!_isValidDayForMonth(day, month + 1, year)) {
        showErrorSnackBar(context, 'Invalid day for selected month');
        return false;
      }

      return true;
    } catch (e) {
      showErrorSnackBar(context, 'Invalid date selection');
      return false;
    }
  }

  static bool _isValidDayForMonth(int day, int month, int year) {
    final daysInMonth = DateTime(year, month + 1, 0).day;
    return day <= daysInMonth;
  }
}
