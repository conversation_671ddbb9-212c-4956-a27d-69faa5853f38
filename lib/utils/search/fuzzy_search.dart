import 'dart:math';

/// A utility class for fuzzy string matching
class FuzzySearch {
  /// Calculate the Levenshtein distance between two strings
  /// This measures how many single-character edits are needed to change one string into another
  static int levenshteinDistance(String s1, String s2) {
    s1 = s1.toLowerCase();
    s2 = s2.toLowerCase();

    // Create a table to store results of subproblems
    final List<List<int>> dp = List.generate(
      s1.length + 1,
      (i) => List.generate(s2.length + 1, (j) => 0),
    );

    // Fill the table
    for (int i = 0; i <= s1.length; i++) {
      for (int j = 0; j <= s2.length; j++) {
        // If first string is empty, insert all characters of second string
        if (i == 0) {
          dp[i][j] = j;
        }
        // If second string is empty, remove all characters of first string
        else if (j == 0) {
          dp[i][j] = i;
        }
        // If last characters are the same, ignore the last char and recur for remaining
        else if (s1[i - 1] == s2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1];
        }
        // If last characters are different, consider all possibilities
        else {
          dp[i][j] = 1 +
              min(
                min(dp[i][j - 1], dp[i - 1][j]), // Insert, Remove
                dp[i - 1][j - 1], // Replace
              );
        }
      }
    }

    return dp[s1.length][s2.length];
  }

  /// Calculate similarity score between two strings (0-100)
  /// Higher score means more similar
  static double similarityScore(String s1, String s2) {
    if (s1.isEmpty && s2.isEmpty) return 100;
    if (s1.isEmpty || s2.isEmpty) return 0;

    final int maxLength = max(s1.length, s2.length);
    final int distance = levenshteinDistance(s1, s2);

    // Convert distance to similarity score (0-100)
    return ((maxLength - distance) / maxLength) * 100;
  }

  /// Check if two strings are similar enough based on a threshold
  static bool isSimilar(String s1, String s2, {double threshold = 70}) {
    return similarityScore(s1, s2) >= threshold;
  }

  /// Find the best match for a query in a list of strings
  /// Returns the index of the best match and its similarity score
  static Map<String, dynamic> findBestMatch(
    String query,
    List<String> choices,
  ) {
    if (choices.isEmpty) {
      return {'index': -1, 'score': 0.0, 'value': ''};
    }

    int bestIndex = 0;
    double bestScore = 0;

    for (int i = 0; i < choices.length; i++) {
      final double score = similarityScore(query, choices[i]);
      if (score > bestScore) {
        bestScore = score;
        bestIndex = i;
      }
    }

    return {
      'index': bestIndex,
      'score': bestScore,
      'value': choices[bestIndex],
    };
  }
}
