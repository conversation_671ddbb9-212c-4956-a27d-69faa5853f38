// import 'dart:async';

// import 'package:connectivity_plus/connectivity_plus.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// part 'internet_event.dart';
// part 'internet_state.dart';

// class InternetBloc extends Bloc<InternetEvent, InternetState> {
//   final Connectivity _connectivity = Connectivity();
//   StreamSubscription? _connectivitySubscription;

//   InternetBloc() : super(InternetInitial()) {
//     on<CheckInternetConnection>(_checkInternetConnection);
//     on<InternetConnectionChanged>(_onInternetConnectionChanged);

//     _connectivitySubscription =
//         _connectivity.onConnectivityChanged.listen((result) {
//       if (result.last == ConnectivityResult.none) {
//         add(InternetConnectionChanged(isConnected: false));
//       } else {
//         add(InternetConnectionChanged(isConnected: true));
//       }
//     });
//   }

//   Future<void> _checkInternetConnection(
//     CheckInternetConnection event,
//     Emitter<InternetState> emit,
//   ) async {
//     final result = await _connectivity.checkConnectivity();
//     if (result.last == ConnectivityResult.none) {
//       emit(InternetDisconnected());
//     } else {
//       emit(InternetConnected());
//     }
//   }

//   void _onInternetConnectionChanged(
//     InternetConnectionChanged event,
//     Emitter<InternetState> emit,
//   ) {
//     if (event.isConnected) {
//       emit(InternetConnected());
//     } else {
//       emit(InternetDisconnected());
//     }
//   }

//   @override
//   Future<void> close() {
//     _connectivitySubscription?.cancel();
//     return super.close();
//   }
// }
