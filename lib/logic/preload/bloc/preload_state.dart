part of 'preload_bloc.dart';

class PreloadState {
  final List<FeedModel> feeds;
  final Map<int, CachedVideoPlayerPlusController> controllers;
  final Map<int, ControllerStatus> controllerStatus;
  final int focusedIndex;

  PreloadState({
    required this.feeds,
    required this.controllers,
    required this.controllerStatus,
    required this.focusedIndex,
  });

  // Factory constructor for initial state
  factory PreloadState.initial() => PreloadState(
        focusedIndex: 0,
        feeds: const [],
        controllers: {},
        controllerStatus: {},
      );

  // Copy with method for creating new state instances
  PreloadState copyWith({
    List<FeedModel>? feeds,
    Map<int, CachedVideoPlayerPlusController>? controllers,
    Map<int, ControllerStatus>? controllerStatus,
    int? focusedIndex,
  }) {
    return PreloadState(
      feeds: feeds ?? this.feeds,
      controllers: controllers ?? this.controllers,
      controllerStatus: controllerStatus ?? this.controllerStatus,
      focusedIndex: focusedIndex ?? this.focusedIndex,
    );
  }
}
