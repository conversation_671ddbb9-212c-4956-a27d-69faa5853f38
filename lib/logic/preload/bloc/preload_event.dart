part of 'preload_bloc.dart';

abstract class PreloadEvent extends Equatable {
  const PreloadEvent();

  @override
  List<Object> get props => [];
}

class InitializeEvent extends PreloadEvent {
  final int currentFeedIndex;
  final List<FeedModel> feeds;

  const InitializeEvent(this.feeds, this.currentFeedIndex);

  @override
  List<Object> get props => [feeds];
}

class VideoIndexChangedEvent extends PreloadEvent {
  final int index;

  const VideoIndexChangedEvent(this.index);

  @override
  List<Object> get props => [index];
}

class DisposeControllersEvent extends PreloadEvent {
  const DisposeControllersEvent();
}

class StopControllerEvent extends PreloadEvent {
  final int index;

  const StopControllerEvent(this.index);

  @override
  List<Object> get props => [index];
}

class PlayControllerEvent extends PreloadEvent {
  final int index;

  const PlayControllerEvent(this.index);

  @override
  List<Object> get props => [index];
}
