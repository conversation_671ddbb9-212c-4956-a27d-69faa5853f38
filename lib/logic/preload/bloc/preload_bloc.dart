import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cached_video_player_plus/cached_video_player_plus.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:equatable/equatable.dart';
import 'package:vibeo/utils/log/app_logger.dart';

part 'preload_event.dart';
part 'preload_state.dart';

enum ControllerStatus { uninitialized, initializing, initialized, error }

class PreloadBloc extends Bloc<PreloadEvent, PreloadState> {
  PreloadBloc() : super(PreloadState.initial()) {
    // Register event handlers
    on<InitializeEvent>(_onInitialize);
    on<VideoIndexChangedEvent>(_onVideoIndexChanged);
    on<DisposeControllersEvent>(_onDisposeControllers);
    on<StopControllerEvent>(
      (event, emit) => _stopControllerAtIndex(event.index, emit),
    );
    on<PlayControllerEvent>(
      (event, emit) => _playControllerAtIndex(event.index, emit),
    );
  }

  // Event handler for initialize event
  Future<void> _onInitialize(
    InitializeEvent event,
    Emitter<PreloadState> emit,
  ) async {
    // Add feeds to the state
    emit(state.copyWith(feeds: event.feeds));

    /// Initialize 1st video
    await _initializeControllerAtIndex(event.currentFeedIndex, emit);

    /// Play 1st video
    await _playControllerAtIndex(event.currentFeedIndex, emit);

    /// Initialize 2nd video
    if (event.currentFeedIndex + 1 < event.feeds.length) {
      await _initializeControllerAtIndex(event.currentFeedIndex + 1, emit);
    }
  }

  // Event handler for video index changed event
  Future<void> _onVideoIndexChanged(
    VideoIndexChangedEvent event,
    Emitter<PreloadState> emit,
  ) async {
    if (event.index > state.focusedIndex) {
      await _playNext(event.index, emit);
    } else {
      await _playPrevious(event.index, emit);
    }
    emit(state.copyWith(focusedIndex: event.index));
  }

  Future<void> _playNext(int index, Emitter<PreloadState> emit) async {
    /// Stop [index - 1] controller
    if (index > 0) {
      await _stopControllerAtIndex(index - 1, emit);
    }

    /// Dispose [index - 2] controller
    if (index > 1) {
      await _disposeControllerAtIndex(index - 2, emit);
    }

    /// Play current video (already initialized)
    await _playControllerAtIndex(index, emit);

    /// Initialize [index + 1] controller
    if (index + 1 < state.feeds.length) {
      await _initializeControllerAtIndex(index + 1, emit);
    }
  }

  Future<void> _playPrevious(int index, Emitter<PreloadState> emit) async {
    /// Stop [index + 1] controller
    if (index + 1 < state.feeds.length) {
      await _stopControllerAtIndex(index + 1, emit);
    }

    /// Dispose [index + 2] controller
    if (index + 2 < state.feeds.length) {
      await _disposeControllerAtIndex(index + 2, emit);
    }

    /// Play current video (already initialized)
    await _playControllerAtIndex(index, emit);

    /// Initialize [index - 1] controller
    if (index - 1 >= 0) {
      await _initializeControllerAtIndex(index - 1, emit);
    }
  }

  Future<void> _initializeControllerAtIndex(
    int index,
    Emitter<PreloadState> emit,
  ) async {
    if (state.feeds.length <= index || index < 0) {
      AppLogger.debug('Invalid index for initialization: $index');
      return;
    }

    // Check if controller already exists and is initialized or initializing
    if (state.controllers.containsKey(index)) {
      final status =
          state.controllerStatus[index] ?? ControllerStatus.uninitialized;
      if (status == ControllerStatus.initialized) {
        AppLogger.debug('Controller already initialized for index: $index');
        return;
      } else if (status == ControllerStatus.initializing) {
        AppLogger.debug('Controller already initializing for index: $index');
        return;
      }
    }

    AppLogger.debug('Initializing controller for index: $index');
    AppLogger.debug('Video URL: ${state.feeds[index].videoURL}');

    // Update state to indicate initialization is in progress
    final updatedStatus =
        Map<int, ControllerStatus>.from(state.controllerStatus);
    updatedStatus[index] = ControllerStatus.initializing;
    emit(state.copyWith(controllerStatus: updatedStatus));

    /// Create new controller with explicit options
    final CachedVideoPlayerPlusController controller =
        CachedVideoPlayerPlusController.networkUrl(
      Uri.parse(state.feeds[index].videoURL),
      videoPlayerOptions: VideoPlayerOptions(
        mixWithOthers: false,
        allowBackgroundPlayback: false,
      ),
      httpHeaders: const {
        'Connection': 'keep-alive',
      },
      invalidateCacheIfOlderThan: const Duration(days: 1),
    );

    // Add controller to state immediately so UI can access it
    final updatedControllers =
        Map<int, CachedVideoPlayerPlusController>.from(state.controllers);
    updatedControllers[index] = controller;
    emit(state.copyWith(controllers: updatedControllers));

    // Initialize the controller
    try {
      AppLogger.debug('Starting controller initialization for index: $index');

      // Use a timeout for initialization to prevent hanging
      await controller.initialize().timeout(
        const Duration(seconds: 20),
        onTimeout: () {
          AppLogger.debug(
            'Controller initialization timed out for index: $index',
          );
          throw TimeoutException('Video initialization timed out');
        },
      );

      if (controller.value.hasError) {
        AppLogger.debug(
          'Controller has error after initialization: ${controller.value.errorDescription}',
        );
        final updatedStatus =
            Map<int, ControllerStatus>.from(state.controllerStatus);
        updatedStatus[index] = ControllerStatus.error;
        emit(state.copyWith(controllerStatus: updatedStatus));
        return;
      }

      AppLogger.debug('Controller initialized for index: $index');
      AppLogger.debug(
        'Video size: ${controller.value.size.width}x${controller.value.size.height}',
      );
      AppLogger.debug('Video aspectRatio: ${controller.value.aspectRatio}');

      // Preload a small amount of video to reduce initial buffering
      await controller.setVolume(0); // Mute while preloading
      await controller.play();
      await Future.delayed(const Duration(milliseconds: 300));
      await controller.pause();
      await controller.setVolume(1); // Restore volume
      await controller.seekTo(Duration.zero);

      // Update state to indicate initialization is complete
      final updatedStatus =
          Map<int, ControllerStatus>.from(state.controllerStatus);
      updatedStatus[index] = ControllerStatus.initialized;
      emit(state.copyWith(controllerStatus: updatedStatus));

      AppLogger.debug(
        'Controller fully initialized and ready for index: $index',
      );
    } catch (e, stackTrace) {
      AppLogger.debug('Error initializing controller for index $index: $e');
      AppLogger.debug('Stack trace: $stackTrace');

      // Update state to indicate initialization failed
      final updatedStatus =
          Map<int, ControllerStatus>.from(state.controllerStatus);
      updatedStatus[index] = ControllerStatus.error;
      emit(state.copyWith(controllerStatus: updatedStatus));
    }
  }

  Future<void> _stopControllerAtIndex(
    int index,
    Emitter<PreloadState> emit,
  ) async {
    if (state.feeds.length <= index ||
        index < 0 ||
        state.controllers[index] == null) {
      return;
    }

    try {
      final controller = state.controllers[index]!;
      await controller.pause();
      await controller.seekTo(Duration.zero);
      AppLogger.debug('🚀🚀🚀 STOPPED $index');
    } catch (e) {
      AppLogger.debug('Error stopping controller at index $index: $e');
    }
  }

  Future<void> _playControllerAtIndex(
    int index,
    Emitter<PreloadState> emit,
  ) async {
    if (state.feeds.length <= index ||
        index < 0 ||
        state.controllers[index] == null) {
      AppLogger.debug(
        'Cannot play: Invalid index or controller not found: $index',
      );
      return;
    }

    final controller = state.controllers[index]!;
    final status =
        state.controllerStatus[index] ?? ControllerStatus.uninitialized;

    // If controller is not initialized, initialize it first
    if (status != ControllerStatus.initialized) {
      AppLogger.debug(
        'Controller not initialized for index $index, initializing first',
      );
      await _initializeControllerAtIndex(index, emit);

      // Check if initialization was successful
      if (state.controllerStatus[index] != ControllerStatus.initialized) {
        AppLogger.debug(
          'Failed to initialize controller for index $index, cannot play',
        );
        return;
      }
    }

    // Now play the video
    try {
      await controller.play();
      AppLogger.debug('🚀🚀🚀 PLAYING $index');
    } catch (e) {
      AppLogger.debug('Error playing video at index $index: $e');
    }
  }

  Future<void> _disposeControllerAtIndex(
    int index,
    Emitter<PreloadState> emit,
  ) async {
    if (state.feeds.length <= index ||
        index < 0 ||
        state.controllers[index] == null) {
      return;
    }

    try {
      // Get the controller before removing it from state
      final controller = state.controllers[index]!;

      // Create a new map without the disposed controller
      final updatedControllers =
          Map<int, CachedVideoPlayerPlusController>.from(state.controllers)
            ..remove(index);

      // Also remove the status
      final updatedStatus =
          Map<int, ControllerStatus>.from(state.controllerStatus)
            ..remove(index);

      // Update the state with the new controllers map
      emit(
        state.copyWith(
          controllers: updatedControllers,
          controllerStatus: updatedStatus,
        ),
      );

      // Dispose the controller after updating state
      await controller.dispose();
      AppLogger.debug('🚀🚀🚀 DISPOSED $index');
    } catch (e) {
      AppLogger.debug('Error disposing controller at index $index: $e');
    }
  }

  Future<void> _onDisposeControllers(
    DisposeControllersEvent event,
    Emitter<PreloadState> emit,
  ) async {
    AppLogger.debug('Disposing all controllers');
    try {
      // Create a copy to avoid concurrent modification
      final controllers =
          Map<int, CachedVideoPlayerPlusController>.from(state.controllers);

      // Dispose each controller
      for (final controller in controllers.values) {
        try {
          await controller.pause();
          await controller.dispose();
        } catch (e) {
          AppLogger.debug('Error disposing controller: $e');
        }
      }

      // Reset state
      emit(PreloadState.initial());
      AppLogger.debug('All controllers disposed');
    } catch (e) {
      AppLogger.debug('Error in _onDisposeControllers: $e');
    }
  }

  @override
  Future<void> close() async {
    AppLogger.debug('Closing PreloadBloc');
    try {
      // Create a copy to avoid concurrent modification
      final controllers =
          Map<int, CachedVideoPlayerPlusController>.from(state.controllers);

      // Dispose each controller
      for (final controller in controllers.values) {
        try {
          await controller.pause();
          await controller.dispose();
        } catch (e) {
          AppLogger.debug('Error disposing controller during close: $e');
        }
      }
    } catch (e) {
      AppLogger.debug('Error in close: $e');
    }
    return super.close();
  }
}
