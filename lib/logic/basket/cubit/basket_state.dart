import 'package:equatable/equatable.dart';
import 'package:vibeo/models/models.dart';

enum BasketStatus { initial, loading, loaded, error }

class BasketState extends Equatable {
  final List<VenueModel> basketVenues;
  final BasketStatus status;
  final String? errorMessage;
  final Map<String, List<dynamic>> basketItemIDs; // Venue ID to offer IDs mapping

  const BasketState({
    this.basketVenues = const [],
    this.status = BasketStatus.initial,
    this.errorMessage,
    this.basketItemIDs = const {},
  });

  BasketState copyWith({
    List<VenueModel>? basketVenues,
    BasketStatus? status,
    String? errorMessage,
    Map<String, List<dynamic>>? basketItemIDs,
  }) {
    return BasketState(
      basketVenues: basketVenues ?? this.basketVenues,
      status: status ?? this.status,
      errorMessage: errorMessage,
      basketItemIDs: basketItemIDs ?? this.basketItemIDs,
    );
  }

  @override
  List<Object?> get props => [basketVenues, status, errorMessage, basketItemIDs];
}
