import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/basket/cubit/basket_state.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/services/basket/basket_storage_service.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class BasketCubit extends Cubit<BasketState> {
  final BasketStorageService _basketStorageService;
  final VenueRepository _venueRepository;

  BasketCubit({
    required BasketStorageService basketStorageService,
    VenueRepository? venueRepository,
  })  : _basketStorageService = basketStorageService,
        _venueRepository = venueRepository ?? VenueRepository(),
        super(const BasketState()) {
    AppLogger.info('BasketCubit: Created new instance');
  }

  /// Load basket items from storage and fetch venue details
  Future<void> loadBasketItems({bool forceRefresh = false}) async {
    AppLogger.info(
      'BasketCubit: loadBasketItems called, forceRefresh=$forceRefresh, status=${state.status}',
    );

    // If already loaded and not forcing refresh, return
    if (state.status == BasketStatus.loaded && !forceRefresh) {
      AppLogger.info(
        'BasketCubit: Already loaded and not forcing refresh, returning early',
      );
      return;
    }

    emit(state.copyWith(status: BasketStatus.loading));
    AppLogger.info('BasketCubit: Status set to loading');

    try {
      // Get basket item IDs from storage, sorted by timestamp (newest first)
      final basketItemIDs = await _basketStorageService.getBasketItemsSorted();
      AppLogger.info(
        'BasketCubit: Got ${basketItemIDs.length} basket items from storage',
      );

      // If basket is empty, update state and return
      if (basketItemIDs.isEmpty) {
        AppLogger.info(
          'BasketCubit: Basket is empty, updating state and returning',
        );
        emit(
          state.copyWith(
            basketVenues: [],
            basketItemIDs: basketItemIDs,
            status: BasketStatus.loaded,
          ),
        );
        return;
      }

      // Check if the basket items have changed
      final basketItemsEqual =
          _basketItemsEqual(basketItemIDs, state.basketItemIDs);
      AppLogger.info('BasketCubit: Basket items equal? $basketItemsEqual');

      if (!forceRefresh && basketItemsEqual) {
        // If basket items haven't changed, just update status
        AppLogger.info(
          "BasketCubit: Basket items haven't changed, just updating status",
        );
        emit(state.copyWith(status: BasketStatus.loaded));
        return;
      }

      // Get the venue IDs from the basket
      final List<String> venueIDs = basketItemIDs.keys.toList();

      AppLogger.info('BasketCubit: Fetching venues for IDs: $venueIDs');

      // Check if we already have all the venues in the state
      final existingVenues = state.basketVenues;
      final existingVenueIds = existingVenues.map((v) => v.id).toSet();
      final missingVenueIds =
          venueIDs.where((id) => !existingVenueIds.contains(id)).toList();

      List<VenueModel> venues = [];

      if (missingVenueIds.isEmpty && !forceRefresh) {
        // If we have all venues and not forcing refresh, use existing venues
        AppLogger.info('BasketCubit: Using existing venues, no need to fetch');
        venues = existingVenues;
      } else {
        // Fetch venue details for all venues in the basket
        AppLogger.info('BasketCubit: Fetching venues from API');
        venues = await _venueRepository.fetchVenueByIds(venueIds: venueIDs);

        if (venues.isEmpty) {
          emit(
            state.copyWith(
              status: BasketStatus.error,
              errorMessage: 'Failed to fetch venue details',
            ),
          );
          return;
        }
      }

      // Create a map to store the highest timestamp for each venue
      final Map<String, int> venueLatestTimestamps = {};

      // Find the latest timestamp for each venue from the basketItemIDs
      basketItemIDs.forEach((venueID, offerItems) {
        if (offerItems.isNotEmpty) {
          // Get the highest timestamp (first item since they're already sorted)
          final firstItem = offerItems.first;
          final timestamp =
              firstItem is Map ? (firstItem['timestamp'] as int? ?? 0) : 0;
          venueLatestTimestamps[venueID] = timestamp;
        }
      });

      // Sort venues by their latest offer timestamp
      venues.sort((a, b) {
        final timestampA = venueLatestTimestamps[a.id] ?? 0;
        final timestampB = venueLatestTimestamps[b.id] ?? 0;
        return timestampB.compareTo(timestampA); // Descending order
      });

      AppLogger.info(
        'BasketCubit: Emitting loaded state with ${venues.length} venues',
      );

      // Log venue IDs for debugging
      final venueIds = venues.map((v) => v.id).toList();
      AppLogger.info('BasketCubit: Venue IDs in state: $venueIds');

      emit(
        state.copyWith(
          basketVenues: venues,
          basketItemIDs: basketItemIDs,
          status: BasketStatus.loaded,
        ),
      );
    } catch (e) {
      AppLogger.error('Error loading basket items: $e');
      emit(
        state.copyWith(
          status: BasketStatus.error,
          errorMessage: 'Failed to load basket items: $e',
        ),
      );
    }
  }

  /// Filter venues based on search query
  List<VenueModel> filterVenues(String searchQuery) {
    if (state.basketVenues.isEmpty) {
      return [];
    }

    // Clean the search query - trim and convert to lowercase
    final String cleanQuery = searchQuery.trim().toLowerCase();

    if (cleanQuery.isEmpty) {
      // If search query is empty, return all venues
      return List.from(state.basketVenues);
    } else {
      // Create a filtered list
      final List<VenueModel> filtered = [];

      // Perform the filtering
      for (final venue in state.basketVenues) {
        final venueName = venue.name.toLowerCase();
        final isMatch = venueName.contains(cleanQuery);

        if (isMatch) {
          filtered.add(venue);
        }
      }

      return filtered;
    }
  }

  /// Check if two basket item maps are equal
  bool _basketItemsEqual(
    Map<String, List<dynamic>> a,
    Map<String, List<dynamic>> b,
  ) {
    AppLogger.info(
      'BasketCubit: Comparing basket items: a.length=${a.length}, b.length=${b.length}',
    );

    if (a.length != b.length) {
      AppLogger.info(
        'BasketCubit: Different lengths: a.length=${a.length}, b.length=${b.length}',
      );
      return false;
    }

    for (final venueId in a.keys) {
      if (!b.containsKey(venueId)) {
        AppLogger.info(
          'BasketCubit: Venue $venueId not found in second basket',
        );
        return false;
      }

      final aOffers = a[venueId]!;
      final bOffers = b[venueId]!;

      if (aOffers.length != bOffers.length) {
        AppLogger.info(
          'BasketCubit: Different offer counts for venue $venueId: a=${aOffers.length}, b=${bOffers.length}',
        );
        return false;
      }

      for (int i = 0; i < aOffers.length; i++) {
        final aOffer = aOffers[i];
        final bOffer = bOffers[i];

        final aId = aOffer is Map ? aOffer['id'] : aOffer;
        final bId = bOffer is Map ? bOffer['id'] : bOffer;

        if (aId != bId) {
          AppLogger.info(
            'BasketCubit: Different offer IDs at index $i for venue $venueId: a=$aId, b=$bId',
          );
          return false;
        }
      }
    }

    AppLogger.info('BasketCubit: Basket items are equal');
    return true;
  }
}
