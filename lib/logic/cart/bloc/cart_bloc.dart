import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

part 'cart_event.dart';
part 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  CartBloc() : super(CartState.initial()) {
    on<AddOfferToCart>(_onAddOfferToCart);
    on<RemoveOfferFromCart>(_onRemoveOfferFromCart);
    on<LoadCart>(_onLoadCart);
  }

  Future<void> _onAddOfferToCart(
    AddOfferToCart event,
    Emitter<CartState> emit,
  ) async {
    final updatedCart =
        Map<String, List<Map<String, dynamic>>>.from(state.cart);

    if (!updatedCart.containsKey(event.venueId)) {
      updatedCart[event.venueId] = [];
    }

    updatedCart[event.venueId]!.add(event.offer);

    await _saveCartToStorage(updatedCart);
    emit(CartState(cart: updatedCart));
  }

  Future<void> _onRemoveOfferFromCart(
    RemoveOfferFromCart event,
    Emitter<CartState> emit,
  ) async {
    final updatedCart =
        Map<String, List<Map<String, dynamic>>>.from(state.cart);

    if (updatedCart.containsKey(event.venueId)) {
      updatedCart[event.venueId]!
          .removeWhere((offer) => offer['id'] == event.offerId);

      if (updatedCart[event.venueId]!.isEmpty) {
        updatedCart.remove(event.venueId);
      }
    }

    await _saveCartToStorage(updatedCart);
    emit(CartState(cart: updatedCart));
  }

  Future<void> _onLoadCart(LoadCart event, Emitter<CartState> emit) async {
    final storedData = await _storage.read(key: 'cart');
    if (storedData != null) {
      final cart = Map<String, List<Map<String, dynamic>>>.from(
        (json.decode(storedData) as Map<String, dynamic>).map(
          (key, value) => MapEntry(
            key,
            (value as List)
                .map(
                  (item) =>
                      Map<String, dynamic>.from(item as Map<String, dynamic>),
                )
                .toList(),
          ),
        ),
      );
      emit(CartState(cart: cart));
    } else {
      emit(CartState.initial());
    }
  }

  Future<void> _saveCartToStorage(
    Map<String, List<Map<String, dynamic>>> cart,
  ) async {
    await _storage.write(key: 'cart', value: json.encode(cart));
  }
}
