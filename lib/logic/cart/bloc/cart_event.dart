part of 'cart_bloc.dart';

abstract class CartEvent {}

class AddOfferToCart extends CartEvent {
  final String venueId;
  final Map<String, dynamic> offer;

  AddOfferToCart({required this.venueId, required this.offer});
}

class RemoveOfferFromCart extends CartEvent {
  final String venueId;
  final String offerId;

  RemoveOfferFromCart({required this.venueId, required this.offerId});
}

class LoadCart extends CartEvent {}
