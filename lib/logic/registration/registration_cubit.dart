import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/utils/log/app_logger.dart';

part 'registration_state.dart';

class RegistrationCubit extends Cubit<RegistrationState> {
  RegistrationCubit() : super(RegistrationState());

  void updateUserDetails(String uid, String email, String photoURL) {
    emit(state.copyWith(uid: uid, email: email, photoURL: photoURL));
  }

  void updatePhoneNumber(String phone) {
    emit(state.copyWith(phoneNumber: phone));
  }

  void updateExtras(String extras) {
    emit(state.copyWith(extras: extras));
  }

  void updateFullName(String name) {
    emit(state.copyWith(fullName: name));
  }

  void updateAge(int age, String dob) {
    emit(state.copyWith(age: age, dob: dob));
  }

  void updateGender(String gender) {
    emit(state.copyWith(gender: gender));
  }

  void updateLocation(
    double lat,
    double lng,
  ) {
    emit(
      state.copyWith(
        latitude: lat,
        longitude: lng,
      ),
    );
  }

  void updateGenres(List<String> selectedGenres) {
    emit(state.copyWith(genres: selectedGenres));
    AppLogger.info(
      '${state.uid} ${state.email} ${state.phoneNumber} ${state.photoURL} ${state.fullName} ${state.age} ${state.genres}',
    );
  }
}
