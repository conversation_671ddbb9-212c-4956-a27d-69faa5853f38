import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:vibeo/models/user/token_model.dart';
import 'package:vibeo/models/user/user_model.dart';

class SecureStorage {
  static const _tokenKey = 'tokens';
  static const _userKey = 'user';
  final _storage = const FlutterSecureStorage();

  Future<void> saveTokens(TokenModel tokens) async {
    await _storage.write(
      key: _tokenKey,
      value: jsonEncode(tokens.toJson()),
    );
  }

  Future<void> saveUser(UserModel user) async {
    await _storage.write(
      key: _userKey,
      value: jsonEncode(user.toJson()),
    );
  }

  Future<TokenModel?> getTokens() async {
    final tokenStr = await _storage.read(key: _tokenKey);
    if (tokenStr != null) {
      return TokenModel.fromJson(jsonDecode(tokenStr) as Map<String, dynamic>);
    }
    return null;
  }

  Future<UserModel?> getUser() async {
    final userStr = await _storage.read(key: _userKey);
    if (userStr != null) {
      return UserModel.fromJson(jsonDecode(userStr) as Map<String, dynamic>);
    }
    return null;
  }

  // Future<void> clearAll() async {
  //   await _storage.deleteAll();
  // }
}
