part of 'registration_cubit.dart';

class RegistrationState {
  final String? email;
  final String? uid;
  final String? gender;
  final String? photoURL;
  final String? phoneNumber;
  final String? fullName;
  final int? age;
  final String? dob;
  final List<String>? genres;
  final double? latitude;
  final double? longitude;
  final String? extras;
  final Map<String, dynamic>? tokens;
  final String? error;

  RegistrationState({
    this.email,
    this.uid,
    this.photoURL,
    this.gender,
    this.phoneNumber,
    this.fullName,
    this.age,
    this.genres,
    this.error,
    this.dob,
    this.latitude,
    this.longitude,
    this.tokens,
    this.extras,
  });

  RegistrationState copyWith({
    String? email,
    String? uid,
    String? photoURL,
    String? phoneNumber,
    String? gender,
    String? fullName,
    int? age,
    String? dob,
    List<String>? genres,
    double? latitude,
    double? longitude,
    String? extras,
    Map<String, dynamic>? tokens,
    String? error,
  }) {
    return RegistrationState(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      photoURL: photoURL ?? this.photoURL,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      fullName: fullName ?? this.fullName,
      gender: gender ?? this.gender,
      age: age ?? this.age,
      dob: dob ?? this.dob,
      genres: genres ?? this.genres,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      tokens: tokens ?? this.tokens,
      error: error ?? this.error,
      extras: extras ?? this.extras,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'uid': uid,
      'photoURL': photoURL,
      'phoneNumber': phoneNumber,
      'fullName': fullName,
      'gender': gender,
      'age': age,
      'dob': dob,
      'genres': genres,
      'location': {
        'latitude': latitude,
        'longitude': longitude,
      },
      'tokens': tokens,
      'error': error,
      'extras': extras,
    };
  }
}
