import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class VenueLocalStorage {
  static const _savedVenuesKey = 'saved_venues';
  static const _ratedVenuesKey = 'rated_venues';
  final _storage = const FlutterSecureStorage();
  final String userID;

  VenueLocalStorage(this.userID);

  Future<String> _getUserSpecificKey() async {
    return '${_savedVenuesKey}_$userID';
  }

  Future<String> _getUserRatedSpecificKey() async {
    return '${_ratedVenuesKey}_$userID';
  }

  Future<void> saveVenue(String venueID) async {
    final key = await _getUserSpecificKey();
    final saved = await getSavedVenues();
    if (!saved.contains(venueID)) {
      saved.add(venueID);
      await _storage.write(
        key: key,
        value: jsonEncode(saved),
      );
    }
  }

  Future<void> rateVenue(String venueID) async {
    final key = await _getUserRatedSpecificKey();
    final saved = await getRatedVenues();
    if (!saved.contains(venueID)) {
      saved.add(venueID);
      await _storage.write(
        key: key,
        value: jsonEncode(saved),
      );
    }
  }

  Future<void> removeVenue(String venueID) async {
    final key = await _getUserSpecificKey();
    final saved = await getSavedVenues();
    saved.remove(venueID);
    await _storage.write(
      key: key,
      value: jsonEncode(saved),
    );
  }

  Future<List<String>> getSavedVenues() async {
    final key = await _getUserSpecificKey();
    final savedStr = await _storage.read(key: key);
    if (savedStr != null) {
      return List<String>.from(jsonDecode(savedStr) as List);
    }
    return [];
  }

  Future<List<String>> getRatedVenues() async {
    final key = await _getUserRatedSpecificKey();
    final savedStr = await _storage.read(key: key);
    if (savedStr != null) {
      return List<String>.from(jsonDecode(savedStr) as List);
    }
    return [];
  }

  Future<bool> isVenueSaved(String venueID) async {
    final saved = await getSavedVenues();
    return saved.contains(venueID);
  }

  Future<bool> isVenueRated(String venueID) async {
    final saved = await getRatedVenues();
    return saved.contains(venueID);
  }
}
