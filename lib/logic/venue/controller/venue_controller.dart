import 'package:vibeo/logic/venue/storage/venue_local_storage.dart';

class VenueController {
  final String userID;
  late final VenueLocalStorage _storage;
  VenueController(this.userID) {
    _storage = VenueLocalStorage(userID);
  }

  Future<bool> onVenueSaved(String venueID) async {
    final isSaved = await checkIfVenueSaved(venueID);
    if (isSaved) {
      await _storage.removeVenue(venueID);
      return false;
    } else {
      await _storage.saveVenue(venueID);
      return true;
    }
  }

  Future<bool> onRateVenue(String venueID) async {
    final isRated = await checkIfVenueRated(venueID);
    if (isRated) {
      return false;
    } else {
      await _storage.rateVenue(venueID);
      return true;
    }
  }

  Future<bool> checkIfVenueSaved(String venueID) async {
    final saved = await _storage.isVenueSaved(venueID);
    return saved;
  }

  Future<bool> checkIfVenueRated(String venueID) async {
    final rated = await _storage.isVenueRated(venueID);
    return rated;
  }

  Future<List<String>> venuesSavedList() async {
    final saved = await _storage.getSavedVenues();
    return saved;
  }

  Future<List<String>> venuesRatedList() async {
    final rated = await _storage.getRatedVenues();
    return rated;
  }
}
