import 'package:vibeo/models/models.dart';

import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class VenueRepository {
  final NetworkServicesApi _api;

  VenueRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<Map<String, dynamic>> fetchVenues({
    required int skip,
    required int limit,
    required Location? location,
  }) async {
    try {
      String url = '/v1/venues?skip=$skip&limit=$limit';

      if (location != null) {
        url += '&latitude=${location.latitude}&longitude=${location.longitude}';
      }

      final response = await _api.getAPI<Map<String, dynamic>>(
        url,
        (json) => json,
      );

      final venuesData = response['data'] as List<dynamic>;
      final totalCount = response['totalCount'] as int;

      final venues = venuesData.map((venue) {
        var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
        if (venueModel.thumbnails.isEmpty) {
          venueModel = venueModel.copyWith(
            thumbnails: [venueModel.imageLink],
          );
        }
        return venueModel;
      }).toList();

      return {
        'venues': venues,
        'totalCount': totalCount,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch venues: $e');
      throw Exception('Failed to fetch venues');
    }
  }

  Future<Map<String, dynamic>> fetchHighlightedVenues({
    required int skip,
    required int limit,
  }) async {
    try {
      final String url = '/v1/promotion/highlights?skip=$skip&limit=$limit';

      final response = await _api.getAPI<Map<String, dynamic>>(
        url,
        (json) => json,
      );

      final venuesData = response['data'] as List<dynamic>;
      final totalCount = response['totalCount'] as int;

      final venues = venuesData.map((venue) {
        var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
        if (venueModel.thumbnails.isEmpty) {
          venueModel = venueModel.copyWith(
            thumbnails: [venueModel.imageLink],
          );
        }
        return venueModel;
      }).toList();

      return {
        'venues': venues,
        'totalCount': totalCount,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch venues: $e');
      throw Exception('Failed to fetch venues');
    }
  }

  Future<Map<String, dynamic>> fetchSpecialVenues({
    required String dateTime,
    required int skip,
    required int limit,
  }) async {
    try {
      final String url =
          '/v1/promotion/special?dateTime=$dateTime&skip=$skip&limit=$limit';

      final response = await _api.getAPI<Map<String, dynamic>>(
        url,
        (json) => json,
      );

      final venuesData = response['data'] as List<dynamic>;
      final totalCount = response['totalCount'] as int;

      final venues = venuesData.map((venue) {
        var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
        if (venueModel.thumbnails.isEmpty) {
          venueModel = venueModel.copyWith(
            thumbnails: [venueModel.imageLink],
          );
        }
        return venueModel;
      }).toList();

      return {
        'venues': venues,
        'totalCount': totalCount,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch venues: $e');
      throw Exception('Failed to fetch venues');
    }
  }

  Future<Map<String, dynamic>> fetchFilteredVenues({
    required int skip,
    required int limit,
    required Location? location,
    required String filter,
  }) async {
    try {
      String url = '/v1/venues/filter?skip=$skip&limit=$limit&filter=$filter';

      if (location != null) {
        url += '&latitude=${location.latitude}&longitude=${location.longitude}';
      }

      final response = await _api.getAPI<Map<String, dynamic>>(
        url,
        (json) => json,
      );
      final venuesData = response['data']['data'] as List<dynamic>;

      final venues = venuesData.map((venue) {
        var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
        if (venueModel.thumbnails.isEmpty) {
          venueModel = venueModel.copyWith(
            thumbnails: [venueModel.imageLink],
          );
        }
        return venueModel;
      }).toList();

      return {
        'venues': venues,
        'title': response['data']['title'] ?? 'Filtered Venues',
      };
    } catch (e) {
      AppLogger.error('Failed to fetch filtered venues: $e');
      throw Exception('Failed to fetch filtered venues');
    }
  }

  Future<VenueModel> fetchVenueByID({required String id}) async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/venues/id?id=$id',
        (json) => json,
      );

      final venuesData = response['data'] as List<dynamic>;

      final venues = venuesData.map((venue) {
        var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
        if (venueModel.thumbnails.isEmpty) {
          venueModel = venueModel.copyWith(
            thumbnails: [venueModel.imageLink],
          );
        }
        return venueModel;
      }).toList();

      return venues.first;
    } catch (e) {
      AppLogger.error('Failed to fetch venues: $e');
      throw Exception('Failed to fetch venues');
    }
  }

  Future<List<VenueModel>> fetchVenueByIds({
    required List<String> venueIds,
  }) async {
    try {
      final response = await _api.postAPI<Map<String, dynamic>>(
        '/v1/venues/saved',
        {
          'venueIds': venueIds,
        },
        (json) => json,
      );

      final venuesData = response['data'] as List<dynamic>;

      final venues = venuesData.map((venue) {
        var venueModel = VenueModel.fromJson(venue as Map<String, dynamic>);
        if (venueModel.thumbnails.isEmpty) {
          venueModel = venueModel.copyWith(
            thumbnails: [venueModel.imageLink],
          );
        }
        return venueModel;
      }).toList();

      return venues;
    } catch (e) {
      AppLogger.error('Failed to fetch venues: $e');
      throw Exception('Failed to fetch venues');
    }
  }

  Future<bool> rateVenue({
    required String venueID,
    required String userID,
    required int rating,
    required String? feedback,
  }) async {
    try {
      final response = await _api.postAPI<bool>(
        '/v1/venues/rate',
        {
          'venueID': venueID,
          'userID': userID,
          'rating': rating,
          'feedback': feedback,
        },
        (json) => json['data'] as bool,
      );

      return response;
    } catch (e) {
      AppLogger.error('Failed to fetch venues: $e');
      throw Exception('Failed to fetch venues');
    }
  }
}
