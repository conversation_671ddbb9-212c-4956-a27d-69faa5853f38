part of 'venue_bloc.dart';

abstract class VenueState {
  final List<VenueModel> venues;
  final List<VenueModel> filteredVenues;
  final String? currentFilter;

  final int totalVenues;
  final bool isLoadingMore;
  final bool hasReachedEnd;

  const VenueState({
    this.venues = const [],
    this.filteredVenues = const [],
    this.currentFilter,
    this.totalVenues = 0,
    this.isLoadingMore = false,
    this.hasReachedEnd = false,
  });
}

class VenueInitial extends VenueState {
  const VenueInitial() : super();
}

class VenueLoading extends VenueState {
  const VenueLoading() : super();
}

class VenueLoaded extends VenueState {
  const VenueLoaded(
    List<VenueModel> venues,
    int totalVenues, {
    List<VenueModel>? filteredVenues,
    super.currentFilter,
    super.hasReachedEnd,
    super.isLoadingMore,
  }) : super(
          venues: venues,
          totalVenues: totalVenues,
          filteredVenues: filteredVenues ?? venues,
        );
}

class VenueError extends VenueState {
  final String message;

  const VenueError(this.message);
}
