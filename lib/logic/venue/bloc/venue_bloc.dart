import 'package:bloc/bloc.dart';

import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';

import 'package:vibeo/models/models.dart';

part 'venue_event.dart';
part 'venue_state.dart';

class VenueBloc extends Bloc<VenueEvent, VenueState> {
  final VenueRepository _venueRepository;
  final FeedRepository _feedRepository;
  bool _isLoadingMore = false;
  DateTime? _lastThrottleError;
  int? _lastFetchSkip;

  VenueBloc({VenueRepository? venueRepository, FeedRepository? feedRepository})
      : _venueRepository = venueRepository ?? VenueRepository(),
        _feedRepository = feedRepository ?? FeedRepository(),
        super(const VenueInitial()) {
    on<FetchVenuesEvent>(_onFetchVenues);
    on<FetchVenueFeedsEvent>(_onFetchVenueFeeds);
    on<RefreshVenuesEvent>(_onRefreshVenues);
    on<AddVenueEvent>(_onAddVenue);
    on<FetchFilteredVenuesEvent>(_onFetchFilteredVenues);
    on<UpdateVenueEvent>(_onUpdateVenue);
  }
  Future<void> _onUpdateVenue(
    UpdateVenueEvent event,
    Emitter<VenueState> emit,
  ) async {
    if (state is VenueLoaded) {
      final currentState = state as VenueLoaded;
      final venueIndex =
          currentState.venues.indexWhere((v) => v.id == event.venue.id);

      if (venueIndex != -1) {
        final updatedVenues = List<VenueModel>.from(currentState.venues);
        updatedVenues[venueIndex] = event.venue;

        emit(
          VenueLoaded(
            updatedVenues,
            currentState.totalVenues,
          ),
        );
      }
    }
  }

  Future<void> _onRefreshVenues(
    RefreshVenuesEvent event,
    Emitter<VenueState> emit,
  ) async {
    await _onFetchVenues(
      FetchVenuesEvent(
        forceRefresh: true,
        location: event.location,
      ),
      emit,
    );
  }

  Future<void> _onFetchFilteredVenues(
    FetchFilteredVenuesEvent event,
    Emitter<VenueState> emit,
  ) async {
    if (_isLoadingMore) return;

    // Add throttle cooldown check
    if (_lastThrottleError != null) {
      final cooldownPeriod = DateTime.now().difference(_lastThrottleError!);
      if (cooldownPeriod.inSeconds < 5) return; // 5 second cooldown
    }

    try {
      // If we're starting a new filter search
      if (event.skip == 0) {
        emit(const VenueLoading());
      } else {
        // If we're fetching more filtered results
        emit(
          VenueLoaded(
            state.venues,
            state.totalVenues,
            isLoadingMore: true,
            currentFilter: event.filter,
          ),
        );
      }

      _isLoadingMore = true;

      // Call repository with filter parameter
      final response = await _venueRepository.fetchFilteredVenues(
        filter: event.filter,
        skip: event.skip,
        limit: 7,
        location: event.location,
      );

      final venues = response['venues'] as List<VenueModel>;
      final totalCount = response['totalCount'] as int;

      if (event.skip == 0) {
        // First batch of filtered results
        emit(
          VenueLoaded(
            venues,
            totalCount,
            currentFilter: event.filter,
          ),
        );
      } else {
        // Append to existing results with duplicate check
        final currentVenues = state.venues;
        final existingIds = Set<String>.from(currentVenues.map((v) => v.id));
        final filteredNewVenues =
            venues.where((venue) => !existingIds.contains(venue.id)).toList();

        emit(
          VenueLoaded(
            [...currentVenues, ...filteredNewVenues],
            totalCount,
            currentFilter: event.filter,
          ),
        );
      }
      _lastThrottleError = null;
    } catch (e) {
      if (e.toString().contains('ThrottlerException')) {
        _lastThrottleError = DateTime.now();
        // Maintain current state during throttle
        if (state is VenueLoaded) {
          emit(
            VenueLoaded(
              state.venues,
              state.totalVenues,
              currentFilter: state.currentFilter,
            ),
          );
        }
      } else if (event.skip == 0) {
        emit(const VenueError('Failed to fetch filtered venues'));
      }
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> _onFetchVenues(
    FetchVenuesEvent event,
    Emitter<VenueState> emit,
  ) async {
    // Prevent duplicate fetches for same skip value
    if (_lastFetchSkip == event.skip && !event.forceRefresh) return;

    if (state.venues.length >= state.totalVenues &&
        event.skip > 0 &&
        state.venues.length < 7) {
      return;
    }

    if (_isLoadingMore) return;

    _lastFetchSkip = event.skip;

    // Add throttle cooldown check
    if (_lastThrottleError != null) {
      final cooldownPeriod = DateTime.now().difference(_lastThrottleError!);
      if (cooldownPeriod.inSeconds < 5) return; // 5 second cooldown
    }

    try {
      if (event.skip == 0) {
        emit(const VenueLoading());
      } else {
        emit(
          VenueLoaded(
            state.venues,
            state.totalVenues,
            isLoadingMore: true,
          ),
        );
      }

      _isLoadingMore = true;
      final response = await _venueRepository.fetchVenues(
        skip: event.skip,
        limit: 7,
        location: event.location,
      );
      final venues = response['venues'] as List<VenueModel>;
      final totalCount = response['totalCount'] as int;

      if (event.skip == 0) {
        emit(VenueLoaded(venues, totalCount));
      } else {
        emit(
          VenueLoaded(
            [...state.venues, ...venues],
            totalCount,
          ),
        );
      }
      _lastThrottleError = null;
    } catch (e) {
      if (e.toString().contains('ThrottlerException')) {
        _lastThrottleError = DateTime.now();
        // Maintain current state during throttle
        if (state is VenueLoaded) {
          emit(
            VenueLoaded(
              state.venues,
              state.totalVenues,
            ),
          );
        }
      } else if (event.skip == 0) {
        emit(const VenueError('Failed to fetch venues'));
      }
    } finally {
      _isLoadingMore = false;
    }
  }

  Future<void> _onFetchVenueFeeds(
    FetchVenueFeedsEvent event,
    Emitter<VenueState> emit,
  ) async {
    // if (state is! VenueLoaded) return;

    final currentState = state as VenueLoaded;

    try {
      // Find the venue index
      final venueIndex =
          currentState.venues.indexWhere((venue) => venue.id == event.venueID);

      // If venue not found or already has feeds, return
      if (venueIndex == -1) return;
      if (currentState.venues[venueIndex].feeds != null) return;

      // Fetch feeds for the venue
      final feeds = await _feedRepository.fetchVenueFeeds(
        event.venueID,
        limit: 6,
      );

      // Create a new list of venues with the updated venue
      final updatedVenues = List<VenueModel>.from(currentState.venues);
      updatedVenues[venueIndex] =
          updatedVenues[venueIndex].copyWith(feeds: feeds);

      // Emit new state with updated venues
      emit(
        VenueLoaded(
          updatedVenues,
          currentState.totalVenues,
        ),
      );
    } catch (e) {
      emit(const VenueError('Failed to fetch venue feeds'));
    }
  }

  Future<void> _onAddVenue(
    AddVenueEvent event,
    Emitter<VenueState> emit,
  ) async {
    try {
      // Check if venue already exists using a unique identifier (e.g., id or some unique property)
      final bool venueExists =
          state.venues.any((venue) => venue.id == event.venueModel.id);

      if (!venueExists) {
        emit(
          VenueLoaded(
            [...state.venues, event.venueModel],
            state.totalVenues,
            isLoadingMore: false,
          ),
        );
      } else {
        // Optionally emit the current state if you want to maintain it
        if (state is VenueLoaded) {
          emit(
            VenueLoaded(
              state.venues,
              state.totalVenues,
            ),
          );
        }
      }
    } catch (e) {
      if (e.toString().contains('ThrottlerException')) {
        _lastThrottleError = DateTime.now();
        if (state is VenueLoaded) {
          emit(
            VenueLoaded(
              state.venues,
              state.totalVenues,
            ),
          );
        }
      }
    } finally {
      _isLoadingMore = false;
    }
  }
}
