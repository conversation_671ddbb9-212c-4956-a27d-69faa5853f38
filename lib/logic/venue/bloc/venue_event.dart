part of 'venue_bloc.dart';

abstract class VenueEvent {
  const VenueEvent();
}

class FetchFilteredVenuesEvent extends VenueEvent {
  final String filter;
  final int skip;
  final Location? location;

  const FetchFilteredVenuesEvent({
    required this.filter,
    this.skip = 0,
    this.location,
  });
}

class FetchVenuesEvent extends VenueEvent {
  final int skip;
  final Location? location;
  final bool forceRefresh;
  final String? filter;

  const FetchVenuesEvent({
    required this.location,
    this.skip = 0,
    this.forceRefresh = false,
    this.filter,
  });
}

class ApplyFilterEvent extends VenueEvent {
  final String? filter;
  final Location? location;
  final bool forceRefresh;

  const ApplyFilterEvent({
    this.filter,
    this.location,
    this.forceRefresh = false,
  });
}

class FetchVenueByIDEvent extends VenueEvent {
  final String venueID;
  const FetchVenueByIDEvent({required this.venueID});
}

class UpdateVenueEvent extends VenueEvent {
  final VenueModel venue;
  const UpdateVenueEvent(this.venue);
}

class FetchVenueFeedsEvent extends VenueEvent {
  final String venueID;
  final bool forceRefresh;
  const FetchVenueFeedsEvent(this.venueID, {this.forceRefresh = false});
}

class FetchVenueOffersEvent extends VenueEvent {
  final String venueID;
  final bool forceRefresh;
  const FetchVenueOffersEvent(this.venueID, {this.forceRefresh = false});
}

class RefreshVenuesEvent extends VenueEvent {
  final Location? location;
  const RefreshVenuesEvent({
    required this.location,
  });
}

class AddVenueEvent extends VenueEvent {
  final VenueModel venueModel;
  const AddVenueEvent(this.venueModel);
}
