import 'dart:convert';

import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/models/location/location.dart';
import 'package:vibeo/models/user/registration_response.dart';
import 'package:vibeo/models/user/user_model.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/location/location_permission.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class UserRepository {
  final NetworkServicesApi _api;

  UserRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<UserModel?> checkUserExists(String uid) async {
    try {
      final encodedUid = Uri.encodeComponent(uid);
      var response = await _api.getAPI<UserModel>(
        '/v1/user?uid=$encodedUid',
        UserModel.fromJson,
      );
      // response = response.copyWith(location: null);
      if (await isLocationPermissionGranted()) {
        final currentLocation = await getCurrentPosition();

        if (currentLocation != null) {
          response = response.copyWith(
            location: Location(
              latitude: currentLocation.latitude,
              longitude: currentLocation.longitude,
            ),
          );
        }
      }
      return response;
    } on NotFoundException {
      return null;
    }
  }

  Future<bool> checkPhoneNumberExists(String phoneNumber) async {
    try {
      final formattedPhoneNumber = phoneNumber.replaceAll(RegExp(r'\D'), '');
      final encodedPhoneNumber = Uri.encodeComponent(formattedPhoneNumber);
      final response = await _api.getAPI<bool>(
        '/v1/user/phone-number?phoneNumber=$encodedPhoneNumber',
        (json) => json['exists'] as bool,
      );

      return response;
    } on NotFoundException {
      return false;
    }
  }

  Future<Map<String, dynamic>> registerUser(
    RegistrationState data,
    String firebaseIDToken,
  ) async {
    final response = await _api.postAPI<RegistrationResponse>(
      '/v1/user?firebaseIDToken=$firebaseIDToken',
      data.toJson(),
      RegistrationResponse.fromJson,
    );

    return {
      'user': response.user,
      'tokens': response.tokens,
    };
  }

  Future<bool?> deleteUser(String uid) async {
    final response = await _api.deleteAPI<bool>(
        '/v1/user/',
        json.encode(
          {
            'data': uid,
          },
        ), (data) {
      return data as bool;
    });

    return response;
  }
}
