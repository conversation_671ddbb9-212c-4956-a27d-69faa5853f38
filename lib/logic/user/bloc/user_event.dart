part of 'user_bloc.dart';

abstract class UserEvent extends Equatable {
  const UserEvent();

  @override
  List<Object?> get props => [];
}

class CheckUserExists extends UserEvent {
  final AuthUser authUser;
  const CheckUserExists(this.authUser);

  @override
  List<Object> get props => [authUser];
}

class RegisterUser extends UserEvent {
  final RegistrationState registrationData;
  const RegisterUser({
    required this.registrationData,
  });

  @override
  List<Object> get props => [registrationData];
}

class DeleteUser extends UserEvent {
  final String uid;
  const DeleteUser({
    required this.uid,
  });

  @override
  List<Object> get props => [uid];
}

class FetchUserFeedsEvent extends UserEvent {
  final String uid;
  final bool forceRefresh;

  const FetchUserFeedsEvent(
    this.uid, {
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [uid, forceRefresh];
}

class FetchUserLikedFeedsEvent extends UserEvent {
  final List<String> feedIds;
  final bool forceRefresh;

  const FetchUserLikedFeedsEvent(
    this.feedIds, {
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [feedIds, forceRefresh];
}

class FetchUserSavedOffersEvent extends UserEvent {
  final List<String> offerIds;
  final bool forceRefresh;

  const FetchUserSavedOffersEvent(
    this.offerIds, {
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [offerIds, forceRefresh];
}

class FetchUserSavedVenuesEvent extends UserEvent {
  final List<String> venueIds;
  final bool forceRefresh;

  const FetchUserSavedVenuesEvent(
    this.venueIds, {
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [venueIds, forceRefresh];
}

class DeleteUserFeed extends UserEvent {
  final String feedId;
  final String userId;
  const DeleteUserFeed({
    required this.feedId,
    required this.userId,
  });

  @override
  List<Object> get props => [feedId, userId];
}

class RefreshFeeds extends UserEvent {
  final String uid;
  const RefreshFeeds(
    this.uid,
  );
}

class RefreshLikedFeeds extends UserEvent {
  final List<String> feedIds;
  const RefreshLikedFeeds(
    this.feedIds,
  );
}

class RefreshSavedVenues extends UserEvent {
  final List<String> venueIds;
  const RefreshSavedVenues(
    this.venueIds,
  );
}

class UpdateUserLocation extends UserEvent {
  final double latitude;
  final double longitude;

  const UpdateUserLocation({
    required this.latitude,
    required this.longitude,
  });

  @override
  List<Object> get props => [latitude, longitude];
}
