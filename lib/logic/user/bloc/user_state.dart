part of 'user_bloc.dart';

abstract class UserState extends Equatable {
  final UserModel? user;
  final List<FeedModel>? feeds;
  final List<FeedModel>? likedFeeds;
  final List<OfferModel>? savedOffers;
  final List<VenueModel>? savedVenues;
  final bool isLoadingFeeds;
  final bool feedsFetched;
  final bool isLikedLoadingFeeds;
  final bool likedFeedsFetched;
  final bool isLoadingSavedOffers;
  final bool isLoadingSavedVenues;
  final bool savedOffersFetched;
  final bool savedVenuesFetched;

  const UserState({
    this.user,
    this.feeds = const [],
    this.likedFeeds = const [],
    this.savedOffers = const [],
    this.savedVenues = const [],
    this.isLoadingFeeds = false,
    this.feedsFetched = false,
    this.isLikedLoadingFeeds = false,
    this.likedFeedsFetched = false,
    this.isLoadingSavedOffers = false,
    this.isLoadingSavedVenues = false,
    this.savedOffersFetched = false,
    this.savedVenuesFetched = false,
  });

  @override
  List<Object?> get props => [
        user,
        feeds,
        isLoadingFeeds,
        feedsFetched,
        likedFeeds,
        savedOffers,
        isLikedLoadingFeeds,
        likedFeedsFetched,
        isLoadingSavedOffers,
        savedOffersFetched,
        savedVenues,
        isLoadingSavedVenues,
        savedVenuesFetched,
      ];
}

class UserInitial extends UserState {
  const UserInitial();
}

class UserLoading extends UserState {
  const UserLoading();
}

class UserRegistering extends UserState {
  const UserRegistering();
}

class UserExists extends UserState {
  const UserExists({
    super.user,
    super.feeds,
    super.likedFeeds,
    super.savedOffers,
    super.savedVenues,
    super.feedsFetched = false,
    super.likedFeedsFetched = false,
    super.isLoadingFeeds = false,
    super.isLikedLoadingFeeds = false,
    super.isLoadingSavedOffers = false,
    super.isLoadingSavedVenues = false,
    super.savedOffersFetched = false,
    super.savedVenuesFetched = false,
  });

  UserExists copyWith({
    UserModel? user,
    List<FeedModel>? feeds,
    List<OfferModel>? savedOffers,
    List<FeedModel>? likedFeeds,
    List<VenueModel>? savedVenues,
    bool? feedsFetched,
    bool? likedFeedsFetched,
    bool? isLoadingFeeds,
    bool? isLikedLoadingFeeds,
    bool? isLoadingSavedOffers,
    bool? isLoadingSavedVenues,
    bool? savedOffersFetched,
    bool? savedVenuesFetched,
  }) {
    return UserExists(
      user: user ?? this.user,
      feeds: feeds ?? this.feeds,
      savedOffers: savedOffers ?? this.savedOffers,
      likedFeeds: likedFeeds ?? this.likedFeeds,
      savedVenues: savedVenues ?? this.savedVenues,
      feedsFetched: feedsFetched ?? this.feedsFetched,
      likedFeedsFetched: likedFeedsFetched ?? this.likedFeedsFetched,
      isLoadingFeeds: isLoadingFeeds ?? this.isLoadingFeeds,
      isLikedLoadingFeeds: isLikedLoadingFeeds ?? this.isLikedLoadingFeeds,
      isLoadingSavedOffers: isLoadingSavedOffers ?? this.isLoadingSavedOffers,
      isLoadingSavedVenues: isLoadingSavedVenues ?? this.isLoadingSavedVenues,
      savedOffersFetched: savedOffersFetched ?? this.savedOffersFetched,
      savedVenuesFetched: savedVenuesFetched ?? this.savedVenuesFetched,
    );
  }

  @override
  List<Object?> get props => [
        user,
        feeds,
        savedOffers,
        likedFeeds,
        savedVenues,
        feedsFetched,
        likedFeedsFetched,
        isLoadingFeeds,
        isLikedLoadingFeeds,
        isLoadingSavedOffers,
        isLoadingSavedVenues,
        savedOffersFetched,
        savedVenuesFetched,
      ];
}

class UserNotFound extends UserState {
  const UserNotFound();

  @override
  List<Object> get props => [];
}

class UserRegistrationRequired extends UserState {
  final AuthUser authUser;
  const UserRegistrationRequired(this.authUser);

  @override
  List<Object> get props => [authUser];
}

class UserRegistrationSuccess extends UserState {
  final UserModel _user;

  const UserRegistrationSuccess(this._user);

  @override
  UserModel get user => _user;

  @override
  List<Object> get props => [user];
}

class UserError extends UserState {
  final AppException error;
  const UserError(this.error);

  @override
  List<Object> get props => [error];
}

class UserDeleted extends UserState {
  const UserDeleted();

  @override
  List<Object> get props => [];
}
