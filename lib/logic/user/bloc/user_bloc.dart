import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/storage/feed_upload_rate_logic.dart';
import 'package:vibeo/logic/offer/bloc/offer_repo_impl.dart';
import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/logic/registration/save_user_info.dart';
import 'package:vibeo/logic/user/bloc/user_rep_impl.dart';

import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/user/auth_model.dart';
import 'package:vibeo/models/user/token_model.dart';
import 'package:vibeo/models/user/user_model.dart';
import 'package:vibeo/models/venue/venue_model.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';

part 'user_event.dart';
part 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final UserRepository _userRepository;
  final FeedRepository _feedRepository;
  final OfferRepository _offerRepository;
  final VenueRepository _venueRepository;

  UserBloc({
    UserRepository? userRepository,
    FeedRepository? feedRepository,
    OfferRepository? offerRepository,
    VenueRepository? venueRepository,
  })  : _userRepository = userRepository ?? UserRepository(),
        _feedRepository = feedRepository ?? FeedRepository(),
        _offerRepository = offerRepository ?? OfferRepository(),
        _venueRepository = venueRepository ?? VenueRepository(),
        super(const UserInitial()) {
    on<CheckUserExists>(_onCheckUserExists);
    on<RegisterUser>(_onRegisterUser);
    on<DeleteUser>(_onDeleteUser);
    on<FetchUserFeedsEvent>(_onFetchUserFeeds);
    on<FetchUserLikedFeedsEvent>(_onFetchUserLikedFeeds);
    on<FetchUserSavedOffersEvent>(_onFetchUserSavedOffers);
    on<FetchUserSavedVenuesEvent>(_onFetchUserSavedVenues);
    on<DeleteUserFeed>(_onDeleteUserFeed);
    on<RefreshFeeds>(_onRefreshFeeds);
    on<RefreshLikedFeeds>(_onRefreshLikedFeeds);
    on<RefreshSavedVenues>(_onRefreshSavedVenues);
  }

  Future<void> _onRefreshFeeds(
    RefreshFeeds event,
    Emitter<UserState> emit,
  ) async {
    await _onFetchUserFeeds(
      FetchUserFeedsEvent(
        event.uid,
        forceRefresh: true,
      ),
      emit,
    );
  }

  Future<void> _onRefreshLikedFeeds(
    RefreshLikedFeeds event,
    Emitter<UserState> emit,
  ) async {
    await _onFetchUserLikedFeeds(
      FetchUserLikedFeedsEvent(
        event.feedIds,
        forceRefresh: true,
      ),
      emit,
    );
  }

  Future<void> _onRefreshSavedVenues(
    RefreshSavedVenues event,
    Emitter<UserState> emit,
  ) async {
    await _onFetchUserSavedVenues(
      FetchUserSavedVenuesEvent(
        event.venueIds,
        forceRefresh: true,
      ),
      emit,
    );
  }

  Future<void> _onCheckUserExists(
    CheckUserExists event,
    Emitter<UserState> emit,
  ) async {
    try {
      emit(const UserLoading());
      final user = await _userRepository.checkUserExists(event.authUser.id);

      if (user != null) {
        emit(UserExists(user: user));
      } else {
        emit(const UserNotFound());
      }
    } on PermissionDeniedException catch (_) {
      emit(const UserNotFound());
    } on AppException catch (error) {
      emit(UserError(error));
    }
  }

  Future<void> _onRegisterUser(
    RegisterUser event,
    Emitter<UserState> emit,
  ) async {
    try {
      emit(const UserRegistering());
      final FirebaseAuth auth = FirebaseAuth.instance;
      final firebaseIDToken = await auth.currentUser?.getIdToken();
      final data = await _userRepository.registerUser(
        event.registrationData,
        firebaseIDToken!,
      );

      final user = data['user'] as UserModel;
      final tokens = data['tokens'] as TokenModel;

      final storage = SecureStorage();
      await storage.saveTokens(tokens);
      await storage.saveUser(user);
      final AnalyticsService analytics = AnalyticsService.instance;
      await analytics.logUserRegistration(
        userId: user.uid,
      );
      analytics.setUserType(kDebugMode ? 'internal' : 'external');

      emit(UserExists(user: user));
    } on AppException catch (e) {
      emit(UserError(e));
      rethrow;
    }
  }

  Future<void> _onDeleteUser(
    DeleteUser event,
    Emitter<UserState> emit,
  ) async {
    try {
      emit(const UserLoading());
      try {
        final currentUser = FirebaseAuth.instance.currentUser;
        await currentUser?.delete();
      } on FirebaseAuthException catch (e) {
        if (e.code == 'requires-recent-login') {
          await Sentry.captureException(
            e,
            hint: Hint.withMap(
              {'message': FirebaseAuth.instance.currentUser?.email},
            ),
          );
        } else {
          throw Exception('Error while deleting user account');
        }
      } finally {
        // Delete user data from your database
        final data = await _userRepository.deleteUser(event.uid);

        if (data!) {
          // Clear local storage
          // final storage = SecureStorage();
          // await storage.clearAll();

          emit(const UserDeleted());
        }
      }
    } on FirebaseAuthException catch (_) {
      rethrow;
    } on AppException catch (e) {
      emit(UserError(e));
      rethrow;
    }
  }

  Future<void> _onFetchUserFeeds(
    FetchUserFeedsEvent event,
    Emitter<UserState> emit,
  ) async {
    if (state is! UserExists) return;
    final currentState = state as UserExists;

    if (!event.forceRefresh && currentState.feedsFetched) return;

    emit(
      UserExists(
        user: currentState.user,
        feeds: currentState.feeds,
        feedsFetched: true,
        isLoadingFeeds: true,
      ),
    );

    try {
      final feeds = await _feedRepository.fetchUserFeeds(event.uid);
      emit(
        UserExists(
          user: currentState.user,
          feeds: feeds,
          feedsFetched: true,
        ),
      );
    } catch (e) {
      emit(
        UserExists(
          user: currentState.user,
          feeds: currentState.feeds,
          feedsFetched: true,
        ),
      );
    }
  }

  Future<void> _onFetchUserLikedFeeds(
    FetchUserLikedFeedsEvent event,
    Emitter<UserState> emit,
  ) async {
    if (state is! UserExists) return;
    final currentState = state as UserExists;

    if (!event.forceRefresh &&
        currentState.likedFeedsFetched &&
        (currentState.likedFeeds!.length == event.feedIds.length)) {
      return;
    }

    emit(
      currentState.copyWith(
        likedFeedsFetched: false,
        isLikedLoadingFeeds: true,
      ),
    );

    try {
      final feeds = await _feedRepository.fetchUserLikedFeeds(event.feedIds);
      emit(
        currentState.copyWith(
          likedFeeds: feeds,
          likedFeedsFetched: true,
          isLikedLoadingFeeds: false,
        ),
      );
    } catch (e) {
      emit(
        currentState.copyWith(
          likedFeedsFetched: true,
          isLikedLoadingFeeds: false,
        ),
      );
    }
  }

  Future<void> _onFetchUserSavedOffers(
    FetchUserSavedOffersEvent event,
    Emitter<UserState> emit,
  ) async {
    if (state is! UserExists) return;
    final currentState = state as UserExists;

    if (!event.forceRefresh &&
        currentState.savedOffersFetched &&
        (currentState.savedOffers!.length == event.offerIds.length)) {
      return;
    }

    emit(
      currentState.copyWith(
        savedOffersFetched: true,
        isLoadingSavedOffers: true,
      ),
    );

    try {
      final offers =
          await _offerRepository.fetchUserSavedOffers(event.offerIds);
      emit(
        currentState.copyWith(
          savedOffers: offers,
          savedOffersFetched: true,
          isLoadingSavedOffers: false,
        ),
      );
    } catch (e) {
      emit(
        currentState.copyWith(
          savedOffersFetched: true,
          isLoadingSavedOffers: false,
        ),
      );
    }
  }

  Future<void> _onFetchUserSavedVenues(
    FetchUserSavedVenuesEvent event,
    Emitter<UserState> emit,
  ) async {
    if (state is! UserExists) return;
    final currentState = state as UserExists;

    if (!event.forceRefresh &&
        currentState.savedVenuesFetched &&
        (currentState.savedVenues!.length == event.venueIds.length)) {
      return;
    }

    emit(
      currentState.copyWith(
        savedVenuesFetched: true,
        isLoadingSavedVenues: true,
      ),
    );

    try {
      final venues = await _venueRepository.fetchVenueByIds(
        venueIds: event.venueIds,
      );
      emit(
        currentState.copyWith(
          savedVenues: venues,
          savedVenuesFetched: true,
          isLoadingSavedVenues: false,
        ),
      );
    } catch (e) {
      emit(
        currentState.copyWith(
          savedVenuesFetched: true,
          isLoadingSavedVenues: false,
        ),
      );
    }
  }

  Future<void> _onDeleteUserFeed(
    DeleteUserFeed event,
    Emitter<UserState> emit,
  ) async {
    if (state is! UserExists) return;
    final currentState = state as UserExists;

    try {
      final success = await _feedRepository.deleteFeed(event.feedId);

      if (success) {
        final FeedRateLimiter limiter = FeedRateLimiter();
        final updatedFeeds = currentState.feeds
            ?.where((feed) => feed.id != event.feedId)
            .toList();
        if (currentState.user!.isPartner) {
          await limiter.removeFeedFromHistory(
            currentState.user!.uid,
            event.feedId,
          );
        }

        emit(
          UserExists(
            user: currentState.user,
            feeds: updatedFeeds,
            feedsFetched: true,
          ),
        );
      }
    } on AppException catch (e) {
      emit(UserError(e));
      // Re-emit previous state to maintain feeds list
      emit(currentState);
    }
  }
}
