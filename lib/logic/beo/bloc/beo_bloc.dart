import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:vibeo/logic/beo/bloc/beo_repo_impl.dart';
import 'package:vibeo/models/beo/beo.model.dart';

part 'beo_event.dart';
part 'beo_state.dart';

class BeoRecommendationsBloc
    extends Bloc<BeoRecommendationsEvent, BeoRecommendationsState> {
  final BeoRepository repository;

  BeoRecommendationsBloc({required this.repository})
      : super(BeoRecommendationsInitial()) {
    on<FetchBeoRecommendations>(_onFetchRecommendations);
  }

  Future<void> _onFetchRecommendations(
    FetchBeoRecommendations event,
    Emitter<BeoRecommendationsState> emit,
  ) async {
    emit(BeoRecommendationsLoading());
    try {
      final response = await repository.getRecommendations(event.query);
      emit(BeoRecommendationsLoaded(response));
    } catch (e) {
      emit(BeoRecommendationsError(e.toString()));
    }
  }
}
