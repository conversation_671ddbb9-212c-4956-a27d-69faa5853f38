import 'dart:convert';

import 'package:vibeo/models/beo/beo.model.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class BeoRepository {
  final NetworkServicesApi _api;

  BeoRepository({NetworkServicesApi? api}) : _api = api ?? NetworkServicesApi();

  Future<BeoResponse> getRecommendations(String query) async {
    try {
      final encodedQuery = Uri.encodeComponent(query);
      final response = await _api.getAPI<BeoResponse>(
        '/v1/beo?query=$encodedQuery',
        BeoResponse.fromJson,
      );

      return response;
    } on NotFoundException {
      throw BusinessLogicException(message: 'Recommendations not found');
    } catch (e) {
      AppLogger.error('Failed to fetch beo feeds: $e');
      throw BusinessLogicException(message: 'An unexpected error occurred: $e');
    }
  }

  Future<void> saveChatMessages(
    String userID,
    List<Map<String, dynamic>> messages,
  ) async {
    final jsonMessages = messages
        .map(
          (msg) => {
            ...msg,
            'type': msg['type'].toJson(),
            'data': null,
          },
        )
        .toList();

    _api
        .postAPI<void>(
          '/v1/beo/save-messages',
          jsonEncode({'userID': userID, 'data': jsonMessages}),
          (json) {},
        )
        .ignore();
  }
}
