part of 'beo_bloc.dart';

abstract class BeoRecommendationsState extends Equatable {
  @override
  List<Object> get props => [];
}

class BeoRecommendationsInitial extends BeoRecommendationsState {}

class BeoRecommendationsLoading extends BeoRecommendationsState {}

class BeoRecommendationsLoaded extends BeoRecommendationsState {
  final BeoResponse response;

  BeoRecommendationsLoaded(this.response);

  @override
  List<Object> get props => [response];
}

class BeoRecommendationsError extends BeoRecommendationsState {
  final String message;

  BeoRecommendationsError(this.message);

  @override
  List<Object> get props => [message];
}
