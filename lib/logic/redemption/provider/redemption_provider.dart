import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/redemption/cubit/redemption_cubit.dart';
import 'package:vibeo/logic/redemption/repository/redemption_repository_impl.dart';

/// Provider for the RedemptionCubit
class RedemptionProvider extends StatelessWidget {
  final Widget child;

  const RedemptionProvider({
    required this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RedemptionCubit(RedemptionRepositoryImpl()),
      child: child,
    );
  }
}
