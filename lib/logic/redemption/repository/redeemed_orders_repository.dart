import 'package:dio/dio.dart';
import 'package:vibeo/models/offer/redeemed_offer_data.dart';
import 'package:vibeo/models/offer/redeemed_orders_response.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class RedeemedOrdersRepository {
  final String _baseUrl =
      'https://testvouchersticketing-dev-367993833476.us-central1.run.app/api/public';

  final Dio _dio;

  RedeemedOrdersRepository({Dio? dio})
      : _dio = dio ??
            Dio(
              BaseOptions(
                connectTimeout: const Duration(seconds: 30),
                receiveTimeout: const Duration(seconds: 30),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
              ),
            );

  /// Fetch redeemed orders for a user
  Future<List<VenueRedeemedOffers>> fetchRedeemedOrders({
    required String userId,
    required String userEmail,
  }) async {
    try {
      final url =
          '$_baseUrl/orders/redeemed?userId=$userId&userEmail=$userEmail';

      final response = await _dio.get(
        url,
      );

      if (response.statusCode == 200) {
        AppLogger.info('Received response: ${response.data}');

        final jsonData = response.data as Map<String, dynamic>;
        AppLogger.info('Response data: $jsonData');

        final redeemedOrdersResponse =
            RedeemedOrdersResponse.fromJson(jsonData);
        AppLogger.info(
          'Parsed response: ${redeemedOrdersResponse.data.length} venues',
        );

        // Convert API response to VenueRedeemedOffers list for UI
        final venueOffers = redeemedOrdersResponse.data
            .map((venueOrder) => venueOrder.toVenueRedeemedOffers())
            .toList();

        AppLogger.info('Converted to ${venueOffers.length} venue offers');
        return venueOffers;
      } else {
        AppLogger.error(
          'Failed to fetch redeemed orders: ${response.statusCode}',
        );
        throw Exception('Failed to fetch redeemed orders');
      }
    } on DioException catch (e) {
      AppLogger.error('Dio error fetching redeemed orders: ${e.message}');
      throw Exception('Error fetching redeemed orders: ${e.message}');
    } catch (e) {
      AppLogger.error('Error fetching redeemed orders: $e');
      throw Exception('Error fetching redeemed orders: $e');
    }
  }
}
