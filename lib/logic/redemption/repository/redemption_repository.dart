import 'package:vibeo/models/redemption/redemption_confirmation.dart';
import 'package:vibeo/models/redemption/redemption_request.dart';
import 'package:vibeo/models/redemption/redemption_response.dart';

abstract class RedemptionRepository {
  /// Initiates the redemption process by sending the offer ID, user ID, and email
  /// Returns a RedemptionResponse with a unique code and status
  Future<RedemptionResponse> initiateRedemption(RedemptionRequest request);

  /// Confirms the redemption using the unique code and PIN
  /// Returns a RedemptionConfirmation with transaction details
  Future<RedemptionConfirmation> confirmRedemptionPin(
    String uniqueCode,
    String pin,
  );
  Future<RedemptionConfirmation> confirmRedemptionNFC(
    String uniqueCode,
    String tagUID,
  );
}
