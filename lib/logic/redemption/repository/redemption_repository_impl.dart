import 'package:dio/dio.dart';

import 'package:vibeo/logic/redemption/repository/redemption_repository.dart';
import 'package:vibeo/models/redemption/redemption_confirmation.dart';
import 'package:vibeo/models/redemption/redemption_request.dart';
import 'package:vibeo/models/redemption/redemption_response.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class RedemptionRepositoryImpl implements RedemptionRepository {
  final Dio _dio;

  // Base URL for the redemption API
  final String _baseUrl =
      'https://testvouchersticketing-dev-367993833476.us-central1.run.app/api/public';

  RedemptionRepositoryImpl({Dio? dio})
      : _dio = dio ??
            Dio(
              BaseOptions(
                connectTimeout: const Duration(seconds: 30),
                receiveTimeout: const Duration(seconds: 30),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
              ),
            );

  @override
  Future<RedemptionResponse> initiateRedemption(
    RedemptionRequest request,
  ) async {
    try {
      final url = '$_baseUrl/purchase/unpaid';

      // Prepare the request data
      final data = {
        'vouchers': {
          request.offerId: 1,
        },
        'userId': request.userId,
        'userEmail': request.userEmail,
      };

      AppLogger.info('Redemption request data: $data');
      AppLogger.info('Initiating redemption for offer: ${request.offerId}');

      try {
        // Make the API call directly with Dio
        final response = await _dio.post(url, data: data);

        // Log the raw response
        AppLogger.info('Raw response: ${response.data}');

        // Check if the response is successful
        if (response.statusCode == 200) {
          // Extract the data field from the response
          final responseData = response.data as Map<String, dynamic>;
          final data = responseData['data'] as Map<String, dynamic>;

          // Create the RedemptionResponse object
          final redemptionResponse = RedemptionResponse.fromJson(data);

          AppLogger.info(
            'Redemption initiated successfully: ${redemptionResponse.uniqueCode}',
          );
          return redemptionResponse;
        } else {
          throw BusinessLogicException(
            message:
                'Failed to initiate redemption: Unexpected status code ${response.statusCode}',
          );
        }
      } on DioException catch (dioError) {
        // Extract error message from the response
        if (dioError.response != null &&
            dioError.response!.data is Map<String, dynamic>) {
          final errorData = dioError.response!.data as Map<String, dynamic>;
          final errorMessage =
              errorData['message'] as String? ?? 'Unknown error occurred';

          AppLogger.error('API Error: $errorMessage');
          throw BusinessLogicException(message: errorMessage);
        }
        rethrow;
      }
    } catch (e) {
      if (e is BusinessLogicException) {
        rethrow;
      }
      AppLogger.error('Failed to initiate redemption: $e');
      throw BusinessLogicException(
        message: 'Failed to initiate redemption: $e',
      );
    }
  }

  @override
  Future<RedemptionConfirmation> confirmRedemptionPin(
    String uniqueCode,
    String pin,
  ) async {
    try {
      // Include query parameters in the URL
      final url = '$_baseUrl/redeem?uniqueCode=$uniqueCode&pin=$pin';

      AppLogger.info('Confirming redemption for code: $uniqueCode');

      try {
        // Make the API call directly with Dio (using POST with query parameters)
        final response = await _dio.post(url);

        // Log the raw response
        AppLogger.info('Raw response: ${response.data}');

        // Check if the response is successful
        if (response.statusCode == 200) {
          AppLogger.info('Response redemption data: ${response.data}');
          // Extract the data field from the response
          final responseData = response.data as Map<String, dynamic>;
          final data = responseData['data'] as Map<String, dynamic>;

          // Create the RedemptionConfirmation object
          final confirmation = RedemptionConfirmation.fromJson(data);

          AppLogger.info(
            'Redemption confirmed successfully: ${confirmation.redemptionId}',
          );
          return confirmation;
        } else {
          throw BusinessLogicException(
            message:
                'Failed to confirm redemption: Unexpected status code ${response.statusCode}',
          );
        }
      } on DioException catch (dioError) {
        // Extract error message from the response
        if (dioError.response != null &&
            dioError.response!.data is Map<String, dynamic>) {
          final errorData = dioError.response!.data as Map<String, dynamic>;
          final errorMessage =
              errorData['message'] as String? ?? 'Unknown error occurred';

          AppLogger.error('API Error: $errorMessage');
          throw BusinessLogicException(message: errorMessage);
        }
        rethrow;
      }
    } catch (e) {
      if (e is BusinessLogicException) {
        rethrow;
      }
      AppLogger.error('Failed to confirm redemption: $e');
      throw BusinessLogicException(
        message: 'Failed to confirm redemption: $e',
      );
    }
  }

  @override
  Future<RedemptionConfirmation> confirmRedemptionNFC(
    String uniqueCode,
    String tagUID,
  ) async {
    try {
      // Include query parameters in the URL
      final url = '$_baseUrl/nfc/redeem?uniqueCode=$uniqueCode&tagUid=$tagUID';

      AppLogger.info('Confirming redemption for code: $uniqueCode');

      try {
        // Make the API call directly with Dio (using POST with query parameters)
        final response = await _dio.post(url);

        // Log the raw response
        AppLogger.info('Raw response: ${response.data}');

        // Check if the response is successful
        if (response.statusCode == 200) {
          AppLogger.info('Response redemption data: ${response.data}');
          // Extract the data field from the response
          final responseData = response.data as Map<String, dynamic>;
          final data = responseData['data'] as Map<String, dynamic>;

          // Create the RedemptionConfirmation object
          final confirmation = RedemptionConfirmation.fromJson(data);

          AppLogger.info(
            'Redemption confirmed successfully: ${confirmation.redemptionId}',
          );
          return confirmation;
        } else {
          throw BusinessLogicException(
            message:
                'Failed to confirm redemption: Unexpected status code ${response.statusCode}',
          );
        }
      } on DioException catch (dioError) {
        // Extract error message from the response
        if (dioError.response != null &&
            dioError.response!.data is Map<String, dynamic>) {
          final errorData = dioError.response!.data as Map<String, dynamic>;
          final errorMessage =
              errorData['message'] as String? ?? 'Unknown error occurred';

          AppLogger.error('API Error: $errorMessage');
          throw BusinessLogicException(message: errorMessage);
        }
        rethrow;
      }
    } catch (e) {
      if (e is BusinessLogicException) {
        rethrow;
      }
      AppLogger.error('Failed to confirm redemption: $e');
      throw BusinessLogicException(
        message: 'Failed to confirm redemption: $e',
      );
    }
  }
}
