import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/redemption/cubit/redemption_state.dart';
import 'package:vibeo/logic/redemption/repository/redemption_repository.dart';
import 'package:vibeo/models/redemption/redemption_request.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class RedemptionCubit extends Cubit<RedemptionState> {
  final RedemptionRepository _repository;

  RedemptionCubit(this._repository) : super(RedemptionInitial());

  /// Initiates the redemption process
  Future<void> initiateRedemption({
    required String offerId,
    required String userId,
    required String userEmail,
  }) async {
    try {
      // Emit loading state
      emit(const RedemptionLoading(isInitiating: true));

      // Create the request
      final request = RedemptionRequest(
        offerId: offerId,
        userId: userId,
        userEmail: userEmail,
      );

      // Call the repository
      final response = await _repository.initiateRedemption(request);

      // Emit success state
      emit(RedemptionInitiated(response));
    } catch (e) {
      AppLogger.error('Error initiating redemption: $e');

      // Create a more user-friendly error message
      String errorMessage;

      if (e is BusinessLogicException) {
        // Use the message from the BusinessLogicException directly
        errorMessage = e.message;
      } else if (e.toString().contains('can only be purchased once per day')) {
        errorMessage = 'You have already redeemed this offer today.';
      } else if (e.toString().contains('400')) {
        errorMessage =
            'Invalid request format. Please check your offer details.';
      } else if (e.toString().contains('401')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (e.toString().contains('404')) {
        errorMessage = 'Offer not found. It may have expired or been removed.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      } else {
        errorMessage = 'Error: $e';
      }

      emit(
        RedemptionError(
          errorMessage,
          isInitiationError: true,
        ),
      );
    }
  }

  /// Confirms the redemption with the unique code and PIN
  Future<void> confirmRedemptionPin({
    required String uniqueCode,
    required String pin,
  }) async {
    try {
      // Emit loading state
      emit(const RedemptionLoading(isConfirming: true));

      // Call the repository
      final confirmation =
          await _repository.confirmRedemptionPin(uniqueCode, pin);

      // Emit success state
      emit(RedemptionConfirmed(confirmation));
    } catch (e) {
      AppLogger.error('Error confirming redemption: $e');

      // Create a more user-friendly error message
      String errorMessage;

      if (e is BusinessLogicException) {
        // Use the message from the BusinessLogicException directly
        errorMessage = e.message;
      } else if (e.toString().contains('400')) {
        errorMessage = 'Invalid confirmation details. Please try again.';
      } else if (e.toString().contains('401')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (e.toString().contains('404')) {
        errorMessage = 'Redemption code not found or expired.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      } else {
        errorMessage = 'Confirmation error. Please try again.';
      }

      emit(
        RedemptionError(
          errorMessage,
          isConfirmationError: true,
        ),
      );
    }
  }

  Future<void> confirmRedemptionNFC({
    required String uniqueCode,
    required String tagUID,
  }) async {
    try {
      // Emit loading state
      emit(const RedemptionLoading(isConfirming: true));

      // Call the repository
      final confirmation =
          await _repository.confirmRedemptionNFC(uniqueCode, tagUID);

      // Emit success state
      emit(RedemptionConfirmed(confirmation));
    } catch (e) {
      AppLogger.error('Error confirming redemption: $e');

      // Create a more user-friendly error message
      String errorMessage;

      if (e is BusinessLogicException) {
        // Use the message from the BusinessLogicException directly
        errorMessage = e.message;
      } else if (e.toString().contains('400')) {
        errorMessage = 'Invalid confirmation details. Please try again.';
      } else if (e.toString().contains('401')) {
        errorMessage = 'Authentication failed. Please log in again.';
      } else if (e.toString().contains('404')) {
        errorMessage = 'Redemption code not found or expired.';
      } else if (e.toString().contains('500')) {
        errorMessage = 'Server error. Please try again later.';
      } else {
        errorMessage = 'Confirmation error. Please try again.';
      }

      emit(
        RedemptionError(
          errorMessage,
          isConfirmationError: true,
        ),
      );
    }
  }

  /// Reset the redemption state to initial
  void reset() {
    emit(RedemptionInitial());
  }
}
