import 'package:equatable/equatable.dart';
import 'package:vibeo/models/redemption/redemption_confirmation.dart';
import 'package:vibeo/models/redemption/redemption_response.dart';

abstract class RedemptionState extends Equatable {
  const RedemptionState();

  @override
  List<Object?> get props => [];
}

/// Initial state when no redemption process has started
class RedemptionInitial extends RedemptionState {}

/// State when a redemption process is loading (either initiation or confirmation)
class RedemptionLoading extends RedemptionState {
  final bool isInitiating;
  final bool isConfirming;

  const RedemptionLoading({
    this.isInitiating = false,
    this.isConfirming = false,
  });

  @override
  List<Object?> get props => [isInitiating, isConfirming];
}

/// State when redemption has been initiated successfully
class RedemptionInitiated extends RedemptionState {
  final RedemptionResponse response;

  const RedemptionInitiated(this.response);

  @override
  List<Object?> get props => [response];
}

/// State when redemption has been confirmed successfully
class RedemptionConfirmed extends RedemptionState {
  final RedemptionConfirmation confirmation;

  const RedemptionConfirmed(this.confirmation);

  @override
  List<Object?> get props => [confirmation];
}

/// State when an error occurs during the redemption process
class RedemptionError extends RedemptionState {
  final String message;
  final bool isInitiationError;
  final bool isConfirmationError;

  const RedemptionError(
    this.message, {
    this.isInitiationError = false,
    this.isConfirmationError = false,
  });

  @override
  List<Object?> get props => [message, isInitiationError, isConfirmationError];
}
