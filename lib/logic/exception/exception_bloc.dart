import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/exception/exception_event.dart';
import 'package:vibeo/utils/log/app_logger.dart';

part 'exception_state.dart';

class GlobalErrorBloc extends Bloc<GlobalErrorEvent, GlobalErrorState> {
  GlobalErrorBloc() : super(GlobalErrorInitial()) {
    on<ShowErrorEvent>((event, emit) {
      AppLogger.error(event.message, [
        event.error,
      ]);
      emit(GlobalErrorOccurred(event.message));
    });
  }
}
