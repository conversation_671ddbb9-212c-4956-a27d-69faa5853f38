import 'package:vibeo/models/promoters/promoter_model.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class PromoterRepository {
  final NetworkServicesApi _api;

  PromoterRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<List<PromoterModel>> fetchVenuePromoters(String venueID) async {
    try {
      final encodedVenueID = Uri.encodeComponent(venueID);
      final response = await _api.getAPI<List<dynamic>>(
        '/v1/promoters?venueID=$encodedVenueID',
        (json) => json['data'] as List<dynamic>,
      );
      final result = response
          .map((offer) {
            try {
              return PromoterModel.fromJson(offer as Map<String, dynamic>);
            } catch (e) {
              AppLogger.error('Error parsing promoter: $e');
              return null;
            }
          })
          .where((offer) => offer != null)
          .cast<PromoterModel>()
          .toList();

      return result;
    } catch (e) {
      AppLogger.error('Failed to fetch venue promoters: $e');
      return [];
    }
  }
}
