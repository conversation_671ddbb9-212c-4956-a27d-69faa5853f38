import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:vibeo/logic/promoter/bloc/promoter_repo_impl.dart';
import 'package:vibeo/models/promoters/promoter_model.dart';

part 'promoter_event.dart';
part 'promoter_state.dart';

class PromoterBloc extends Bloc<PromoterEvent, PromoterState> {
  final PromoterRepository _promoterRepository = PromoterRepository();
  late PromotersLoaded _loadedState = const PromotersLoaded(
    venuePromoters: {},
  );

  PromoterBloc() : super(const PromotersInitial()) {
    on<FetchVenuePromotersEvent>(
      _onFetchVenuePromoters,
    );
  }

  Future<void> _onFetchVenuePromoters(
    FetchVenuePromotersEvent event,
    Emitter<PromoterState> emit,
  ) async {
    // Check if we already have data for this venue
    if (_loadedState.venuePromoters.containsKey(event.venueID) &&
        _loadedState.venuePromoters[event.venueID]?.isNotEmpty == true) {
      // We already have valid data, so don't fetch again
      return;
    }

    // Update loading state while preserving existing data
    if (state is! PromotersLoaded) {
      emit(const PromotersLoading());
    } else {
      emit(PromotersLoading(venuePromoters: _loadedState.venuePromoters));
    }

    try {
      final promoters =
          await _promoterRepository.fetchVenuePromoters(event.venueID);

      // Update the loaded state with new data
      _loadedState = PromotersLoaded(
        venuePromoters: {
          ..._loadedState.venuePromoters,
          event.venueID: promoters.isEmpty ? null : promoters,
        },
      );

      emit(_loadedState);
    } catch (e) {
      if (state is PromotersLoaded) {
        // Keep existing data on error
        emit(_loadedState);
      } else {
        emit(PromotersError(e.toString()));
      }
    }
  }
}
