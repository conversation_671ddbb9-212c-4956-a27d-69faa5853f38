part of 'promoter_bloc.dart';

abstract class PromoterState extends Equatable {
  final Map<String, List<PromoterModel>?> venuePromoters;
  const PromoterState({
    this.venuePromoters = const {},
  });

  @override
  List<Object?> get props => [venuePromoters];
}

class PromotersInitial extends PromoterState {
  const PromotersInitial() : super();
}

class PromotersLoading extends PromoterState {
  const PromotersLoading({super.venuePromoters});
}

class PromotersLoaded extends PromoterState {
  const PromotersLoaded({super.venuePromoters});
}

class PromotersError extends PromoterState {
  final String message;

  const PromotersError(this.message, {super.venuePromoters});
}
