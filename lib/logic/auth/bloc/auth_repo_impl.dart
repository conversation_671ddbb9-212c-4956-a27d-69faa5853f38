import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:vibeo/logic/user/bloc/user_rep_impl.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/models/user/auth_model.dart';
import 'package:vibeo/logic/auth/bloc/auth_repo_contract.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class AuthRepository implements IAuthenticationRepository {
  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;

  AuthRepository({
    FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _googleSignIn = googleSignIn ?? GoogleSignIn() {
    _firebaseAuth.setSettings(
      forceRecaptchaFlow: false,
      appVerificationDisabledForTesting: false,
    );
  }

  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  @override
  Future<AuthResult> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw AuthenticationException(message: 'Google sign in aborted');
      }

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential =
          await _firebaseAuth.signInWithCredential(credential);

      return AuthResult(
        success: true,
        user: _mapFirebaseUser(userCredential.user),
      );
    } catch (e) {
      throw AuthenticationException(message: 'Google sign in failed: $e');
    }
  }

  @override
  Future<AuthResult> signInWithApple() async {
    try {
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      final userCredential = await _firebaseAuth.signInWithCredential(
        oauthCredential,
      );
      return AuthResult(
        success: true,
        user: _mapFirebaseUser(
          userCredential.user,
          fullName: userCredential.user?.displayName ??
              oauthCredential.appleFullPersonName?.givenName,
        ),
      );
    } catch (e) {
      throw AuthenticationException(message: 'Apple sign in failed: $e');
    }
  }

  Future<String?> phoneSignIn(String phoneNumber) async {
    final Completer<String?> completer = Completer<String?>();
    final pNumber = phoneNumber.replaceAll('+1', '');

    try {
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: '+1$pNumber',
        verificationCompleted: (PhoneAuthCredential credential) async {
          // await _firebaseAuth.signInWithCredential(credential);
          // completer.complete(null);
          completer.complete(credential.verificationId);
        },
        verificationFailed: completer.completeError,
        codeSent: (String verificationId, int? resendToken) {
          completer.complete(verificationId);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          if (!completer.isCompleted) {
            completer.complete(verificationId);
          }
        },
        forceResendingToken: null,
        timeout: const Duration(seconds: 60),
      );

      return await completer.future;
    } catch (e) {
      AppLogger.error('Error in phoneSignIn: $e');
      rethrow;
    }
  }

  Future<bool> verifyOTP(
    String verificationId,
    String smsCode,
    String? phoneNumber,
  ) async {
    try {
      final PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: smsCode,
      );

      final User? currentUser = FirebaseAuth.instance.currentUser;
      final UserRepository userRepository = UserRepository();

      // For signed in users (via Google/Apple)
      if (currentUser != null) {
        try {
          // Check if phone number exists in your database

          if (phoneNumber != null) {
            final numberExists =
                await userRepository.checkPhoneNumberExists(phoneNumber);

            if (!numberExists) {
              // If number doesn't exist in DB, allow updating
              // await currentUser.linkWithCredential(credential);
              await currentUser.updatePhoneNumber(credential);
              return true;
            } else {
              throw AuthenticationException(
                message: 'Phone number is already linked to another account.',
              );
            }
          } else {
            throw AuthenticationException(
              message:
                  'We encountered with some problem. Please try again later!',
            );
          }
        } on FirebaseAuthException catch (e) {
          if (e.code == 'provider-already-linked' ||
              e.code == 'credential-already-in-use') {}
          rethrow;
        }
      } else {
        throw AuthenticationException(
          message: 'We encountered with some problem. Please try again later!',
        );
      }
    } on FirebaseAuthException catch (e) {
      AppLogger.error('OTP verification failed: $e');
      rethrow;
    }
  }

  @override
  AuthUser? getCurrentUser() {
    final user = _firebaseAuth.currentUser;
    if (user == null) return null;
    return _mapFirebaseUser(user);
  }

  @override
  Future<void> signOut() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (e) {
      throw AuthenticationException(message: 'Sign out failed: $e');
    }
  }

  @override
  bool isAuthenticated() => _firebaseAuth.currentUser != null;

  AuthUser _mapFirebaseUser(User? user, {String? fullName}) {
    if (user == null) throw AuthenticationException(message: 'User is null');

    return AuthUser(
      id: user.uid,
      email: user.email,
      phoneNumber: user.phoneNumber,
      displayName: user.displayName,
      photoUrl: user.photoURL,
      fullName: fullName,
    );
  }

  @override
  String getCurrentUserId() {
    return _firebaseAuth.currentUser!.uid;
  }

  @override
  Future<String?> getIDToken() async {
    return _firebaseAuth.currentUser!.getIdToken();
  }
}
