import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/constants/constants.dart';
import 'package:vibeo/logic/registration/registration_cubit.dart';

import 'package:vibeo/models/user/auth_model.dart';
import 'package:vibeo/logic/auth/bloc/auth_repo_impl.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/utils/utils.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;
  late StreamSubscription<User?> _authStateSubscription;

  AuthBloc({
    required AuthRepository authRepository,
  })  : _authRepository = authRepository,
        super(const AuthInitial()) {
    on<AuthStarted>(_onStarted);
    on<SignInWithGoogle>(_onSignInWithGoogle);
    on<SignInWithApple>(_onSignInWithApple);
    on<SignOut>(_onSignOut);
    on<AuthStateChanged>(_onAuthStateChanged);
    on<PhoneSignIn>(_onPhoneSignIn);
    on<VerifyOTP>(_onVerifyOTP);

    _authStateSubscription = _authRepository.authStateChanges.listen(
      (user) {
        if (user != null) {
          final authUser = _authRepository.getCurrentUser();
          if (authUser != null) {
            add(AuthStateChanged(user: authUser));
          }
        } else {
          add(const AuthStateChanged(user: null));
        }
      },
    );
  }

  Future<void> _onStarted(
    AuthStarted event,
    Emitter<AuthState> emit,
  ) async {
    await tryWrapperAsync(
      () async {
        try {
          emit(const AuthLoading());
          final user = _authRepository.getCurrentUser();
          if (user != null) {
            // When starting up, set hasAttemptedSignIn to false
            emit(Authenticated(userModel: user, hasAttemptedSignIn: false));
          } else {
            emit(const Unauthenticated());
          }
        } on AuthenticationException catch (error) {
          emit(AuthError(AuthenticationException(message: error.message)));
          throw AuthenticationException(message: 'Some error occurred!');
        }
      },
      event.context,
    );
  }

  Future<void> _onSignInWithGoogle(
    SignInWithGoogle event,
    Emitter<AuthState> emit,
  ) async {
    await tryWrapperAsync(
      () async {
        try {
          emit(const AuthLoading());
          final result = await _authRepository.signInWithGoogle();

          if (result.success && result.user != null) {
            emit(
              Authenticated(
                userModel: result.user!,
                hasAttemptedSignIn: true,
              ),
            );
            if (event.context.mounted) {
              event.context.read<RegistrationCubit>().updateUserDetails(
                    result.user!.id,
                    result.user!.email ?? '',
                    result.user!.photoUrl ?? '',
                  );
            }
          } else {
            emit(
              AuthError(
                AuthenticationException(
                  message: result.errorMessage ?? 'Google sign in failed',
                ),
              ),
            );
            throw AuthenticationException(message: 'Google sign in failed');
          }
        } on AuthenticationException catch (error) {
          emit(AuthError(AuthenticationException(message: error.message)));
          AppLogger.error(error.message);
        }
      },
      event.context,
    );
  }

  Future<void> _onSignInWithApple(
    SignInWithApple event,
    Emitter<AuthState> emit,
  ) async {
    await tryWrapperAsync(
      () async {
        try {
          emit(const AuthLoading());
          final result = await _authRepository.signInWithApple();

          if (result.success && result.user != null) {
            emit(
              Authenticated(
                userModel: result.user!,
                hasAttemptedSignIn: true,
              ),
            );
            if (event.context.mounted) {
              event.context.read<RegistrationCubit>().updateUserDetails(
                    result.user!.id,
                    result.user!.email ?? '',
                    result.user!.photoUrl ?? noProfileImageUrl,
                  );
              // event.context.read<RegistrationCubit>().updateFullName(
              //       result.user!.fullName ?? result.user!.displayName ?? 'User',
              //     );
            }
          } else {
            emit(
              AuthError(
                AuthenticationException(
                  message: result.errorMessage ?? 'Apple sign in failed',
                ),
              ),
            );
            throw AuthenticationException(message: 'Apple sign in failed');
          }
        } on AuthenticationException catch (error) {
          emit(AuthError(AuthenticationException(message: error.message)));
          AppLogger.error(error.message);
        }
      },
      event.context,
    );
  }

  Future<void> _onSignOut(
    SignOut event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(const AuthLoading());
      await _authRepository.signOut();
      emit(const Unauthenticated());
    } on AuthenticationException catch (error) {
      emit(AuthError(AuthenticationException(message: error.message)));
      AppLogger.error(error.message);
      throw AuthenticationException(message: 'Error while signing out');
    }
  }

  Future<void> _onAuthStateChanged(
    AuthStateChanged event,
    Emitter<AuthState> emit,
  ) async {
    if (event.user != null) {
      final currentState = state;
      final hasAttempted =
          currentState is Authenticated && currentState.hasAttemptedSignIn;

      emit(
        Authenticated(
          userModel: event.user!,
          hasAttemptedSignIn: hasAttempted,
        ),
      );
    } else {
      emit(const Unauthenticated());
    }
  }

  @override
  Future<void> close() {
    _authStateSubscription.cancel();
    return super.close();
  }

  Future<void> _onPhoneSignIn(
    PhoneSignIn event,
    Emitter<AuthState> emit,
  ) async {
    await tryWrapperAsync(
      () async {
        try {
          emit(const AuthLoading());

          final verificationId =
              await _authRepository.phoneSignIn(event.phoneNumber);

          if (verificationId != null) {
            emit(PhoneVerificationSent(verificationId: verificationId));
          } else {
            // Auto-verification completed
            emit(const PhoneVerified());
          }
        } on FirebaseAuthException catch (e) {
          String errorMessage;
          switch (e.code) {
            case 'invalid-phone-number':
              errorMessage = 'Invalid phone number format';
            case 'too-many-requests':
              errorMessage = 'Too many attempts. Please try again later';
            default:
              errorMessage = 'Authentication failed. Please try again';
          }
          emit(AuthError(AuthenticationException(message: errorMessage)));

          throw AuthenticationException(message: errorMessage);
        } catch (e) {
          emit(
            AuthError(
              AuthenticationException(message: 'Unexpected error occurred'),
            ),
          );
          throw AuthenticationException(message: 'Unexpected error occurred');
        }
      },
      event.context,
    );
  }

  Future<void> _onVerifyOTP(VerifyOTP event, Emitter<AuthState> emit) async {
    await tryWrapperAsync(
      () async {
        try {
          emit(const AuthLoading());
          final success = await _authRepository.verifyOTP(
            event.verificationId,
            event.smsCode,
            event.context.read<RegistrationCubit>().state.phoneNumber,
          );

          if (success) {
            final user = _authRepository.getCurrentUser();
            if (user != null) {
              emit(const PhoneVerified());
            } else {
              emit(
                AuthError(
                  AuthenticationException(message: 'Authentication failed.'),
                ),
              );
              throw AuthenticationException(
                message:
                    'We encountered with some problem. Please try again later!',
              );
            }
          }
        } on FirebaseAuthException catch (e) {
          final errorMessage = switch (e.code) {
            'invalid-verification-code' => 'Invalid OTP. Please try again',
            'provider-already-linked' ||
            'credential-already-in-use' ||
            'account-exists-with-different-credential' =>
              'Phone number is already linked to another account.',
            _ => 'Phone verification failed: ${e.message}'
          };

          emit(AuthError(AuthenticationException(message: errorMessage)));
          throw AuthenticationException(message: errorMessage);
        }
      },
      event.context,
    );
  }
}
