part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthStarted extends AuthEvent {
  final BuildContext context;
  const AuthStarted(this.context);
}

class SignInWithGoogle extends AuthEvent {
  final BuildContext context;
  const SignInWithGoogle(this.context);
}

class SignInWithApple extends AuthEvent {
  final BuildContext context;
  const SignInWithApple(this.context);
}

class VerifyPhoneNumber extends AuthEvent {
  const VerifyPhoneNumber();
}

class SignInWithPhoneNumber extends AuthEvent {
  const SignInWithPhoneNumber();
}

class SignOut extends AuthEvent {
  const SignOut();
}

class AuthStateChanged extends AuthEvent {
  final AuthUser? user;
  const AuthStateChanged({required this.user});

  @override
  List<Object?> get props => [];
}

class PhoneSignIn extends AuthEvent {
  final BuildContext context;
  final String phoneNumber;

  const PhoneSignIn(this.phoneNumber, this.context);
}

class VerifyOTP extends AuthEvent {
  final String verificationId;
  final String smsCode;
  final BuildContext context;

  const VerifyOTP({
    required this.verificationId,
    required this.smsCode,
    required this.context,
  });
}
