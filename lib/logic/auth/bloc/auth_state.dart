part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class Authenticated extends AuthState {
  final AuthUser userModel;
  final bool hasAttemptedSignIn; // Add this flag

  const Authenticated({
    required this.userModel,
    this.hasAttemptedSignIn = false, // Default to false
  });

  @override
  List<Object> get props => [userModel, hasAttemptedSignIn];
}

class Unauthenticated extends AuthState {
  const Unauthenticated();
}

class AuthError extends AuthState {
  final AppException error;
  const AuthError(this.error);

  @override
  List<Object> get props => [error];
}

class PhoneVerificationSent extends AuthState {
  final String verificationId;

  const PhoneVerificationSent({required this.verificationId});
}

class PhoneVerified extends AuthState {
  const PhoneVerified();
}
