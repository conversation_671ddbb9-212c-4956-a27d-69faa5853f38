part of 'perks_cubit_cubit.dart';

class PerksState {
  final Map<String, Map<String, dynamic>> perks;
  final bool isLoading;
  final String? error;

  const PerksState({
    this.perks = const {},
    this.isLoading = false,
    this.error,
  });

  PerksState copyWith({
    Map<String, Map<String, dynamic>>? perks,
    bool? isLoading,
    String? error,
  }) {
    return PerksState(
      perks: perks ?? this.perks,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}
