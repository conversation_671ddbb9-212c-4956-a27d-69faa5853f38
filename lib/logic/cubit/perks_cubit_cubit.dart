import 'package:bloc/bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/promoter/bloc/promoter_repo_impl.dart';
import 'package:vibeo/models/offer/offer_model.dart';

part 'perks_cubit_state.dart';

class PerksCubit extends Cubit<PerksState> {
  final FeedRepository _feedRepository = FeedRepository();
  final PromoterRepository _promoterRepository = PromoterRepository();

  PerksCubit() : super(const PerksState());

  Future<void> fetchPerks({
    required String venueId,
    required String userId,
    required String userEmail,
  }) async {
    if (state.perks.containsKey(venueId)) {
      return;
    }

    try {
      emit(state.copyWith(isLoading: true));

      final fetchedPerks = await _feedRepository.fetchPerks(
        venueId: venueId,
        userId: userId,
        userEmail: userEmail,
      );

      final promoters = await _promoterRepository.fetchVenuePromoters(venueId);

      final updatedPerks = Map<String, Map<String, dynamic>>.from(state.perks);
      updatedPerks[venueId] = {
        'offers': fetchedPerks['offers']['data'] as List<OfferModel>,
        'promoters': promoters,
      };

      emit(
        state.copyWith(
          perks: updatedPerks,
          isLoading: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to fetch perks: $e',
        ),
      );
    }
  }

  void addPerks({
    required String venueId,
    required Map<String, dynamic> venuePerks,
  }) {
    final updatedPerks = Map<String, Map<String, dynamic>>.from(state.perks);
    updatedPerks[venueId] = venuePerks;
    emit(state.copyWith(perks: updatedPerks));
  }
}
