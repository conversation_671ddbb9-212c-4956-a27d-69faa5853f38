part of 'content_bloc.dart';

abstract class ContentEvent extends Equatable {
  const ContentEvent();

  @override
  List<Object> get props => [];
}

class UpdateFeedDetailsEvent extends ContentEvent {
  final Map<String, dynamic> details;

  const UpdateFeedDetailsEvent(this.details);

  @override
  List<Object> get props => [details];
}

class ResetContentEvent extends ContentEvent {
  const ResetContentEvent();
}

class UploadFeedEvent extends ContentEvent {
  final bool isPartner;
  const UploadFeedEvent({this.isPartner = false});
}

class OnFeedCanceled extends ContentEvent {}

class SetVideoPathEvent extends ContentEvent {
  final String videoPath;

  const SetVideoPathEvent(this.videoPath);

  @override
  List<Object> get props => [videoPath];
}
