part of 'content_bloc.dart';

class ContentState extends Equatable {
  final List<String>? areas;
  const ContentState({this.areas});
  @override
  List<Object> get props => [];
}

class ContentInitial extends ContentState {
  const ContentInitial({super.areas});
}

class FeedUploading extends ContentState {
  final double progress;

  const FeedUploading(this.progress, {super.areas});

  @override
  List<Object> get props => [progress];
}

class FeedSuccess extends ContentState {
  const FeedSuccess({super.areas});
}

class FeedCanceled extends ContentState {
  const FeedCanceled({super.areas});
}

class FeedError extends ContentState {
  final String message;

  const FeedError(this.message, {super.areas});

  @override
  List<Object> get props => [message];
}
