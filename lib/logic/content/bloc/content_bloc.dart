import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/helper/parse_double.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/storage/feed_upload_rate_logic.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/services/feed/video_uploader.dart';
import 'package:vibeo/utils/utils.dart';

part 'content_event.dart';
part 'content_state.dart';

class ContentBloc extends Bloc<ContentEvent, ContentState> {
  final VideoUploader videoUploader = VideoUploader();
  final FeedRepository _feedRepository = FeedRepository();
  FeedModel? _currentFeed;
  String? currentVideoPath;
  ContentBloc() : super(const ContentInitial()) {
    on<ResetContentEvent>((event, emit) => emit(const ContentInitial()));
    on<UpdateFeedDetailsEvent>(_onUpdateFeedDetails);
    on<UploadFeedEvent>(_onUploadFeed);
    on<OnFeedCanceled>(_resetBloc);
    on<SetVideoPathEvent>(_onSetVideoPath);
  }

  void _onSetVideoPath(SetVideoPathEvent event, Emitter<ContentState> emit) {
    AppLogger.info('Setting video path: ${event.videoPath}');
    if (event.videoPath.isNotEmpty) {
      currentVideoPath = event.videoPath;
    } else {
      AppLogger.error('Empty video path provided');
    }
  }

  bool get isShazamError => _currentFeed?.shazamError ?? false;

  Future<void> _onUpdateFeedDetails(
    UpdateFeedDetailsEvent event,
    Emitter<ContentState> emit,
  ) async {
    try {
      AppLogger.info('Updating feed details: ${event.details}');
      // Create a new FeedModel if _currentFeed is null
      if (_currentFeed == null) {
        _currentFeed = FeedModel(
          id: event.details['id']?.toString() ?? '',
          googlePlaceID: event.details['googlePlaceID']?.toString() ?? '',
          venueName: event.details['venueName']?.toString() ?? 'Null',
          address: event.details['address']?.toString() ?? 'Null',
          coverCharge: event.details['coverCharge']?.toString() ?? 'null',
          usp: event.details['usp']?.toString() ?? '',
          liveMusic: event.details['liveMusic'] as bool? ?? false,
          vibeScore: parseInt(event.details['vibeScore']) ?? 3,
          venueType: event.details['venueType']?.toString() ?? '',
          location: Location(
            latitude:
                double.tryParse(event.details['latitude']?.toString() ?? '0') ??
                    0.0,
            longitude: double.tryParse(
                  event.details['longitude']?.toString() ?? '0',
                ) ??
                0.0,
          ),
          userID: event.details['userID']?.toString() ?? '',
          area: event.details['area']?.toString() ?? '',
          googleArea: event.details['googleArea']?.toString() ?? '',
        );
      } else {
        // Update existing FeedModel
        _currentFeed = _currentFeed!.copyWith(
          id: event.details['id']?.toString() ?? '',
          googlePlaceID: event.details['googlePlaceID']?.toString() ?? '',
          venueName: event.details['venueName']?.toString() ?? 'Null',
          address: event.details['address']?.toString() ?? 'Null',
          coverCharge: event.details['coverCharge']?.toString() ?? 'null',
          usp: event.details['usp']?.toString() ?? '',
          liveMusic: event.details['liveMusic'] as bool? ?? false,
          vibeScore: parseInt(event.details['vibeScore']) ?? 3,
          venueType: event.details['venueType']?.toString() ?? '',
          location: Location(
            latitude:
                double.tryParse(event.details['latitude']?.toString() ?? '0') ??
                    0.0,
            longitude: double.tryParse(
                  event.details['longitude']?.toString() ?? '0',
                ) ??
                0.0,
          ),
          userID: event.details['userID']?.toString() ?? '',
          area: event.details['area']?.toString() ?? '',
          googleArea: event.details['googleArea']?.toString() ?? '',
        );
      }
      AppLogger.info('Updated feed: $_currentFeed');
    } catch (e) {
      await Sentry.captureException(e);
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onUploadFeed(
    UploadFeedEvent event,
    Emitter<ContentState> emit,
  ) async {
    AppLogger.info('Uploading feed: $_currentFeed');
    if (_currentFeed == null) {
      AppLogger.error(
        '_currentFeed is null in _onUploadFeed. This should not happen after UpdateFeedDetailsEvent.',
      );
      emit(const FeedError('No feed to upload'));
      await Sentry.captureException(Exception('No feed to upload'));
      return;
    }

    if (!_validateFeedFields()) {
      emit(const FeedError('Required fields are missing'));
      await Sentry.captureException(Exception('Required fields are missing'));
      return;
    }

    if (currentVideoPath == null) {
      AppLogger.error(
        'currentVideoPath is null in _onUploadFeed. Video path must be set before uploading.',
      );
      emit(const FeedError('No video to upload'));
      await Sentry.captureException(Exception('No video to upload'));
      return;
    }

    emit(const FeedUploading(0));

    try {
      // Video upload - 80% of total progress
      final result = await videoUploader.uploadVideo(
        _currentFeed!.id,
        currentVideoPath,
        (progress) => emit(FeedUploading(progress * 0.8)),
      );

      // Update feed with URLs
      _currentFeed = _currentFeed!.copyWith(
        videoURL: result!['videoURL']!,
      );

      AppLogger.info('Updated WITH URL feed: $_currentFeed');

      // Feed upload - remaining 20%
      emit(const FeedUploading(0.9));
      final bool uploaded =
          await _feedRepository.uploadFeed(_currentFeed!, currentVideoPath!);

      if (!uploaded) {
        emit(const FeedError('Failed to upload feed'));
        return;
      } else {
        if (event.isPartner) {
          await FeedRateLimiter().recordFeedUpload(
            _currentFeed!.userID,
            _currentFeed!.id,
          );
        }
      }

      emit(const FeedUploading(1));
      emit(const FeedSuccess());
      _resetFeed();
    } catch (e) {
      await Sentry.captureException(e);
      emit(FeedError(e.toString()));
    }
  }

  bool _validateFeedFields() {
    return _currentFeed!.id.isNotEmpty &&
        _currentFeed!.venueName.isNotEmpty &&
        _currentFeed!.address.isNotEmpty &&
        _currentFeed!.userID.isNotEmpty &&
        _currentFeed!.location != null;
  }

  Future<void> _resetBloc(
    OnFeedCanceled event,
    Emitter<ContentState> emit,
  ) async {
    _currentFeed = null;
    currentVideoPath = null;
    emit(const FeedCanceled());
  }

  void _resetFeed() {
    _currentFeed = null;
    currentVideoPath = null;
  }
}
