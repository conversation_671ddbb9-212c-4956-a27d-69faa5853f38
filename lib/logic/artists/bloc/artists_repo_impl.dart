import 'package:vibeo/models/artists/artist_model.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class ArtistsRepository {
  final NetworkServicesApi _api;

  ArtistsRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<List<ArtistModel>> fetchArtists() async {
    try {
      final response = await _api.getAPI<List<dynamic>>(
        '/v1/user/artists',
        (json) => json['data'] as List<dynamic>,
      );

      return response
          .map((artist) {
            try {
              final Map<String, dynamic> artistMap =
                  artist as Map<String, dynamic>;
              artistMap['tags'] ??= ['Pop', 'Rock', 'Jazz'];
              return ArtistModel.fromJson(artistMap);
            } catch (e) {
              AppLogger.error('Error parsing artist: $e');
              return null;
            }
          })
          .where((artist) => artist != null)
          .cast<ArtistModel>()
          .toList();
    } catch (e) {
      AppLogger.error('Failed to fetch artists: $e');
      throw Exception('Failed to fetch artists');
    }
  }

  Future<List<OfferModel>> fetchUserSavedOffers(
    List<String> offerIds, {
    int limit = 10,
  }) async {
    try {
      final response = await _api.postAPI<List<dynamic>>(
        '/v1/offers/saved',
        {
          'offerIds': offerIds,
          'limit': limit,
        },
        (json) => json['data'] as List<dynamic>,
      );
      final result = response
          .map((offer) {
            try {
              return OfferModel.fromJson(offer as Map<String, dynamic>);
            } catch (e) {
              AppLogger.error('Error parsing offer: $e');
              return null;
            }
          })
          .where((offer) => offer != null)
          .cast<OfferModel>()
          .toList();

      return result;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch saved offers: $e');
      return [];
    }
  }
}
