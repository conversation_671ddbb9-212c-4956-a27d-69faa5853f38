part of 'artists_bloc.dart';

abstract class ArtistState extends Equatable {
  final List<ArtistModel> artists;
  const ArtistState(this.artists);

  @override
  List<Object?> get props => [];
}

class ArtistsInitial extends ArtistState {
  const ArtistsInitial(super.artists);
}

class ArtistsLoading extends ArtistState {
  const ArtistsLoading(super.artists);
}

class ArtistsLoaded extends ArtistState {
  final List<ArtistModel> artistsLoaded;

  const ArtistsLoaded({
    this.artistsLoaded = const [],
  }) : super(artistsLoaded);

  @override
  List<Object?> get props => [artists];

  ArtistsLoaded copyWith({
    List<ArtistModel>? artists,
  }) {
    return ArtistsLoaded(
      artistsLoaded: artists ?? this.artists,
    );
  }
}

class ArtistsError extends ArtistState {
  final String message;

  const ArtistsError(this.message, super.artists);

  @override
  List<Object?> get props => [message];
}
