import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/logic/artists/bloc/artists_repo_impl.dart';

import 'package:vibeo/models/artists/artist_model.dart';
import 'package:vibeo/utils/exceptions/try_wrapper.dart';

part 'artists_event.dart';
part 'artists_state.dart';

class ArtistBloc extends Bloc<ArtistEvent, ArtistState> {
  final ArtistsRepository _artistRepository = ArtistsRepository();

  ArtistBloc() : super(const ArtistsInitial([])) {
    on<FetchArtistsEvent>(_onFetchArtists);
  }

  Future<void> _onFetchArtists(
    FetchArtistsEvent event,
    Emitter<ArtistState> emit,
  ) async {
    await tryWrapperAsync(
      () async {
        if (state is! ArtistsLoaded) {
          emit(ArtistsLoading(state.artists));
        }

        try {
          final artists = await _artistRepository.fetchArtists();
          emit(ArtistsLoaded(artistsLoaded: artists));
        } catch (e) {
          if (state is ArtistsLoaded) {
            emit(state);
          } else {
            emit(ArtistsError(e.toString(), state.artists));
          }
        }
        return;
      },
      event.context,
    );
  }
}
