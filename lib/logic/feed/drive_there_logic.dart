import 'package:clipboard/clipboard.dart';
import 'package:flutter/cupertino.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:io' show Platform;

Future<void> openUber(String address, {BuildContext? context}) async {
  final encodedAddress = Uri.encodeComponent(address);
  await FlutterClipboard.copy(address);
  if (context!.mounted) {
    await showCupertinoDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        Future.delayed(const Duration(seconds: 2), () {
          if (context.mounted) {
            Navigator.of(context).pop();
          }
        });

        return const CupertinoAlertDialog(
          title: Text(
            'Address copied to clipboard',
            style: TextStyle(fontSize: 15),
          ),
          content: Text('Opening Uber...'),
        );
      },
    ).then((_) async {
      final appUrl = Uri.parse('uber://?action=setPickup&pickup=my_location'
          '&dropoff[formatted_address]=$encodedAddress');

      final webUrl = Uri.parse('https://m.uber.com/ul/?action=setPickup'
          '&pickup=my_location'
          '&nickName=&dropoff[formatted_address]=$encodedAddress');

      try {
        if (await canLaunchUrl(appUrl)) {
          await launchUrl(appUrl, mode: LaunchMode.externalApplication);
        } else {
          await launchUrl(webUrl, mode: LaunchMode.externalApplication);
        }
      } catch (e) {
        await launchUrl(webUrl, mode: LaunchMode.externalApplication);
      }
    });
  }
}

// 2. Open Lyft with address
Future<void> openLyft(String address) async {
  final encodedAddress = Uri.encodeComponent(address);
  final appUrl =
      Uri.parse('lyft://ridetype?id=lyft&destination[address]=$encodedAddress');
  final webUrl = Uri.parse(
    'https://lyft.com/ride?id=lyft&destination[address]=$encodedAddress',
  );

  try {
    if (await canLaunchUrl(appUrl)) {
      await launchUrl(appUrl, mode: LaunchMode.externalApplication);
    } else {
      await launchUrl(webUrl, mode: LaunchMode.externalApplication);
    }
  } catch (e) {
    await launchUrl(webUrl, mode: LaunchMode.externalApplication);
  }
}

// 3. Open Google Maps with address
Future<void> openGoogleMaps(String address) async {
  final encodedAddress = Uri.encodeComponent(address);
  final appUrl = Uri.parse(
    'https://www.google.com/maps/dir/?api=1&destination=$encodedAddress',
  );

  try {
    await launchUrl(appUrl, mode: LaunchMode.externalApplication);
  } catch (e) {
    final webUrl = Uri.parse('https://maps.google.com/?q=$encodedAddress');
    await launchUrl(webUrl, mode: LaunchMode.externalApplication);
  }
}

// 4. Open Apple Maps with address
Future<void> openAppleMaps(String address) async {
  final encodedAddress = Uri.encodeComponent(address);
  final appUrl = Uri.parse('maps://?daddr=$encodedAddress');
  final webUrl = Uri.parse('https://maps.apple.com/?daddr=$encodedAddress');

  try {
    if (Platform.isIOS) {
      if (await canLaunchUrl(appUrl)) {
        await launchUrl(appUrl, mode: LaunchMode.externalApplication);
      } else {
        await launchUrl(webUrl, mode: LaunchMode.externalApplication);
      }
    } else {
      await launchUrl(webUrl, mode: LaunchMode.externalApplication);
    }
  } catch (e) {
    await launchUrl(webUrl, mode: LaunchMode.externalApplication);
  }
}

// 5. Universal opener with platform detection
Future<void> openNavigationApp(String address) async {
  if (Platform.isIOS) {
    // iOS priority: Apple Maps -> Google Maps -> Uber -> Lyft
    if (await canLaunchUrl(Uri.parse('maps://'))) {
      await openAppleMaps(address);
    } else if (await canLaunchUrl(Uri.parse('comgooglemaps://'))) {
      await openGoogleMaps(address);
    } else if (await canLaunchUrl(Uri.parse('uber://'))) {
      await openUber(address);
    } else if (await canLaunchUrl(Uri.parse('lyft://'))) {
      await openLyft(address);
    } else {
      await openAppleMaps(address); // Fallback to Apple Maps web
    }
  } else {
    // Android priority: Google Maps -> Uber -> Lyft
    if (await canLaunchUrl(Uri.parse('comgooglemaps://'))) {
      await openGoogleMaps(address);
    } else if (await canLaunchUrl(Uri.parse('uber://'))) {
      await openUber(address);
    } else if (await canLaunchUrl(Uri.parse('lyft://'))) {
      await openLyft(address);
    } else {
      await openGoogleMaps(address); // Fallback to Google Maps web
    }
  }
}
