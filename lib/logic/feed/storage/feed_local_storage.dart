import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class FeedLocalStorage {
  static const _watchedFeedsKey = 'watched_feeds';
  static const _likedFeedsKey = 'liked_feeds';
  final _storage = const FlutterSecureStorage();
  final String userID;

  FeedLocalStorage(this.userID);

  String _getUserSpecificKey(String key) {
    return '${key}_$userID';
  }

  Future<void> saveWatchedFeed(String feedId) async {
    final key = _getUserSpecificKey(_watchedFeedsKey);
    final watched = await getWatchedFeeds();
    if (!watched.contains(feedId)) {
      watched.add(feedId);
      await _storage.write(
        key: key,
        value: jsonEncode(watched),
      );
    }
  }

  Future<List<String>> getWatchedFeeds() async {
    final key = _getUserSpecificKey(_watchedFeedsKey);
    final watchedStr = await _storage.read(key: key);
    if (watchedStr != null) {
      return List<String>.from(jsonDecode(watchedStr) as List);
    }
    return [];
  }

  Future<bool> toggleLikedFeed(String feedId) async {
    final key = _getUserSpecificKey(_likedFeedsKey);
    final liked = await getLikedFeeds();
    if (liked.contains(feedId)) {
      liked.remove(feedId);
    } else {
      liked.add(feedId);
    }
    await _storage.write(
      key: key,
      value: jsonEncode(liked),
    );
    return liked.contains(feedId);
  }

  Future<List<String>> getLikedFeeds() async {
    final key = _getUserSpecificKey(_likedFeedsKey);
    final likedStr = await _storage.read(key: key);
    if (likedStr != null) {
      return List<String>.from(jsonDecode(likedStr) as List);
    }
    return [];
  }

  Future<bool> isFeedLiked(String feedId) async {
    final liked = await getLikedFeeds();
    return liked.contains(feedId);
  }
}
