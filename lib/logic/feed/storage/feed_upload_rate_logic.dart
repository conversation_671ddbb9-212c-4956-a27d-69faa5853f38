import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:vibeo/models/user/user_model.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class UploadEntry {
  final String id;
  final DateTime timestamp;

  UploadEntry({required this.id, required this.timestamp});

  Map<String, dynamic> toJson() => {
        'id': id,
        'timestamp': timestamp.toIso8601String(),
      };

  factory UploadEntry.fromJson(Map<String, dynamic> json) {
    return UploadEntry(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }
}

class FeedRateLimiter {
  static const String _storageKey = 'feed_upload_history';
  static const Duration _windowDuration = Duration(hours: 4);
  final FlutterSecureStorage _secureStorage;

  FeedRateLimiter({FlutterSecureStorage? secureStorage})
      : _secureStorage = secureStorage ?? const FlutterSecureStorage();

  Future<Map<String, dynamic>> getRateLimitInfo(UserModel user) async {
    final remaining = await getRemainingUploads(user);
    final timeUntilNext = await getTimeUntilNextUpload(user);
    final canUpload = await canUploadFeed(user);

    return {
      'canUpload': canUpload,
      'remainingUploads': remaining < 0 ? 'Unlimited' : remaining,
      'timeUntilNextUpload': _formatDuration(Duration(seconds: timeUntilNext)),
      'isLimited': remaining == 0,
    };
  }

  /// Format duration to human readable string
  String _formatDuration(Duration duration) {
    if (duration.inSeconds <= 0) return '4 hours';

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;

    final parts = <String>[];
    if (hours > 0) parts.add('$hours hour${hours == 1 ? '' : 's'}');
    if (minutes > 0) parts.add('$minutes minute${minutes == 1 ? '' : 's'}');
    if (seconds > 0 && hours == 0) {
      parts.add('$seconds second${seconds == 1 ? '' : 's'}');
    }

    return parts.join(', ');
  }

  Future<bool> canUploadFeed(UserModel user) async {
    if (user.feedRateLimit == null || user.feedRateLimit! <= 0) {
      // User has unlimited uploads
      return true;
    }

    final uploadCount = await _getRecentUploadCount(user.uid);
    return uploadCount < user.feedRateLimit!;
  }

  Future<bool> recordFeedUpload(String userId, String feedId) async {
    try {
      final history = await _getUploadHistory(userId);

      // Add new entry
      history.add(
        UploadEntry(
          id: feedId,
          timestamp: DateTime.now(),
        ),
      );

      // Save updated history
      await _saveUploadHistory(userId, history);
      return true;
    } catch (e) {
      AppLogger.error('Failed to record feed upload: $e');
      return false;
    }
  }

  Future<int> _getRecentUploadCount(String userId) async {
    final history = await _getUploadHistory(userId);
    final windowStart = DateTime.now().subtract(_windowDuration);

    // Filter entries to only include those within the window
    final recentUploads =
        history.where((entry) => entry.timestamp.isAfter(windowStart)).toList();

    return recentUploads.length;
  }

  Future<int> getTimeUntilNextUpload(UserModel user) async {
    // if (await canUploadFeed(user)) {
    //   return 0;
    // }

    final history = await _getUploadHistory(user.uid);
    if (history.isEmpty) {
      return 0;
    }

    // Sort history by timestamp (oldest first)
    history.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    // Find the oldest upload within the window
    final windowStart = DateTime.now().subtract(_windowDuration);
    final oldestInWindow = history.firstWhere(
      (entry) => entry.timestamp.isAfter(windowStart),
      orElse: () => history.last,
    );

    // Calculate when this entry will expire
    final expiryTime = oldestInWindow.timestamp.add(_windowDuration);
    final secondsRemaining = expiryTime.difference(DateTime.now()).inSeconds;

    return secondsRemaining > 0 ? secondsRemaining : 0;
  }

  Future<List<UploadEntry>> _getUploadHistory(String userId) async {
    try {
      final jsonStr = await _secureStorage.read(key: '${_storageKey}_$userId');
      if (jsonStr == null) {
        return [];
      }

      final List<dynamic> jsonList = json.decode(jsonStr) as List<dynamic>;
      final entries = jsonList
          .map((item) => UploadEntry.fromJson(item as Map<String, dynamic>))
          .toList();

      // Clean up old entries (older than window duration)
      final windowStart = DateTime.now().subtract(_windowDuration);
      return entries
          .where((entry) => entry.timestamp.isAfter(windowStart))
          .toList();
    } catch (e) {
      AppLogger.error('Error retrieving upload history: $e');
      return [];
    }
  }

  /// Saves the upload history for a specific user.
  Future<void> _saveUploadHistory(
    String userId,
    List<UploadEntry> history,
  ) async {
    try {
      // Clean up old entries before saving
      final windowStart = DateTime.now().subtract(_windowDuration);
      final recentHistory = history
          .where((entry) => entry.timestamp.isAfter(windowStart))
          .toList();

      final jsonList = recentHistory.map((entry) => entry.toJson()).toList();
      final jsonStr = json.encode(jsonList);

      await _secureStorage.write(key: '${_storageKey}_$userId', value: jsonStr);
    } catch (e) {
      AppLogger.error('Error saving upload history: $e');
      throw Exception('Failed to save upload history');
    }
  }

  Future<void> clearHistory(String userId) async {
    await _secureStorage.delete(key: '${_storageKey}_$userId');
  }

  Future<bool> removeFeedFromHistory(String userId, String feedId) async {
    try {
      final history = await _getUploadHistory(userId);

      // Find the specific upload entry
      final initialLength = history.length;

      // Remove all instances matching the feedId (should typically be just one)
      history.removeWhere((entry) => entry.id == feedId);

      // Check if anything was removed
      final wasRemoved = history.length < initialLength;

      if (wasRemoved) {
        await _saveUploadHistory(userId, history);
        AppLogger.info(
          'Removed feed $feedId from upload history for user $userId',
        );
      } else {
        AppLogger.info(
          'Feed $feedId not found in upload history for user $userId',
        );
      }

      return wasRemoved;
    } catch (e) {
      AppLogger.error('Failed to remove feed from history: $e');
      return false;
    }
  }

  Future<int> getRemainingUploads(UserModel user) async {
    if (user.feedRateLimit == null) return 10;

    final used = await _getRecentUploadCount(user.uid);
    return user.feedRateLimit! - used;
  }
}
