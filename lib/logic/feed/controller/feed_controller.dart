import 'package:vibeo/logic/feed/storage/feed_local_storage.dart';

class FeedController {
  final String userID;
  late final FeedLocalStorage _storage;

  FeedController(this.userID) {
    _storage = FeedLocalStorage(userID);
  }

  Future<void> onFeedViewed(String feedId) async {
    await _storage.saveWatchedFeed(feedId);
  }

  Future<void> onFeedLikePressed(String feedId) async {
    await _storage.toggleLikedFeed(feedId);
  }

  Future<bool> checkIfFeedLiked(String feedId) async {
    final liked = await _storage.isFeedLiked(feedId);
    return liked;
  }

  Future<int> vibesWatched() async {
    final watched = await _storage.getWatchedFeeds();
    return watched.length;
  }

  Future<int> vibesLiked() async {
    final liked = await _storage.getLikedFeeds();
    return liked.length;
  }

  Future<List<String>> listVibesLiked() async {
    final liked = await _storage.getLikedFeeds();
    return liked;
  }
}
