import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:vibeo/models/feed/previous_content.dart';

class FeedSecureStorage {
  static const _previousContentKey = 'previous_content';
  final _storage = const FlutterSecureStorage();

  Future<void> savePreviousContent(PreviousContent content) async {
    await _storage.write(
      key: _previousContentKey,
      value: jsonEncode(content.toJson()),
    );
  }

  Future<PreviousContent?> getPreviousContent() async {
    final contentStr = await _storage.read(key: _previousContentKey);
    if (contentStr != null) {
      return PreviousContent.fromJson(
        jsonDecode(contentStr) as Map<String, dynamic>,
      );
    }
    return null;
  }
}
