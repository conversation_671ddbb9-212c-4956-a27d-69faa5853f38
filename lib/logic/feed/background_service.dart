// import 'dart:async';
// import 'dart:ui';
// import 'package:flutter_background_service/flutter_background_service.dart';
// import 'package:vibeo/logic/content/bloc/content_bloc.dart';
// import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
// import 'package:vibeo/services/feed/video_uploader.dart';

// @pragma('vm:entry-point')
// Future<void> backgroundServiceEntrypoint(ServiceInstance service) async {
//   DartPluginRegistrant.ensureInitialized();

//   service.on('uploadFeed').listen((event) async {
//     if (event != null) {
//       ContentBloc(
//         videoUploader: VideoUploader(),
//         feedRepository: FeedRepository(),
//       )

//           // Trigger the upload process
//           .add(UploadFeedEvent());
//     }
//   });
// }

// Future<void> initBackgroundService() async {
//   final service = FlutterBackgroundService();

//   await service.configure(
//     androidConfiguration: AndroidConfiguration(
//       onStart: backgroundServiceEntrypoint,
//       autoStart: false,
//       isForegroundMode: true,
//     ),
//     iosConfiguration: IosConfiguration(
//       autoStart: false,
//       onForeground: backgroundServiceEntrypoint,
//       onBackground: onIosBackground,
//     ),
//   );
// }

// @pragma('vm:entry-point')
// Future<bool> onIosBackground(ServiceInstance service) async {
//   return true;
// }
