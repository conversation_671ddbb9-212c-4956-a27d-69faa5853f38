import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/models/models.dart';

class FeedBloc extends Bloc<FeedEvent, FeedState> {
  final FeedRepository _feedRepository = FeedRepository();
  List<Map<String, dynamic>> areas = [];
  List<String> scenes = [];
  List<Map<String, String>> beoTypes = [];
  List<Map<String, String>> vibeTypes = [];

  List<Map<String, dynamic>> get getAreas => areas;
  List<String> get getScenes => scenes;
  List<Map<String, String>> get getbeoTypes => beoTypes;
  List<Map<String, String>> get getVibeTypes => vibeTypes;

  set setAreas(List<Map<String, dynamic>> value) {
    areas = value;
  }

  set setScenes(List<String> value) {
    scenes = value;
  }

  set setBeoTypes(List<Map<String, String>> value) {
    beoTypes = value;
  }

  set setVibeTypes(List<Map<String, String>> value) {
    vibeTypes = value;
  }

  FeedsLoaded _loadedState = const FeedsLoaded(
    liveVibes: null,
    musicGenreFeeds: {},
    nearbyFeeds: [],
    areaFeeds: {},
    talkVibes: [],
  );

  FeedBloc() : super(const FeedInitial()) {
    on<FetchMusicGenreFeedsEvent>(_onFetchMusicGenreFeeds);
    on<FetchNearbyFeedsEvent>(_onFetchNearbyFeeds);
    on<FetchAreaFeedsEvent>(_onFetchAreaFeeds);
    on<FetchSceneFeedsEvent>(_onFetchSceneFeeds);
    on<FetchBeoAIFeedsEvent>(_onFetchBeoAIFeeds);
    on<FetchVibeTypeFeedsEvent>(_onFetchVibeTypeFeeds);
    on<FetchTalkVibesEvent>(_onFetchTalkVibes);
    on<FetchDanceVibesEvent>(_onFetchDanceVibes);
    on<FetchLiveFeedsEvent>(_onFetchLiveFeeds);
    // on<FetchVibeScoreFeedsEvent>(_onFetchVibeScoreFeeds);
  }

  final Map<String, int> _totalFetchedCounts = {
    'nearby': 0,
  };

  Future<void> _onFetchAreaFeeds(
    FetchAreaFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.areaFeeds.containsKey(event.area) &&
        _loadedState.areaFeeds[event.area]!.isNotEmpty &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(byAreaLoading: true));

    try {
      final result = await _feedRepository.fetchAreaFeeds(
        event.area,
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          final List<VibeFeedModel> updatedFeeds = [
            ...(_loadedState.areaFeeds[event.area] ?? []),
            VibeFeedModel.endMarker(),
          ];
          _loadedState = _loadedState.copyWith(
            areaFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.areaFeeds,
            )..[event.area] = updatedFeeds,
            byAreaTitle: _loadedState.byAreaTitle,
            byAreaLoading: false,
          );
        } else {
          _loadedState = _loadedState.copyWith(
            areaFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.areaFeeds,
            )..[event.area] = [],
            byAreaTitle: null,
            byAreaLoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.areaFeeds[event.area]!,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          areaFeeds: Map<String, List<VibeFeedModel>>.from(
            _loadedState.areaFeeds,
          )..[event.area] = updatedFeeds,
          byAreaTitle: result['title'] as String?,
          byAreaLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchSceneFeeds(
    FetchSceneFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.sceneFeeds.containsKey(event.scene) &&
        _loadedState.sceneFeeds[event.scene]!.isNotEmpty &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(sceneLoading: true));

    try {
      final result = await _feedRepository.fetchSceneFeeds(
        event.scene,
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          final List<VibeFeedModel> updatedFeeds = [
            ...(_loadedState.sceneFeeds[event.scene] ?? []),
            VibeFeedModel.endMarker(),
          ];
          _loadedState = _loadedState.copyWith(
            sceneFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.sceneFeeds,
            )..[event.scene] = updatedFeeds,
            sceneTitle: _loadedState.sceneTitle,
            sceneLoading: false,
          );
        } else {
          _loadedState = _loadedState.copyWith(
            sceneFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.sceneFeeds,
            )..[event.scene] = [],
            sceneTitle: null,
            sceneLoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.sceneFeeds[event.scene]!,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          sceneFeeds: Map<String, List<VibeFeedModel>>.from(
            _loadedState.sceneFeeds,
          )..[event.scene] = updatedFeeds,
          sceneTitle: result['title'] as String?,
          sceneLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchBeoAIFeeds(
    FetchBeoAIFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.beoAIFeeds.containsKey(event.typeID) &&
        _loadedState.beoAIFeeds[event.typeID]!.isNotEmpty &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(beoAILoading: true));

    try {
      final result = await _feedRepository.fetchBeoTypeFeeds(
        event.typeID,
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          final List<VibeFeedModel> updatedFeeds = [
            ...(_loadedState.beoAIFeeds[event.typeID] ?? []),
            VibeFeedModel.endMarker(),
          ];
          _loadedState = _loadedState.copyWith(
            beoAIFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.beoAIFeeds,
            )..[event.typeID] = updatedFeeds,
            beoAITitle: _loadedState.beoAITitle,
            beoAILoading: false,
          );
        } else {
          _loadedState = _loadedState.copyWith(
            beoAIFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.beoAIFeeds,
            )..[event.typeID] = [],
            beoAITitle: null,
            beoAILoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.beoAIFeeds[event.typeID]!,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          beoAIFeeds: Map<String, List<VibeFeedModel>>.from(
            _loadedState.beoAIFeeds,
          )..[event.typeID] = updatedFeeds,
          beoAITitle: result['title'] as String?,
          beoAILoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchVibeTypeFeeds(
    FetchVibeTypeFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.vibeTypeFeeds.containsKey(event.vibeTypeID) &&
        _loadedState.vibeTypeFeeds[event.vibeTypeID]!.isNotEmpty &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(vibeTypeLoading: true));

    try {
      final result = await _feedRepository.fetchVibeTypeFeeds(
        event.vibeTypeID,
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          final List<VibeFeedModel> updatedFeeds = [
            ...(_loadedState.vibeTypeFeeds[event.vibeTypeID] ?? []),
            VibeFeedModel.endMarker(),
          ];
          _loadedState = _loadedState.copyWith(
            vibeTypeFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.vibeTypeFeeds,
            )..[event.vibeTypeID] = updatedFeeds,
            vibeTypeTitle: _loadedState.vibeTypeTitle,
            vibeTypeLoading: false,
          );
        } else {
          _loadedState = _loadedState.copyWith(
            vibeTypeFeeds: Map<String, List<VibeFeedModel>>.from(
              _loadedState.vibeTypeFeeds,
            )..[event.vibeTypeID] = [],
            vibeTypeTitle: null,
            vibeTypeLoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.vibeTypeFeeds[event.vibeTypeID]!,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          vibeTypeFeeds: Map<String, List<VibeFeedModel>>.from(
            _loadedState.vibeTypeFeeds,
          )..[event.vibeTypeID] = updatedFeeds,
          vibeTypeTitle: result['title'] as String?,
          vibeTypeLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchLiveFeeds(
    FetchLiveFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.liveVibes != null &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(liveLoading: true));

    try {
      final result = await _feedRepository.fetchLiveFeeds(
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          // If it's a pagination request, keep existing feeds and add end marker
          final List<VibeFeedModel> updatedFeeds = [
            ...(_loadedState.liveVibes ?? []),
            VibeFeedModel.endMarker(), // Create an end marker feed
          ];
          _loadedState = _loadedState.copyWith(
            liveVibes: updatedFeeds,
            liveTitle: _loadedState.liveTitle,
            liveLoading: false,
          );
        } else {
          // Initial load with no results
          _loadedState = _loadedState.copyWith(
            liveVibes: [],
            liveTitle: null,
            liveLoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.liveVibes!,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          liveVibes: updatedFeeds,
          liveTitle: result['title'] as String?,
          liveLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchNearbyFeeds(
    FetchNearbyFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    final currentState = state;
    emit(currentState.copyWith(nearbyLoading: true));

    try {
      if (event.forceRefresh) {
        // On refresh, start from the current total count
        final skip = _totalFetchedCounts['nearby']!;
        _totalFetchedCounts['nearby'] = skip + event.limit;

        final result = await _feedRepository.fetchVibeFeeds(
          limit: event.limit,
          skip: skip,
          location: event.location,
        );

        _loadedState = _loadedState.copyWith(
          nearbyFeeds: result['feeds'] as List<VibeFeedModel>,
          nearbyLoading: false,
        );
      } else {
        // For pagination, use the total fetched count
        final skip = _totalFetchedCounts['nearby']!;
        _totalFetchedCounts['nearby'] = skip + event.limit;

        final result = await _feedRepository.fetchVibeFeeds(
          limit: event.limit,
          skip: skip,
          location: event.location,
        );

        final updatedFeeds = [
          ..._loadedState.nearbyFeeds,
          ...(result['feeds'] as List<VibeFeedModel>),
        ];

        _loadedState = _loadedState.copyWith(
          nearbyTitle: result['title'] as String?,
          nearbyFeeds: updatedFeeds,
          nearbyLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchTalkVibes(
    FetchTalkVibesEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.talkVibes.isNotEmpty &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(talkLoading: true));

    try {
      final result = await _feedRepository.fetchTalkVibes(
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          final List<VibeFeedModel> updatedFeeds = [
            ..._loadedState.talkVibes,
            VibeFeedModel.endMarker(),
          ];
          _loadedState = _loadedState.copyWith(
            talkVibes: updatedFeeds,
            talkTitle: _loadedState.talkTitle,
            talkLoading: false,
          );
        } else {
          _loadedState = _loadedState.copyWith(
            talkVibes: [],
            talkTitle: null,
            talkLoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.talkVibes,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          talkVibes: updatedFeeds,
          talkTitle: result['title'] as String?,
          talkLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchDanceVibes(
    FetchDanceVibesEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.danceVibes.isNotEmpty &&
        !event.forceRefresh &&
        event.skip == 0) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(danceLoading: true));

    try {
      final result = await _feedRepository.fetchDanceVibes(
        limit: event.limit,
        skip: event.skip,
      );

      if (result.isEmpty) {
        if (event.skip > 0) {
          final List<VibeFeedModel> updatedFeeds = [
            ..._loadedState.danceVibes,
            VibeFeedModel.endMarker(),
          ];
          _loadedState = _loadedState.copyWith(
            danceVibes: updatedFeeds,
            danceTitle: _loadedState.danceTitle,
            danceLoading: false,
          );
        } else {
          _loadedState = _loadedState.copyWith(
            danceVibes: [],
            danceTitle: null,
            danceLoading: false,
          );
        }
      } else {
        final List<VibeFeedModel> updatedFeeds;
        if (event.forceRefresh) {
          // For force refresh, replace existing feeds
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        } else if (event.skip > 0) {
          // For pagination, append to existing feeds
          updatedFeeds = [
            ..._loadedState.danceVibes,
            ...(result['feeds'] as List<VibeFeedModel>),
          ];
        } else {
          // For initial load
          updatedFeeds = result['feeds'] as List<VibeFeedModel>;
        }

        _loadedState = _loadedState.copyWith(
          danceVibes: updatedFeeds,
          danceTitle: result['title'] as String?,
          danceLoading: false,
        );
      }

      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }

  Future<void> _onFetchMusicGenreFeeds(
    FetchMusicGenreFeedsEvent event,
    Emitter<FeedState> emit,
  ) async {
    if (_loadedState.musicGenreFeeds.containsKey(event.genre) &&
        _loadedState.musicGenreFeeds[event.genre]!.isNotEmpty &&
        !event.forceRefresh) {
      return;
    }

    final currentState = state;
    emit(currentState.copyWith(byMusicLoading: true));
    try {
      final result = await _feedRepository.fetchMusicGenreFeeds(event.genre);
      final updatedGenreFeeds = Map<String, List<FeedModel>>.from(
        _loadedState.musicGenreFeeds,
      )..addAll({event.genre: result['feeds'] as List<FeedModel>});

      _loadedState = _loadedState.copyWith(
        musicGenreFeeds: updatedGenreFeeds,
        byMusicTitle: result['title'] as String?,
      );
      emit(_loadedState);
    } catch (e) {
      emit(FeedError(e.toString()));
    }
  }
}
