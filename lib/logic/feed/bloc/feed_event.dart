import 'package:equatable/equatable.dart';

import 'package:vibeo/models/models.dart';

abstract class FeedEvent extends Equatable {
  const FeedEvent();

  @override
  List<Object> get props => [];
}

class FetchLiveFeedsEvent extends FeedEvent {
  final int limit;
  final int skip;
  final bool forceRefresh;
  const FetchLiveFeedsEvent({
    this.limit = 5,
    this.skip = 0,
    this.forceRefresh = false,
  });
  @override
  List<Object> get props => [limit];
}

class FetchNearbyFeedsEvent extends FeedEvent {
  final int limit;
  final int skip;
  final bool forceRefresh;
  final Location? location;
  const FetchNearbyFeedsEvent({
    required this.location,
    this.limit = 5,
    this.skip = 0,
    this.forceRefresh = false,
  });
  @override
  List<Object> get props => [limit];
}

class FetchMusicGenreFeedsEvent extends FeedEvent {
  final String genre;
  final bool forceRefresh;
  const FetchMusicGenreFeedsEvent(this.genre, {this.forceRefresh = false});
  @override
  List<Object> get props => [genre];
}

class FetchAreaFeedsEvent extends FeedEvent {
  final String area;
  final int skip;
  final int limit;

  final bool forceRefresh;

  const FetchAreaFeedsEvent(
    this.area, {
    this.skip = 0,
    this.limit = 5,
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [area, skip, limit];
}

class FetchSceneFeedsEvent extends FeedEvent {
  final String scene;
  final int skip;
  final int limit;

  final bool forceRefresh;

  const FetchSceneFeedsEvent(
    this.scene, {
    this.skip = 0,
    this.limit = 5,
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [scene, skip, limit];
}

class FetchBeoAIFeedsEvent extends FeedEvent {
  final String typeID;
  final int skip;
  final int limit;

  final bool forceRefresh;

  const FetchBeoAIFeedsEvent(
    this.typeID, {
    this.skip = 0,
    this.limit = 5,
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [typeID, skip, limit];
}

class FetchVibeTypeFeedsEvent extends FeedEvent {
  final String vibeTypeID;
  final int skip;
  final int limit;

  final bool forceRefresh;

  const FetchVibeTypeFeedsEvent(
    this.vibeTypeID, {
    this.skip = 0,
    this.limit = 5,
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [vibeTypeID, skip, limit];
}

class FetchTalkVibesEvent extends FeedEvent {
  final int limit;
  final int skip;
  final bool forceRefresh;
  const FetchTalkVibesEvent({
    this.limit = 5,
    this.skip = 0,
    this.forceRefresh = false,
  });
  @override
  List<Object> get props => [limit];
}

class FetchDanceVibesEvent extends FeedEvent {
  final int limit;
  final int skip;
  final bool forceRefresh;
  const FetchDanceVibesEvent({
    this.limit = 5,
    this.skip = 0,
    this.forceRefresh = false,
  });
  @override
  List<Object> get props => [limit];
}

class FetchVibeScoreFeedsEvent extends FeedEvent {}
