import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';

import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/services/feed/thumbnail_extractor.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class FeedRepository {
  final NetworkServicesApi _api;

  FeedRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<List<FeedModel>> fetchVenueFeeds(
    String venueID, {
    int limit = 5,
  }) async {
    try {
      final encodedVenue = Uri.encodeComponent(venueID);
      final response = await _api.getAPI<List<dynamic>>(
        '/v1/feeds/venueID?venueID=$encodedVenue&limit=$limit',
        (json) => json['data'] as List<dynamic>,
      );

      return response
          .map((feed) => FeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch venue feeds: $e');
      return [];
    }
  }

  Future<List<String>> fetchAreas() async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/areas',
        (json) => json,
      );
      return response['areas'].cast<String>() as List<String>;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch areas: $e');
      return [];
    }
  }

  Future<List<Map<String, dynamic>>> fetchAreasForFeeds() async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/areasForFeeds',
        (json) => json,
      );
      return response['areas']['data'].cast<Map<String, dynamic>>()
          as List<Map<String, dynamic>>;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch areas for feeds: $e');
      return [];
    }
  }

  Future<List<String>> fetchScenesForFeeds() async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/sceneForFeeds',
        (json) => json,
      );
      return response['scenes'].cast<String>() as List<String>;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch venue feeds: $e');
      return [];
    }
  }

  Future<List<Map<String, String>>> fetchBeoTypesForFeeds() async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/beoTypeForFeeds',
        (json) => json,
      );
      return response['beoTypes'].cast<Map<String, String>>()
          as List<Map<String, String>>;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch venue feeds: $e');
      return [];
    }
  }

  Future<List<Map<String, String>>> fetchVibeTypesForFeeds() async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/vibeTypeForFeeds',
        (json) => json,
      );
      return response['vibeTypes'].cast<Map<String, String>>()
          as List<Map<String, String>>;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch venue feeds: $e');
      return [];
    }
  }

  Future<List<FeedModel>?> fetchUserFeeds(
    String uid, {
    int limit = 10,
  }) async {
    try {
      final response = await _api.getAPI<List<dynamic>>(
        '/v1/feeds/user?uid=$uid&limit=$limit',
        (json) => json['data'] as List<dynamic>,
      );

      return response
          .map((feed) => FeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch user feeds: $e');
      return [];
    }
  }

  Future<List<FeedModel>> fetchUserLikedFeeds(
    List<String> feedIds, {
    int limit = 10,
  }) async {
    try {
      final response = await _api.postAPI<List<dynamic>>(
        '/v1/feeds/liked',
        {
          'feedIds': feedIds,
          'limit': limit,
        },
        (json) => json['data'] as List<dynamic>,
      );

      return response
          .map((feed) => FeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch user liked feeds: $e');
      return [];
    }
  }

  Future<Map<String, dynamic>> fetchAreaFeeds(
    String area, {
    int skip = 0,
    int limit = 5,
  }) async {
    try {
      final encodedArea = Uri.encodeComponent(area);
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/area?area=$encodedArea&skip=$skip&limit=$limit',
        (json) => json,
      );
      final feedsList = response['data'] as List<dynamic>;
      final feeds = feedsList
          .map((feed) => VibeFeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch area feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchSceneFeeds(
    String scene, {
    int skip = 0,
    int limit = 5,
  }) async {
    try {
      final encodedScene = Uri.encodeComponent(scene);
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/scene?scene=$encodedScene&skip=$skip&limit=$limit',
        (json) => json,
      );
      final feedsList = response['data'] as List<dynamic>;
      final feeds = feedsList
          .map((feed) => VibeFeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch scene feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchBeoTypeFeeds(
    String beoTypeID, {
    int skip = 0,
    int limit = 5,
  }) async {
    try {
      final encodedBeoType = Uri.encodeComponent(beoTypeID);
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/beoType?beoTypeID=$encodedBeoType&skip=$skip&limit=$limit',
        (json) => json,
      );
      final feedsList = response['data'] as List<dynamic>;
      final feeds = feedsList
          .map((feed) => VibeFeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch beoAI feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchVibeTypeFeeds(
    String vibeTypeID, {
    int skip = 0,
    int limit = 5,
  }) async {
    try {
      final encodedVibeType = Uri.encodeComponent(vibeTypeID);
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/vibeTypeFeeds?vibeTypeID=$encodedVibeType&skip=$skip&limit=$limit',
        (json) => json,
      );
      final feedsList = response['data'] as List<dynamic>;
      final feeds = feedsList
          .map((feed) => VibeFeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } catch (e) {
      AppLogger.error('Failed to fetch vibeType feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchMusicGenreFeeds(
    String genre, {
    int limit = 10,
  }) async {
    try {
      final encodedGenre = Uri.encodeComponent(genre);
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/genre?genre=$encodedGenre&limit=$limit',
        (json) => json,
      );
      final feedsList = response['data'] as List<dynamic>;
      final feeds = feedsList
          .map((feed) => FeedModel.fromJson(feed as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } on NotFoundException {
      return {};
    } catch (e) {
      AppLogger.error('Failed to fetch genre feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchVibeFeeds({
    int limit = 5,
    int skip = 0,
    Location? location,
  }) async {
    try {
      String url = '/v1/feeds/vibes?limit=$limit&skip=$skip';
      if (location != null) {
        url += '&latitude=${location.latitude}&longitude=${location.longitude}';
      }

      final response = await _api.getAPI<Map<String, dynamic>>(
        url,
        (json) => json,
      );

      final venuesList = response['data'] as List<dynamic>;
      final feeds = venuesList
          .map((venue) => VibeFeedModel.fromJson(venue as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } on NotFoundException {
      return {};
    } catch (e) {
      AppLogger.error('Failed to fetch vibes feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchLiveFeeds({
    int limit = 5,
    int skip = 0,
  }) async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/today?limit=$limit&skip=$skip',
        (json) => json,
      );

      final venuesList = response['data'] as List<dynamic>;
      final feeds = venuesList
          .map((venue) => VibeFeedModel.fromJson(venue as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } on NotFoundException {
      return {};
    } catch (e) {
      AppLogger.error('Failed to fetch live vibes feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchTalkVibes({
    int limit = 5,
    int skip = 0,
  }) async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/talk?limit=$limit&skip=$skip',
        (json) => json,
      );

      final venuesList = response['data'] as List<dynamic>;
      final feeds = venuesList
          .map((venue) => VibeFeedModel.fromJson(venue as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } on NotFoundException {
      return {};
    } catch (e) {
      AppLogger.error('Failed to fetch talk feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchDanceVibes({
    int limit = 5,
    int skip = 0,
  }) async {
    try {
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/feeds/dance?limit=$limit&skip=$skip',
        (json) => json,
      );

      final venuesList = response['data'] as List<dynamic>;
      final feeds = venuesList
          .map((venue) => VibeFeedModel.fromJson(venue as Map<String, dynamic>))
          .toList();

      return {
        'title': response['title'] as String,
        'feeds': feeds,
      };
    } on NotFoundException {
      return {};
    } catch (e) {
      AppLogger.error('Failed to fetch dance feeds: $e');
      return {};
    }
  }

  Future<Map<String, dynamic>> fetchPerks({
    required String venueId,
    required String userId,
    required String userEmail,
    int limit = 5,
    int skip = 0,
  }) async {
    try {
      final encodedVenueID = Uri.encodeComponent(venueId);

      // For regular venues, use the standard API endpoint

      final response = await _api.getAPI<Map<String, dynamic>>(
        'https://testvouchersticketing-dev-367993833476.us-central1.run.app/api/public/vouchers/byVenueWithStatus?venueId=$encodedVenueID&userId=$userId&userEmail=$userEmail',
        (json) => json,
      );

      // For the test venue, use the direct voucher API
      final offers = response['data'] as List<dynamic>;

      return {
        'offers': {
          'title': 'Venue Offers',
          'data': offers
              .map(
                (offer) => OfferModel.fromJson(offer as Map<String, dynamic>),
              )
              .toList(),
        },
        'promoters': {
          'title': 'Venue Promoters',
          'data': <PromoterModel>[],
        },
      };

      // final offers = response['offers']?['data'] is List
      //     ? (response['offers']['data'] as List<dynamic>)
      //         .where((offer) => offer != null)
      //         .map(
      //           (offer) => OfferModel.fromJson(offer as Map<String, dynamic>),
      //         )
      //         .toList()
      //     : <OfferModel>[];

      // final promoters = response['promoters']?['data'] is List
      //     ? (response['promoters']['data'] as List<dynamic>)
      //         .where((promoter) => promoter != null)
      //         .map(
      //           (promoter) =>
      //               PromoterModel.fromJson(promoter as Map<String, dynamic>),
      //         )
      //         .toList()
      //     : <PromoterModel>[];

      // return {
      //   'offers': {
      //     'title': response['offers']?['title'] as String? ?? 'Venue Offers',
      //     'data': offers,
      //   },
      //   'promoters': {
      //     'title':
      //         response['promoters']?['title'] as String? ?? 'Venue Promoters',
      //     'data': promoters,
      //   },
      // };
      // }
    } on NotFoundException {
      return {
        'offers': {'title': 'Venue Offers', 'data': <OfferModel>[]},
        'promoters': {'title': 'Venue Promoters', 'data': <PromoterModel>[]},
      };
    } catch (e) {
      AppLogger.error('Failed to fetch perks: $e');
      return {
        'offers': {'title': 'Venue Offers', 'data': <OfferModel>[]},
        'promoters': {'title': 'Venue Promoters', 'data': <PromoterModel>[]},
      };
    }
  }

  Future<bool> uploadFeed(
    FeedModel feed,
    String currentVideoPath,
  ) async {
    try {
      // Make a direct Dio request to handle the raw response
      final dio = Dio(
        BaseOptions(
          connectTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 30),
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      // Create a local variable to store the updated feed
      var updatedFeed = feed;

      AppLogger.info('Uploading IN REPO feed: $updatedFeed');

      final response = await dio.post(
        'https://feeds-testings-367993833476.us-central1.run.app/process-video',
        data: updatedFeed.toJson(),
      );

      AppLogger.info('Raw response: ${response.data}');

      // Check if the response contains the expected structure
      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;

        // Check for data field which contains the success boolean
        if (responseData.containsKey('data')) {
          final success = responseData['data'] as bool? ?? false;
          AppLogger.info('Feed uploaded successfully: $success');

          // If successful, update the feed with result data if available
          if (success && responseData.containsKey('result')) {
            final result = responseData['result'] as Map<String, dynamic>?;
            if (result != null) {
              // Update feed with music, genre, artists info if available
              if (result.containsKey('music') && result['music'] != null) {
                updatedFeed =
                    updatedFeed.copyWith(music: result['music'] as String?);
              }
              if (result.containsKey('genre') && result['genre'] != null) {
                updatedFeed =
                    updatedFeed.copyWith(genre: result['genre'] as String?);
              }
              if (result.containsKey('artists') && result['artists'] != null) {
                updatedFeed =
                    updatedFeed.copyWith(artists: result['artists'] as String?);
              }
              if (result.containsKey('thumbnailUrl') &&
                  result['thumbnailUrl'] != null) {
                updatedFeed = updatedFeed.copyWith(
                  thumbnailURL: result['thumbnailUrl'] as String,
                );
              }
              if (result.containsKey('videoUrl') &&
                  result['videoUrl'] != null) {
                updatedFeed = updatedFeed.copyWith(
                  videoURL: result['videoUrl'] as String,
                );
              }
            }
          }

          return success;
        }
      }

      // If we can't find the expected structure, check if it's a list
      if (response.data is List && (response.data as List).isNotEmpty) {
        final firstItem = (response.data as List)[0];
        if (firstItem is Map<String, dynamic> &&
            firstItem.containsKey('data')) {
          final success = firstItem['data'] as bool? ?? false;
          AppLogger.info('Feed uploaded successfully (from list): $success');

          // If successful, update the feed with result data if available
          if (success && firstItem.containsKey('result')) {
            final result = firstItem['result'] as Map<String, dynamic>?;
            if (result != null) {
              // Update feed with music, genre, artists info if available
              if (result.containsKey('music') && result['music'] != null) {
                updatedFeed =
                    updatedFeed.copyWith(music: result['music'] as String?);
              }
              if (result.containsKey('genre') && result['genre'] != null) {
                updatedFeed =
                    updatedFeed.copyWith(genre: result['genre'] as String?);
              }
              if (result.containsKey('artists') && result['artists'] != null) {
                updatedFeed =
                    updatedFeed.copyWith(artists: result['artists'] as String?);
              }
              if (result.containsKey('thumbnailUrl') &&
                  result['thumbnailUrl'] != null) {
                updatedFeed = updatedFeed.copyWith(
                  thumbnailURL: result['thumbnailUrl'] as String,
                );
              }
              if (result.containsKey('videoUrl') &&
                  result['videoUrl'] != null) {
                updatedFeed = updatedFeed.copyWith(
                  videoURL: result['videoUrl'] as String,
                );
              }
            }
          }

          return success;
        }
      }

      AppLogger.error('Unexpected response format: ${response.data}');
      return false;
    } on DioException catch (e) {
      AppLogger.error('Dio error while uploading feed: ${e.message}');
      if (e.response != null) {
        AppLogger.error('Response data: ${e.response?.data}');
      }
      await Sentry.captureException(e);
      return false;
    } on NotFoundException catch (e) {
      AppLogger.error('Feed endpoint not found: ${e.message}');
      return false;
    } on Exception catch (e) {
      AppLogger.error('API error while uploading feed: $e');
      await Sentry.captureException(e);
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('Unexpected error while uploading feed: $e');
      await Sentry.captureException(e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<void> uploadFeedToVision(
    FeedModel feed,
    String currentVideoPath,
  ) async {
    try {
      AppLogger.debug('Uploading feed to vision');
      AppLogger.info(feed.toString());

      // Generate thumbnails from the video
      final thumbnails = await getSegmentThumbnails(currentVideoPath);
      if (thumbnails == null || thumbnails.length < 3) {
        throw Exception('Failed to generate thumbnails');
      }

      // Take only the first 3 thumbnails
      final List<String> base64Images =
          thumbnails.take(3).map(base64Encode).toList();

      final formData = FormData.fromMap({
        'id': feed.id,
        'music': feed.music ?? 'Music',
        'venueName': feed.venueName,
        'area': feed.area,
        'venueCategory': feed.venueType ?? 'venueType',
        'coverCharge': feed.coverCharge ?? 'coverCharge',
        'USP': feed.usp ?? 'USP',
        'vibeScore': feed.vibeScore,
        'thumbnail1': MultipartFile.fromString(
          base64Images[0],
          filename: 'thumbnail1.txt',
          contentType: DioMediaType('text', 'plain'),
        ),
        'thumbnail2': MultipartFile.fromString(
          base64Images[1],
          filename: 'thumbnail2.txt',
          contentType: DioMediaType('text', 'plain'),
        ),
        'thumbnail3': MultipartFile.fromString(
          base64Images[2],
          filename: 'thumbnail3.txt',
          contentType: DioMediaType('text', 'plain'),
        ),
      });

      final response = await _api.postAPI<Map<String, dynamic>>(
        '/v1/feeds/process-video',
        formData,
        (json) => json,
      );

      if (response['data'] == null) {
        AppLogger.error('Upload failed with status: $response');
      }
    } catch (e) {
      AppLogger.error('Upload error: $e');
      rethrow;
    }
  }

  Future<bool> deleteFeed(String feedID) async {
    try {
      final response = await _api.deleteAPI<bool>(
          '/v1/feeds/',
          json.encode(
            {
              'data': feedID,
            },
          ), (data) {
        return data as bool;
      });

      return response;
    } on NotFoundException {
      return false;
    } catch (e) {
      AppLogger.error('Failed to upload feed: $e');
      return false;
    }
  }
}
