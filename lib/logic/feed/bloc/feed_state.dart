import 'package:equatable/equatable.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/feed/vibe_feed_model.dart';

abstract class FeedState extends Equatable {
  final List<VibeFeedModel> nearbyFeeds;
  final Map<String, List<FeedModel>> musicGenreFeeds;
  final Map<String, List<VibeFeedModel>> areaFeeds;
  final Map<String, List<VibeFeedModel>> sceneFeeds;
  final Map<String, List<VibeFeedModel>> beoAIFeeds;
  final Map<String, List<VibeFeedModel>> vibeTypeFeeds;

  final List<VibeFeedModel> talkVibes;
  final List<VibeFeedModel> danceVibes;
  final List<VibeFeedModel>? liveVibes;
  final String? error;

  final String nearbyTitle;
  final String byMusicTitle;
  final String byAreaTitle;
  final String talkTitle;
  final String danceTitle;
  final String liveTitle;
  final String sceneTitle;
  final String beoAITitle;
  final String vibeTypeTitle;

  final bool nearbyLoading;
  final bool byMusicLoading;
  final bool byAreaLoading;
  final bool talkLoading;
  final bool danceLoading;
  final bool liveLoading;
  final bool sceneLoading;
  final bool beoAILoading;
  final bool vibeTypeLoading;

  const FeedState({
    this.nearbyFeeds = const [],
    this.musicGenreFeeds = const {},
    this.areaFeeds = const {},
    this.sceneFeeds = const {},
    this.beoAIFeeds = const {},
    this.talkVibes = const [],
    this.danceVibes = const [],
    this.vibeTypeFeeds = const {},
    this.liveVibes,
    this.nearbyTitle = 'Chicago Vibes',
    this.byMusicTitle = "What's Playing",
    this.byAreaTitle = 'By Neighborhood',
    this.talkTitle = "Let's Talk",
    this.danceTitle = "Let's Dance",
    this.liveTitle = 'Today',
    this.sceneTitle = 'Pick Your Scene?',
    this.beoAITitle = 'AI Curations',
    this.vibeTypeTitle = 'Tonight I Want To...',
    this.byAreaLoading = false,
    this.byMusicLoading = false,
    this.danceLoading = false,
    this.liveLoading = false,
    this.nearbyLoading = false,
    this.sceneLoading = false,
    this.beoAILoading = false,
    this.talkLoading = false,
    this.vibeTypeLoading = false,
    this.error,
  });

  FeedState copyWith({
    List<VibeFeedModel>? nearbyFeeds,
    Map<String, List<FeedModel>>? musicGenreFeeds,
    Map<String, List<VibeFeedModel>>? areaFeeds,
    Map<String, List<VibeFeedModel>>? sceneFeeds,
    Map<String, List<VibeFeedModel>>? beoAIFeeds,
    Map<String, List<VibeFeedModel>>? vibeTypeFeeds,
    List<VibeFeedModel>? talkVibes,
    List<VibeFeedModel>? danceVibes,
    List<VibeFeedModel>? liveVibes,
    String? nearbyTitle,
    String? byMusicTitle,
    String? byAreaTitle,
    String? talkTitle,
    String? danceTitle,
    String? liveTitle,
    String? sceneTitle,
    String? beoAITitle,
    String? vibeTypeTitle,
    bool? byAreaLoading,
    bool? byMusicLoading,
    bool? danceLoading,
    bool? nearbyLoading,
    bool? talkLoading,
    bool? liveLoading,
    bool? sceneLoading,
    bool? beoAILoading,
    bool? vibeTypeLoading,
  }) {
    return FeedsLoaded(
      nearbyFeeds: nearbyFeeds ?? this.nearbyFeeds,
      musicGenreFeeds: musicGenreFeeds ?? this.musicGenreFeeds,
      areaFeeds: areaFeeds ?? this.areaFeeds,
      sceneFeeds: sceneFeeds ?? this.sceneFeeds,
      beoAIFeeds: beoAIFeeds ?? this.beoAIFeeds,
      vibeTypeFeeds: vibeTypeFeeds ?? this.vibeTypeFeeds,
      talkVibes: talkVibes ?? this.talkVibes,
      danceVibes: danceVibes ?? this.danceVibes,
      liveVibes: liveVibes ?? this.liveVibes,
      nearbyTitle: nearbyTitle ?? this.nearbyTitle,
      byMusicTitle: byMusicTitle ?? this.byMusicTitle,
      byAreaTitle: byAreaTitle ?? this.byAreaTitle,
      talkTitle: talkTitle ?? this.talkTitle,
      danceTitle: danceTitle ?? this.danceTitle,
      liveTitle: liveTitle ?? this.liveTitle,
      sceneTitle: sceneTitle ?? this.sceneTitle,
      beoAITitle: beoAITitle ?? this.beoAITitle,
      vibeTypeTitle: vibeTypeTitle ?? this.vibeTypeTitle,
      byAreaLoading: byAreaLoading ?? this.byAreaLoading,
      byMusicLoading: byMusicLoading ?? this.byMusicLoading,
      danceLoading: danceLoading ?? this.danceLoading,
      nearbyLoading: nearbyLoading ?? this.nearbyLoading,
      talkLoading: talkLoading ?? this.talkLoading,
      liveLoading: liveLoading ?? this.liveLoading,
      sceneLoading: sceneLoading ?? this.sceneLoading,
      beoAILoading: beoAILoading ?? this.beoAILoading,
      vibeTypeLoading: vibeTypeLoading ?? this.vibeTypeLoading,
    );
  }

  @override
  List<Object?> get props => [
        nearbyFeeds,
        musicGenreFeeds,
        areaFeeds,
        sceneFeeds,
        beoAIFeeds,
        vibeTypeFeeds,
        talkVibes,
        danceVibes,
        liveVibes,
        nearbyTitle,
        byMusicTitle,
        byAreaTitle,
        talkTitle,
        danceTitle,
        liveTitle,
        sceneTitle,
        beoAITitle,
        vibeTypeTitle,
        nearbyLoading,
        byMusicLoading,
        byAreaLoading,
        talkLoading,
        danceLoading,
        liveLoading,
        sceneLoading,
        beoAILoading,
        vibeTypeLoading,
        error,
      ];
}

class FeedInitial extends FeedState {
  const FeedInitial();
}

// class FeedsLoading extends FeedState {
//   const FeedsLoading();
// }

class FeedsLoaded extends FeedState {
  const FeedsLoaded({
    super.nearbyFeeds,
    super.musicGenreFeeds,
    super.areaFeeds,
    super.sceneFeeds,
    super.beoAIFeeds,
    super.vibeTypeFeeds,
    super.talkVibes,
    super.danceVibes,
    super.liveVibes,
    super.byAreaTitle,
    super.byMusicTitle,
    super.danceTitle,
    super.nearbyTitle,
    super.talkTitle,
    super.liveTitle,
    super.sceneTitle,
    super.beoAITitle,
    super.vibeTypeTitle,
    super.byAreaLoading,
    super.byMusicLoading,
    super.danceLoading,
    super.nearbyLoading,
    super.talkLoading,
    super.liveLoading,
    super.sceneLoading,
    super.beoAILoading,
    super.vibeTypeLoading,
  });

  @override
  FeedsLoaded copyWith({
    List<VibeFeedModel>? nearbyFeeds,
    Map<String, List<FeedModel>>? musicGenreFeeds,
    Map<String, List<VibeFeedModel>>? areaFeeds,
    Map<String, List<VibeFeedModel>>? sceneFeeds,
    Map<String, List<VibeFeedModel>>? beoAIFeeds,
    Map<String, List<VibeFeedModel>>? vibeTypeFeeds,
    List<VibeFeedModel>? talkVibes,
    List<VibeFeedModel>? danceVibes,
    List<VibeFeedModel>? liveVibes,
    String? nearbyTitle,
    String? byMusicTitle,
    String? byAreaTitle,
    String? talkTitle,
    String? danceTitle,
    String? liveTitle,
    String? sceneTitle,
    String? beoAITitle,
    String? vibeTypeTitle,
    bool? byAreaLoading,
    bool? byMusicLoading,
    bool? danceLoading,
    bool? nearbyLoading,
    bool? talkLoading,
    bool? liveLoading,
    bool? sceneLoading,
    bool? beoAILoading,
    bool? vibeTypeLoading,
  }) {
    return FeedsLoaded(
      nearbyFeeds: nearbyFeeds ?? this.nearbyFeeds,
      musicGenreFeeds: musicGenreFeeds ?? this.musicGenreFeeds,
      areaFeeds: areaFeeds ?? this.areaFeeds,
      sceneFeeds: sceneFeeds ?? this.sceneFeeds,
      beoAIFeeds: beoAIFeeds ?? this.beoAIFeeds,
      vibeTypeFeeds: vibeTypeFeeds ?? this.vibeTypeFeeds,
      talkVibes: talkVibes ?? this.talkVibes,
      danceVibes: danceVibes ?? this.danceVibes,
      liveVibes: liveVibes ?? this.liveVibes,
      nearbyTitle: nearbyTitle ?? this.nearbyTitle,
      byMusicTitle: byMusicTitle ?? this.byMusicTitle,
      byAreaTitle: byAreaTitle ?? this.byAreaTitle,
      talkTitle: talkTitle ?? this.talkTitle,
      danceTitle: danceTitle ?? this.danceTitle,
      liveTitle: liveTitle ?? this.liveTitle,
      sceneTitle: sceneTitle ?? this.sceneTitle,
      beoAITitle: beoAITitle ?? this.beoAITitle,
      vibeTypeTitle: vibeTypeTitle ?? this.vibeTypeTitle,
      byAreaLoading: byAreaLoading ?? this.byAreaLoading,
      byMusicLoading: byMusicLoading ?? this.byMusicLoading,
      danceLoading: danceLoading ?? this.danceLoading,
      nearbyLoading: nearbyLoading ?? this.nearbyLoading,
      talkLoading: talkLoading ?? this.talkLoading,
      liveLoading: liveLoading ?? this.liveLoading,
      sceneLoading: sceneLoading ?? this.sceneLoading,
      beoAILoading: beoAILoading ?? this.beoAILoading,
      vibeTypeLoading: vibeTypeLoading ?? this.vibeTypeLoading,
    );
  }
}

class FeedError extends FeedState {
  final String message;

  const FeedError(this.message);

  @override
  List<Object> get props => [message];
}
