import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:vibeo/utils/log/app_logger.dart';

enum NFCOperation {
  Read,
  Write,
}

class NFCFunctions {
  Future<String?> startNFCOperation({
    required NFCOperation operation,
    String dataType = '',
  }) async {
    try {
      // Check if NFC is available
      final availability = await FlutterNfcKit.nfcAvailability;
      AppLogger.info('NFC availability: $availability');

      if (availability != NFCAvailability.available) {
        String errorMessage;
        if (availability == NFCAvailability.disabled) {
          errorMessage =
              'NFC is disabled. Please enable NFC in your device settings.';
        } else if (availability == NFCAvailability.not_supported) {
          errorMessage = 'NFC is not supported on this device.';
        } else {
          errorMessage = 'NFC is not available on this device.';
        }

        AppLogger.error(errorMessage);
        return null;
      }

      // Start NFC session
      NFCTag tag;
      try {
        // Poll for NFC tags with expanded configuration
        tag = await FlutterNfcKit.poll(
          timeout: const Duration(seconds: 20),
          iosMultipleTagMessage:
              'Multiple tags detected, please present only one tag',
          iosAlertMessage: 'Hold your phone near the NFC tag',
          // Enable all supported tag technologies
          readIso14443A: true, // Type A tags (MIFARE, etc.)
          readIso14443B: true, // Type B tags
          readIso15693: true, // ISO 15693 tags
          readIso18092: true, // FeliCa, etc.
          androidPlatformSound: true,
          androidCheckNDEF: false, // Don't limit to NDEF-formatted tags
        );
        AppLogger.info('NFC Tag found: $tag');

        // Log detailed tag information
        final tagDetails = {
          'ID': tag.id,
          'Type': tag.type.toString(),
          'Standard': tag.standard,
          'NDEF Available': tag.ndefAvailable,
          'NDEF Writable': tag.ndefWritable,
          'NDEF Capacity': tag.ndefCapacity,
        };
        AppLogger.info('NFC Tag found: $tagDetails');

        // Process the tag based on operation
        if (operation == NFCOperation.Read) {
          final readSuccess = await readFromTag(tag: tag);
          if (readSuccess == true) {
            return tag.id;
          }
          return null;
        }
      } catch (e) {
        AppLogger.error('NFC Error: $e');
        return null;
      } finally {
        // Always finish the NFC session
        await FlutterNfcKit.finish();
      }
    } catch (e) {
      AppLogger.error('NFC Operation Error: $e');
      return null;
    }

    return null;
  }

  Future<bool?> readFromTag({required NFCTag tag}) async {
    String? decodedText;
    bool readSuccess = false;

    try {
      AppLogger.info(
        'Reading tag: ${tag.id}, Type: ${tag.type}, Standard: ${tag.standard}',
      );

      // Try multiple approaches to read the tag
      // Approach 1: Try to read NDEF data if available
      if (tag.ndefAvailable == true) {
        try {
          // Read NDEF raw records
          final ndefRawRecords = await FlutterNfcKit.readNDEFRawRecords();
          AppLogger.info('NDEF Raw Records: $ndefRawRecords');

          if (ndefRawRecords.isNotEmpty) {
            // Process the first NDEF record
            final firstRecord = ndefRawRecords.first;

            // Convert hex string payload to bytes
            if (firstRecord.payload.isNotEmpty) {
              try {
                // Convert hex string to bytes
                final List<int> payloadBytes = _hexToBytes(firstRecord.payload);

                // Handle different record types
                if (firstRecord.type == 'T') {
                  // Text record
                  // For Text records, the first byte is the status byte
                  // The status byte contains the language code length in the lower 6 bits
                  if (payloadBytes.isNotEmpty) {
                    final languageCodeLength = payloadBytes[0] & 0x3F;
                    if (payloadBytes.length > languageCodeLength + 1) {
                      final textBytes =
                          payloadBytes.sublist(1 + languageCodeLength);
                      decodedText = utf8.decode(textBytes);
                      readSuccess = true;
                      AppLogger.info('Decoded text record: $decodedText');
                    }
                  }
                } else if (firstRecord.type == 'U') {
                  // URI record
                  if (payloadBytes.isNotEmpty) {
                    // First byte is the URI identifier code
                    final prefixCode = payloadBytes[0];
                    final prefix = _getUriPrefix(prefixCode);
                    final uri = prefix + utf8.decode(payloadBytes.sublist(1));
                    decodedText = 'URI: $uri';
                    readSuccess = true;
                    AppLogger.info('Decoded URI record: $decodedText');
                  }
                } else {
                  // For other types, try to decode as UTF-8
                  try {
                    decodedText = utf8.decode(payloadBytes);
                    readSuccess = true;
                    AppLogger.info('Decoded as UTF-8: $decodedText');
                  } catch (e) {
                    // If UTF-8 decoding fails, just show the hex
                    decodedText = 'Raw data (hex): ${firstRecord.payload}';
                    readSuccess = true;
                    AppLogger.info(
                      'Could not decode as UTF-8, raw data: ${firstRecord.payload}',
                    );
                  }
                }
              } catch (e) {
                decodedText = 'Failed to parse payload: $e';
                readSuccess = false;
                AppLogger.error('Payload parsing error: $e');
              }
            }
          }
        } catch (e) {
          AppLogger.error('Error reading NDEF data: $e');
          decodedText = 'Error reading NDEF data: $e';
          readSuccess = false;
        }
      }

      // Approach 2: If NDEF reading failed or no NDEF data, try direct tag reading
      if (decodedText == null) {
        // For MIFARE Classic/Ultralight tags
        if (tag.type == NFCTagType.mifare_classic ||
            tag.type == NFCTagType.mifare_ultralight) {
          try {
            // Note: readBlock is only supported on Android for MIFARE cards
            // and may not be supported for all card types
            final blockData = await FlutterNfcKit.readBlock(0);
            if (blockData.isNotEmpty) {
              // Try to decode as text
              try {
                decodedText =
                    utf8.decode(blockData.where((byte) => byte > 0).toList());
                readSuccess = true;
                AppLogger.info('Decoded block data as text: $decodedText');
              } catch (e) {
                // If decoding fails, show hex
                decodedText =
                    'Block data (hex): ${_bytesToHex(Uint8List.fromList(blockData))}';
                readSuccess = true;
                AppLogger.info(
                  'Block data (hex): ${_bytesToHex(Uint8List.fromList(blockData))}',
                );
              }
            }
          } catch (e) {
            // Don't treat this as a failure - just log it and continue
            AppLogger.info('Block reading not supported for this card: $e');
            // We'll fall through to Approach 3 to get tag info
          }
        }
      }

      // Approach 3: Always collect tag info, even if we already have some data
      // This ensures we have a fallback and always return success if we can read the tag
      final tagInfo = <String, String>{
        'ID': tag.id,
        'Type': tag.type.toString(),
        'Standard': tag.standard,
      };

      if (tag.atqa != null) {
        tagInfo['ATQA'] = tag.atqa!;
      }
      if (tag.sak != null) {
        tagInfo['SAK'] = tag.sak!;
      }
      if (tag.historicalBytes != null) {
        tagInfo['Historical Bytes'] = tag.historicalBytes!;
      }
      if (tag.protocolInfo != null) {
        tagInfo['Protocol Info'] = tag.protocolInfo!;
      }
      if (tag.applicationData != null) {
        tagInfo['Application Data'] = tag.applicationData!;
      }

      // If we haven't successfully read any data yet, use the tag info
      decodedText ??=
          'Tag Info: ${tagInfo.entries.map((e) => '${e.key}: ${e.value}').join(', ')}';

      // At this point, if we've detected a tag, consider it a success
      // This ensures we don't show an error just because we couldn't read specific data
      readSuccess = true;
      AppLogger.info('Tag info: $tagInfo');
    } catch (e) {
      AppLogger.error('Error reading tag: $e');
      decodedText = 'Error reading tag: $e';
      readSuccess = false;
    }

    return readSuccess;
  }

  // Helper method to convert bytes to hex string
  String _bytesToHex(Uint8List bytes) {
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join('');
  }

  List<int> _hexToBytes(String hex) {
    final List<int> bytes = [];
    for (int i = 0; i < hex.length; i += 2) {
      if (i + 2 <= hex.length) {
        bytes.add(int.parse(hex.substring(i, i + 2), radix: 16));
      }
    }
    return bytes;
  }

  // Helper method to get URI prefix based on prefix code
  String _getUriPrefix(int code) {
    switch (code) {
      case 0x00:
        return '';
      case 0x01:
        return 'http://www.';
      case 0x02:
        return 'https://www.';
      case 0x03:
        return 'http://';
      case 0x04:
        return 'https://';
      case 0x05:
        return 'tel:';
      case 0x06:
        return 'mailto:';
      case 0x07:
        return 'ftp://anonymous:anonymous@';
      case 0x08:
        return 'ftp://ftp.';
      case 0x09:
        return 'ftps://';
      case 0x0A:
        return 'sftp://';
      case 0x0B:
        return 'smb://';
      case 0x0C:
        return 'nfs://';
      case 0x0D:
        return 'ftp://';
      case 0x0E:
        return 'dav://';
      case 0x0F:
        return 'news:';
      case 0x10:
        return 'telnet://';
      case 0x11:
        return 'imap:';
      case 0x12:
        return 'rtsp://';
      case 0x13:
        return 'urn:';
      case 0x14:
        return 'pop:';
      case 0x15:
        return 'sip:';
      case 0x16:
        return 'sips:';
      case 0x17:
        return 'tftp:';
      case 0x18:
        return 'btspp://';
      case 0x19:
        return 'btl2cap://';
      case 0x1A:
        return 'btgoep://';
      case 0x1B:
        return 'tcpobex://';
      case 0x1C:
        return 'irdaobex://';
      case 0x1D:
        return 'file://';
      case 0x1E:
        return 'urn:epc:id:';
      case 0x1F:
        return 'urn:epc:tag:';
      case 0x20:
        return 'urn:epc:pat:';
      case 0x21:
        return 'urn:epc:raw:';
      case 0x22:
        return 'urn:epc:';
      case 0x23:
        return 'urn:nfc:';
      default:
        return '';
    }
  }
}
