import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/utils/log/app_logger.dart';

part 'app_lifecycle_event.dart';
part 'app_lifecycle_state.dart';

class AppLifecycleBloc extends Bloc<AppLifecycleEvent, AppLifecycleBlocState>
    with WidgetsBindingObserver {
  AppLifecycleBloc() : super(AppResumedState()) {
    on<AppResumedEvent>((event, emit) {
      emit(AppResumedState());
      AppLogger.debug('App Resumed');
    });

    on<AppInactiveEvent>((event, emit) {
      emit(AppInactiveState());
      AppLogger.debug('App Inactive');
    });

    on<AppPausedEvent>((event, emit) {
      emit(AppPausedState());
      AppLogger.debug('App Paused');
    });

    on<AppDetachedEvent>((event, emit) {
      emit(AppDetachedState());
      AppLogger.debug('App Detached');
    });

    // Initialize observer
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        add(AppResumedEvent());
      case AppLifecycleState.inactive:
        add(AppInactiveEvent());
      case AppLifecycleState.paused:
        add(AppPausedEvent());
      case AppLifecycleState.detached:
        add(AppDetachedEvent());
      case AppLifecycleState.hidden:
        break;
    }
  }

  @override
  Future<void> close() {
    WidgetsBinding.instance.removeObserver(this);
    return super.close();
  }
}
