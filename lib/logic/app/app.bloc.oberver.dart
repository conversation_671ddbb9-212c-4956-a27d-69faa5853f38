import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/logic/exception/exception_bloc.dart';
import 'package:vibeo/logic/exception/exception_event.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class AppBlocObserver extends BlocObserver {
  final BuildContext context;

  AppBlocObserver(this.context);

  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    if (kDebugMode) {
      AppLogger.debug('onEvent $event');
    }
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    if (kDebugMode) {
      AppLogger.debug('onChange $change');
    }
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    if (kDebugMode) {
      AppLogger.debug('onTransition $transition');
    }
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    super.onError(bloc, error, stackTrace);
    if (kDebugMode) {
      AppLogger.debug('onError $error');
    }
    Sentry.captureException(
      error,
      stackTrace: stackTrace,
    );
    // Error handler should still work in all modes
    BlocProvider.of<GlobalErrorBloc>(context).add(
      ShowErrorEvent(
        error.toString(),
        [error, stackTrace],
      ),
    );
  }
}
