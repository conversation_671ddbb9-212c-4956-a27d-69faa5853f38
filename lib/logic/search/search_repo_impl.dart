import 'package:vibeo/models/search/search_response.dart';
import 'package:vibeo/api/network/network_services_api.dart';

class SearchRepository {
  final NetworkServicesApi _api;

  SearchRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<SearchResponse> search(String query) async {
    try {
      final encodedQuery = Uri.encodeComponent(query);
      final response = await _api.getAPI<Map<String, dynamic>>(
        '/v1/search?query=$encodedQuery',
        (json) => json,
      );

      if (response.isEmpty) {
        return SearchResponse(venues: [], feeds: []);
      }

      return SearchResponse.fromJson(response);
    } catch (e) {
      throw Exception('Failed to perform search: $e');
    }
  }

  Future<List<String>> searchAutoCompletions() async {
    try {
      final response = await _api.getAPI<List<String>>(
        '/v1/search/auto-completions',
        (json) => (json['autocompletions'] as List<dynamic>).cast<String>(),
      );
      if (response.isEmpty) {
        return [];
      }

      return response;
    } catch (e) {
      throw Exception('Failed to perform search: $e');
    }
  }
}
