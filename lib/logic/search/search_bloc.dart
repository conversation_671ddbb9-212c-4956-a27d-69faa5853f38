import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:vibeo/logic/search/search_repo_impl.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/venue/venue_model.dart';

part 'search_event.dart';
part 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final SearchRepository _searchRepository;

  SearchBloc({SearchRepository? searchRepository})
      : _searchRepository = searchRepository ?? SearchRepository(),
        super(const SearchInitialState()) {
    on<Searched>(_onSearched);
    on<SearchInitial>(_onSearchInitial);
    on<ClearSearch>(_onClearSearch);
  }

  Future<void> _onSearched(
    Searched event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.isEmpty) {
      emit(
        SearchInitialState(
          autocompleteSuggestions: state.autocompleteSuggestions,
        ),
      );
      return;
    }

    emit(
      SearchLoadingState(
        autocompleteSuggestions: state.autocompleteSuggestions,
      ),
    );

    try {
      final results = await _searchRepository.search(event.query);
      if (results.venues.isEmpty && results.feeds.isEmpty) {
        emit(
          NoSearchResultsFoundState(
            autocompleteSuggestions: state.autocompleteSuggestions,
          ),
        );
        return;
      }
      emit(
        SearchLoadedState(
          venues: results.venues,
          feeds: results.feeds,
          autocompleteSuggestions: state.autocompleteSuggestions,
        ),
      );
    } catch (error) {
      emit(
        SearchErrorState(
          error.toString(),
          autocompleteSuggestions: state.autocompleteSuggestions,
        ),
      );
    }
  }

  Future<void> _onSearchInitial(
    SearchInitial event,
    Emitter<SearchState> emit,
  ) async {
    if (state.autocompleteSuggestions.isNotEmpty) {
      emit(
        SearchInitialState(
          autocompleteSuggestions: state.autocompleteSuggestions,
        ),
      );
      return;
    }

    try {
      final suggestions = await _searchRepository.searchAutoCompletions();
      emit(SearchInitialState(autocompleteSuggestions: suggestions));
    } catch (error) {
      emit(
        SearchErrorState(
          error.toString(),
          autocompleteSuggestions: state.autocompleteSuggestions,
        ),
      );
    }
  }

  Future<void> _onClearSearch(
    ClearSearch event,
    Emitter<SearchState> emit,
  ) async {
    await _onSearchInitial(SearchInitial(), emit);
  }
}
