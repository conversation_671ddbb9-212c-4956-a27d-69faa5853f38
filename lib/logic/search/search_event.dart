part of 'search_bloc.dart';

abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

class SearchInitial extends SearchEvent {
  @override
  List<Object?> get props => [];
}

class ClearSearch extends SearchEvent {
  @override
  List<Object?> get props => [];
}

class Searched extends SearchEvent {
  final String query;
  const Searched(this.query);

  @override
  List<Object?> get props => [query];
}
