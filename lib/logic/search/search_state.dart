part of 'search_bloc.dart';

abstract class SearchState extends Equatable {
  final List<String> autocompleteSuggestions;
  const SearchState({
    this.autocompleteSuggestions = const [],
  });

  @override
  List<Object?> get props => [autocompleteSuggestions];
}

class SearchInitialState extends SearchState {
  const SearchInitialState({super.autocompleteSuggestions});
}

class ClearSearchState extends SearchState {
  const ClearSearchState({super.autocompleteSuggestions});
}

class SearchLoadingState extends SearchState {
  const SearchLoadingState({super.autocompleteSuggestions});
}

class SearchLoadedState extends SearchState {
  final List<VenueModel> venues;
  final List<FeedModel> feeds;

  const SearchLoadedState({
    required this.venues,
    required this.feeds,
    super.autocompleteSuggestions,
  });

  @override
  List<Object?> get props => [venues, feeds, autocompleteSuggestions];
}

class NoSearchResultsFoundState extends SearchState {
  const NoSearchResultsFoundState({super.autocompleteSuggestions});

  @override
  List<Object?> get props => [autocompleteSuggestions];
}

class SearchErrorState extends SearchState {
  final String message;

  const SearchErrorState(
    this.message, {
    super.autocompleteSuggestions,
  });

  @override
  List<Object?> get props => [message, autocompleteSuggestions];
}
