import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class OfferLocalStorage {
  static const _savedOffersKey = 'saved_offers';
  final _storage = const FlutterSecureStorage();
  final String userID;

  OfferLocalStorage(this.userID);

  Future<String> _getUserSpecificKey() async {
    return '${_savedOffersKey}_$userID';
  }

  Future<void> saveOffer(String offerID) async {
    final key = await _getUserSpecificKey();
    final saved = await getSavedOffers();
    if (!saved.contains(offerID)) {
      saved.add(offerID);
      await _storage.write(
        key: key,
        value: jsonEncode(saved),
      );
    }
  }

  Future<void> removeOffer(String offerID) async {
    final key = await _getUserSpecificKey();
    final saved = await getSavedOffers();
    saved.remove(offerID);
    await _storage.write(
      key: key,
      value: jsonEncode(saved),
    );
  }

  Future<List<String>> getSavedOffers() async {
    final key = await _getUserSpecificKey();
    final savedStr = await _storage.read(key: key);
    if (savedStr != null) {
      return List<String>.from(jsonDecode(savedStr) as List);
    }
    return [];
  }

  Future<bool> isOfferSaved(String offerID) async {
    final saved = await getSavedOffers();
    return saved.contains(offerID);
  }
}
