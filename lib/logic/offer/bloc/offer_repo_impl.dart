import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/venue/venue_model.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/api/network/network_services_api.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';

class OfferRepository {
  final NetworkServicesApi _api;

  OfferRepository({NetworkServicesApi? api})
      : _api = api ?? NetworkServicesApi();

  Future<List<OfferModel>> fetchVenueOffers(String venueID) async {
    try {
      final encodedVenueID = Uri.encodeComponent(venueID);
      final response = await _api.getAPI<Map<String, dynamic>>(
        'https://testvouchersticketing-dev-367993833476.us-central1.run.app/api/public/vouchers/byVenue?venueId=$encodedVenueID',
        (json) => json,
      );
      final result = response['data']
          .map((Map<String, dynamic> offer) {
            return OfferModel.fromJson(offer);
          })
          .cast<OfferModel>()
          .toList();

      return result as List<OfferModel>;
    } catch (e) {
      AppLogger.error('Failed to fetch venue offers: $e');
      throw Exception('Failed to fetch venue offers');
    }
  }

  Future<List<OfferModel>> fetchOffersByList(
    List<String> offersByVenue,
    String userId,
    String userEmail,
  ) async {
    try {
      if (offersByVenue.isEmpty) {
        return [];
      }
      AppLogger.debug('Fetching offers for: $offersByVenue');
      final response = await _api.postAPI<Map<String, dynamic>>(
        'https://testvouchersticketing-dev-367993833476.us-central1.run.app/api/public/vouchers/byUniqueCodeWithStatus?userId=$userId&userEmail=$userEmail',
        {'voucherUniqueCodes': offersByVenue},
        (json) => json,
      );

      AppLogger.debug('Received response: $response');

      // Check if the response contains a 'data' field
      if (response.containsKey('data')) {
        // The response contains a 'data' field with offers information
        final data = response['data'];

        if (data is List) {
          // Process the list of offers
          final List<OfferModel> offers = [];

          for (final offerData in data) {
            try {
              if (offerData is Map<String, dynamic>) {
                final offer = OfferModel.fromJson(offerData);
                offers.add(offer);
              }
            } catch (e) {
              AppLogger.error('Error parsing offer: $e');
              // Continue with next offer if there's an error
            }
          }

          return offers;
        }
      }

      // If we reach here, either the data field is missing or not a list
      AppLogger.error('Invalid response format: $response');
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch offers by list: $e');
      // Return empty list instead of throwing to avoid crashing the UI
      return [];
    }
  }

  /// Fetch offers by venue ID and offer IDs using the list API
  /// Returns a list of VenueModel objects with their offers
  Future<List<VenueModel>> fetchOffersByVenueMap(
    Map<String, List<String>> venueOffersMap,
    String userId,
    String userEmail,
  ) async {
    try {
      // Prepare a list to hold all venue models with their offers
      final List<VenueModel> result = [];

      // Flatten the map into a single list of offer IDs for the API call
      final List<String> allOfferIds = [];
      venueOffersMap.forEach((_, offerIds) {
        allOfferIds.addAll(offerIds);
      });

      if (allOfferIds.isEmpty) {
        return [];
      }

      // Fetch all offers in a single API call
      final offers = await fetchOffersByList(allOfferIds, userId, userEmail);

      // Group offers by venue ID
      final Map<String, List<OfferModel>> offersByVenue = {};

      for (final offer in offers) {
        final venueId = offer.venueID;
        if (venueId.isNotEmpty) {
          offersByVenue[venueId] ??= [];
          offersByVenue[venueId]!.add(offer);
        }
      }

      // We need to fetch venue details for each venue ID
      final VenueRepository venueRepository = VenueRepository();
      final List<String> venueIds = venueOffersMap.keys.toList();

      if (venueIds.isNotEmpty) {
        try {
          // Fetch all venue details in a single API call
          final venues =
              await venueRepository.fetchVenueByIds(venueIds: venueIds);

          // Associate offers with their venues
          for (final venue in venues) {
            final venueId = venue.id;
            if (offersByVenue.containsKey(venueId) &&
                offersByVenue[venueId]!.isNotEmpty) {
              // Create a new venue model with the offers
              final venueWithOffers =
                  venue.copyWith(offers: offersByVenue[venueId]);
              result.add(venueWithOffers);
            } else {
              // Add the venue even if it has no offers
              result.add(venue);
            }
          }
        } catch (e) {
          AppLogger.error('Error fetching venue details: $e');
          // If we can't fetch venue details, we'll just return an empty list
          // We need complete venue details to create proper venue models
        }
      }

      return result;
    } catch (e) {
      AppLogger.error('Failed to fetch offers by venue map: $e');
      // Return empty list instead of throwing to avoid crashing the UI
      return [];
    }
  }

  Future<List<OfferModel>> fetchUserSavedOffers(
    List<String> offerIds, {
    int limit = 10,
  }) async {
    try {
      final response = await _api.postAPI<List<dynamic>>(
        '/v1/offers/saved',
        {
          'offerIds': offerIds,
          'limit': limit,
        },
        (json) => json['data'] as List<dynamic>,
      );
      final result = response
          .map((offer) {
            try {
              return OfferModel.fromJson(offer as Map<String, dynamic>);
            } catch (e) {
              AppLogger.error('Error parsing offer: $e');
              return null;
            }
          })
          .where((offer) => offer != null)
          .cast<OfferModel>()
          .toList();

      return result;
    } on NotFoundException {
      return [];
    } catch (e) {
      AppLogger.error('Failed to fetch saved offers: $e');
      return [];
    }
  }
}
