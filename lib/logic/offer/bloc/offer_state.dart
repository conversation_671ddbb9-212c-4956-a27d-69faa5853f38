part of 'offer_bloc.dart';

abstract class OfferState extends Equatable {
  final Map<String, List<OfferModel>?> venueOffers;
  const OfferState({
    this.venueOffers = const {},
  });

  @override
  List<Object?> get props => [venueOffers];
}

class OffersInitial extends OfferState {
  const OffersInitial() : super();
}

class OffersLoading extends OfferState {
  const OffersLoading({super.venueOffers});
}

class OffersLoaded extends OfferState {
  const OffersLoaded({super.venueOffers});
}

class OffersError extends OfferState {
  final String message;
  const OffersError(this.message, {super.venueOffers});
}
