import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:vibeo/logic/offer/bloc/offer_repo_impl.dart';
import 'package:vibeo/models/offer/offer_model.dart';

part 'offer_event.dart';
part 'offer_state.dart';

class OfferBloc extends Bloc<OfferEvent, OfferState> {
  final OfferRepository _offerRepository = OfferRepository();
  late OffersLoaded _loadedState = const OffersLoaded(
    venueOffers: {},
  );

  OfferBloc() : super(const OffersInitial()) {
    on<FetchVenueOffersEvent>(
      _onFetchVenueOffers,
    );
  }

  Future<void> _onFetchVenueOffers(
    FetchVenueOffersEvent event,
    Emitter<OfferState> emit,
  ) async {
    // Check if we already have data for this venue
    if (_loadedState.venueOffers.containsKey(event.venueId) &&
        _loadedState.venueOffers[event.venueId]?.isNotEmpty == true) {
      // We already have valid data, so don't fetch again
      return;
    }
    // Update loading state while preserving existing data
    if (state is! OffersLoaded) {
      emit(const OffersLoading());
    } else {
      emit(OffersLoading(venueOffers: _loadedState.venueOffers));
    }

    try {
      final offers = await _offerRepository.fetchVenueOffers(event.venueId);

      // Update the loaded state with new data
      _loadedState = OffersLoaded(
        venueOffers: {
          ..._loadedState.venueOffers,
          event.venueId: offers.isEmpty ? null : offers,
        },
      );

      emit(_loadedState);
    } catch (e) {
      if (state is OffersLoaded) {
        // Keep existing data on error
        emit(_loadedState);
      } else {
        emit(OffersError(e.toString()));
      }
    }
  }
}
