// import 'package:vibeo/analytics/analytics.dart';
// import 'package:vibeo/logic/offer/storage/offer_storage.dart';

// class OfferController {
//   final String userID;
//   late final OfferLocalStorage _storage;
//   OfferController(this.userID) {
//     _storage = OfferLocalStorage(userID);
//   }

//   Future<bool> onOfferSaved(String offerID) async {
//     final isSaved = await checkIfOfferSaved(offerID);
//     if (isSaved) {
//       await _storage.removeOffer(offerID);
//       return false;
//     } else {
//       final AnalyticsService analytics = AnalyticsService.instance;
//       await analytics.trackOfferSaved(offerID);
//       await _storage.saveOffer(offerID);
//       return true;
//     }
//   }

//   Future<bool> checkIfOfferSaved(String offerID) async {
//     final saved = await _storage.isOfferSaved(offerID);
//     return saved;
//   }

//   Future<List<String>> offersSavedList() async {
//     final saved = await _storage.getSavedOffers();
//     return saved;
//   }
// }
