import 'package:vibeo/models/genre/genre.dart';

List<String> genres = [
  'Hip-Hop',
  'Rap',
  'Trap',
  'Old School',
  'Latin',
  'Reggaeton',
  'Latin Pop',
  'Salsa',
  'EDM',
  'House',
  'Techno',
  'Trance',
  'Pop',
  'Synth-pop',
  'Dance Pop',
  'World',
  'World Music',
  'African',
  'Bollywood',
  'R&B',
  'Soul',
  'Neo-Soul',
  'Country',
  'Country Pop',
  'Country Rock',
  'Jazz',
  'Smooth Jazz',
  'Jazz Fusion',
  'Rock',
  'Alternative Rock',
  'Hard Rock',
  'Classic Rock',
];

// Pop
// EDM
// Hip-Hop
// Latin
// R&B
// Discover
// World
// Jazz
// Rock
// Country

const List<Genre> genresList = [
  Genre(
    name: 'Pop',
    image: 'assets/music_genres/5.png',
  ),
  Genre(
    name: 'EDM',
    image: 'assets/music_genres/4.png',
  ),
  Genre(
    name: 'Hip-Hop',
    image: 'assets/music_genres/2.png',
  ),
  Genre(
    name: 'Latin',
    image: 'assets/music_genres/3.png',
  ),
  Genre(
    name: 'R&B',
    image: 'assets/music_genres/8.png',
  ),
  Genre(
    name: 'Discoveries',
    image: 'assets/music_genres/1.png',
  ),
  Genre(
    name: 'World',
    image: 'assets/music_genres/6.png',
  ),
  Genre(
    name: 'Jazz',
    image: 'assets/music_genres/9.png',
  ),
  Genre(
    name: 'Rock',
    image: 'assets/music_genres/10.png',
  ),
  Genre(
    name: 'Country',
    image: 'assets/music_genres/7.png',
  ),
];
