final Map<String, String> venueDefinitions = {
  'Nightclub':
      'A venue with a dance floor, loud music, and a high-energy atmosphere',
  'Lounge':
      'A more relaxed venue with comfortable seating, softer music, and a casual atmosphere',
  'Bar':
      'A place primarily serving drinks, with a mix of standing and seating areas, often more casual',
  'Sports bar/pub':
      'A bar setting with TVs for sports, casual seating, and social energy',
  'Speakeasy':
      'A hidden or themed venue with a unique, exclusive vibe and usually quieter music',
  'Rooftop':
      'An open-air venue on top of a building, known for views and a varied atmosphere',
  'Event':
      'A space hosting special events or performances, may vary in music and energy',
  'Barcade':
      'A mix of a bar and arcade, offering games, drinks, and a fun, interactive vibe',
};

List<String> coverCharge = [
  'No Cover',
  r'$5-10',
  r'$11-20',
  r'$20+',
];

final Map<int, Map<String, String>> vibeScoreDefinitions = {
  1: {
    'name': 'Quiet',
    'description': 'Seated, relaxed, and conversational',
  },
  2: {
    'name': 'Chill',
    'description': 'Seated/standing, light socializing',
  },
  3: {
    'name': 'Lively',
    'description': 'Standing, light movement, social energy',
  },
  4: {
    'name': "Groovin'",
    'description': 'Lively atmosphere, some dancing',
  },
  5: {
    'name': 'Electric',
    'description': 'High energy, packed dance floor, party mode',
  },
};
