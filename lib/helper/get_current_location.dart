import 'package:vibeo/models/models.dart';
import 'package:vibeo/utils/location/location_permission.dart';

Future<Location?> getCurrentLocation() async {
  if (await isLocationPermissionGranted()) {
    final currentLocation = await getCurrentPosition();

    if (currentLocation != null) {
      return Location(
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
      );
    }
  }

  return null;
}
