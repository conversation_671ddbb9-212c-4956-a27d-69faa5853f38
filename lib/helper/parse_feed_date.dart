String formatTimeAgo(DateTime date) {
  final now = DateTime.now().toUtc();
  final difference = now.difference(date);

  if (difference.inHours <= 6) {
    return 'Today';
  } else if (difference.inDays == 1) {
    return 'Yesterday';
  } else if (difference.inDays == 2) {
    return 'This week';
  } else if (difference.inDays >= 3) {
    return '${_getDayName(date.toUtc())} Vibes';
  } else {
    return 'Recent';
  }
}

String _getDayName(DateTime date) {
  final newDate = date.subtract(const Duration(hours: 3)).toLocal();

  switch (newDate.weekday) {
    case DateTime.monday:
      return 'Monday';
    case DateTime.tuesday:
      return 'Tuesday';
    case DateTime.wednesday:
      return 'Wednesday';
    case DateTime.thursday:
      return 'Thursday';
    case DateTime.friday:
      return 'Friday';
    case DateTime.saturday:
      return 'Saturday';
    case DateTime.sunday:
      return 'Sunday';
    default:
      return '';
  }
}
