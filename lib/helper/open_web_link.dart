import 'package:url_launcher/url_launcher.dart';
import 'package:vibeo/utils/utils.dart';

Future<void> openSocialHandleLink(String instagramLink) async {
  final Uri instagramUrl = Uri.parse(instagramLink);
  try {
    if (await canLaunchUrl(instagramUrl)) {
      await launchUrl(
        instagramUrl,
        mode: LaunchMode.externalApplication,
      );
    } else {
      // Fallback to web URL if app isn't installed
      final webUrl = Uri.parse(
        'https://www.instagram.com/${instagramLink.split('.com/')[1]}',
      );
      await launchUrl(
        webUrl,
        mode: LaunchMode.externalApplication,
      );
    }
  } catch (e) {
    AppLogger.error('Error launching URL: $e');
  }
}

Future<void> openEventLink(String eventLink) async {
  final Uri eventUrl = Uri.parse(eventLink);
  try {
    await launchUrl(
      eventUrl,
      mode: LaunchMode.externalApplication,
    );
  } catch (e) {
    AppLogger.error('Error launching URL: $e');
  }
}

Future<void> openVibeoWebLink() async {
  final Uri vibeoUrl = Uri.parse('https://www.vibeo.io/');
  try {
    if (await canLaunchUrl(vibeoUrl)) {
      await launchUrl(
        vibeoUrl,
        mode: LaunchMode.externalApplication,
      );
    }
  } catch (e) {
    AppLogger.error('Error launching URL: $e');
  }
}
