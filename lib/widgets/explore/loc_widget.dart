import 'package:flutter/material.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class LocWidget extends StatelessWidget {
  final String image;
  final VoidCallback onTap;
  const LocWidget({
    required this.image,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return HapticButton(
      onTap: onTap,
      child: Container(
        height: 60,
        width: 60,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(16),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black54,
              blurRadius: 8,
              spreadRadius: 0.1,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.all(
            Radius.circular(16),
          ),
          child: Image.asset(
            image,
            fit: BoxFit.cover,
            // Add cache dimensions matching display size
            cacheWidth: 180,
            cacheHeight: 180,
          ),
        ),
      ),
    );
  }
}
