import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

HapticButton infoUSP(BuildContext context) {
  return HapticButton(
    child: const Icon(CupertinoIcons.info, color: Colors.grey),
    onTap: () {
      showDialog(
        context: context,
        barrierColor: Colors.black.withAlpha(178),
        builder: (BuildContext context) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: Center(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 32),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(24)),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                    child: Container(
                      decoration: BoxDecoration(
                        color: darkAppColors.secondaryBackgroundColor
                            .withAlpha(76),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(24)),
                        border: Border.all(
                          color: Colors.white.withAlpha(51),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 15,
                            spreadRadius: -5,
                            offset: const Offset(0, 10),
                          ),
                          BoxShadow(
                            color: Colors.white.withAlpha(26),
                            blurRadius: 3,
                            spreadRadius: -1,
                            offset: const Offset(0, -1),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              "Today's USP Guide",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 12),
                            const Text(
                              'What makes this place special today? Why should a user come here?',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            ConstrainedBox(
                              constraints: BoxConstraints(
                                maxHeight:
                                    MediaQuery.of(context).size.height * 0.5,
                              ),
                              child: SingleChildScrollView(
                                child: Text(
                                  '''
Here are some examples of keywords to add (3-6 per venue):

\n🎉 \$7 shots, latin, dancing, couples, lively, energetic
\n🍹 groups, conversations, food, chill, friends, relaxed
\n🥳 big groups, cheap drinks, fun, large venue
\n🎯 free games, baseball cage, groups, college, fun
\n💕 \$2 drinks, romantic, cozy, couples
\nKeep it fun and highlight what makes the venue stand out today!''',
                                  style: TextStyle(
                                    color: Colors.white.withAlpha(204),
                                    fontSize: 15,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 24),
                            authButton(
                              title: 'Close',
                              onTap: () => RouteUtils.pop(context),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      );
    },
  );
}
