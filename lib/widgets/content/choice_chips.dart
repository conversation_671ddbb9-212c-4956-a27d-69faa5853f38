import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

Widget buildChoiceChip(
  String label,
  VoidCallback onTap, {
  required bool isSelected,
}) {
  return HapticButton(
    onTap: () {
      onTap();
    },
    child: AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.symmetric(
        horizontal: 22,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: isSelected
            ? darkAppColors.deepPurple
            : darkAppColors.lightColor.withAlpha(26),
        borderRadius: const BorderRadius.all(Radius.circular(24)),
      ),
      child: AnimatedDefaultTextStyle(
        duration: const Duration(milliseconds: 200),
        style: const TextStyle(
          fontSize: 16,
        ),
        child: Text(label),
      ),
    ),
  );
}
