import 'package:flutter/material.dart';
import 'package:bounce/bounce.dart';
import 'package:flutter/services.dart';
import 'package:vibeo/themes/shadow_theme.dart';

Widget addContentButton({
  required String title,
  required Function? onTap,
  bool enabled = true,
}) {
  return Bounce(
    onTap: () {
      HapticFeedback.mediumImpact();
      if (enabled) {
        onTap!();
      }
    },
    child: SizedBox(
      width: double.infinity,
      height: 56,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: enabled ? Colors.white : Colors.white24,
          borderRadius: const BorderRadius.all(
            Radius.circular(28),
          ),
          boxShadow: AppShadowStyles.baseStyle,
        ),
        child: Text(
          title,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
          ),
        ),
      ),
    ),
  );
}
