import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:vibeo/logic/content/bloc/content_bloc.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';

class UploadingWidget extends StatelessWidget {
  const UploadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: BlocBuilder<ContentBloc, ContentState>(
        builder: (context, state) {
          if (state is FeedUploading || state is FeedSuccess) {
            final bool isComplete = state is FeedSuccess;
            final double progress =
                state is FeedUploading ? state.progress : 1.0;

            return AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: isComplete ? 0.0 : 1.0,
              onEnd: () {
                if (isComplete) {
                  context.read<ContentBloc>().add(const ResetContentEvent());
                }
              },
              child: SizedB<PERSON>(
                height: 50,
                child: ClipRRect(
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      decoration: BoxDecoration(
                        color: isComplete
                            ? Colors.green.withAlpha(153)
                            : darkAppColors.secondaryBackgroundColor
                                .withAlpha(153),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Colors.white.withAlpha(13),
                              Colors.white.withAlpha(2),
                            ],
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: 15,
                                left: SizeUtils.horizontalPadding.left,
                              ),
                              child: Text(
                                isComplete
                                    ? 'Feed uploaded successfully'
                                    : 'Uploading Your Feed',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            LinearPercentIndicator(
                              padding: EdgeInsets.zero,
                              percent: progress,
                              animation: true,
                              curve: Curves.linear,
                              animateFromLastPercent: true,
                              fillColor: isComplete
                                  ? Colors.green.shade300
                                  : darkAppColors.deepPurple,
                              progressColor: isComplete
                                  ? Colors.green.shade300
                                  : darkAppColors.deepPurple,
                              lineHeight: 2,
                              backgroundColor: isComplete
                                  ? Colors.green.shade200.withAlpha(76)
                                  : darkAppColors.secondaryBackgroundColor,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          } else if (state is FeedError) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              ScaffoldMessenger.of(context).clearSnackBars();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    state.message,
                    style: AppTextStyles.bodyBold,
                  ),
                  backgroundColor: darkAppColors.errorColor,
                  duration: const Duration(seconds: 5),
                ),
              );
            });
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }
}
