import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class RedemptionHelpDialog extends StatefulWidget {
  const RedemptionHelpDialog({super.key});

  static Future<void> show(BuildContext context) async {
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: darkAppColors.backgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const Padding(
        padding: EdgeInsets.only(bottom: 20),
        child: RedemptionHelpDialog(),
      ),
    );
  }

  @override
  State<RedemptionHelpDialog> createState() => _RedemptionHelpDialogState();
}

class _RedemptionHelpDialogState extends State<RedemptionHelpDialog> {
  final List<String> _commonIssues = [
    'NFC tag not reading properly',
    "Offer shows as redeemed but I didn't redeem it",
    "Venue staff doesn't recognize my redemption code",
    'App crashes during redemption process',
    'Offer disappeared after trying to redeem',
    'Other issue (please describe)',
  ];

  String? _selectedIssue;
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  bool _isSubmitting = false;
  bool _showEmailField = false;
  bool _showSuccessMessage = false;

  @override
  void initState() {
    super.initState();
    // Check if user has email
    final user = context.read<UserBloc>().state.user;
    if (user != null && (user.email.isEmpty)) {
      _showEmailField = true;
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  /// Launches the default email client with pre-populated fields
  Future<void> _launchEmailClient({
    required String userEmail,
    required String userId,
    required String userPhoneNumber,
    required String selectedIssue,
    required String message,
  }) async {
    const String subject = 'Redemption Help Request';

    // Build email body with all the details
    final String body = '''
Issue: ${selectedIssue.isNotEmpty ? selectedIssue : 'Not specified'}

Message: ${message.isNotEmpty ? message : 'No additional details provided'}

User Information:
- Email: ${userEmail.isNotEmpty ? userEmail : 'Not provided'}
- Phone: ${userPhoneNumber.isNotEmpty ? userPhoneNumber : 'Not provided'}

Sent from Vibeo App
''';

    // Create mailto URI
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: encodeQueryParameters({
        'subject': subject,
        'body': body,
      }),
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri, mode: LaunchMode.externalApplication);
        return;
      } else {
        throw Exception('Could not launch email client');
      }
    } catch (e) {
      AppLogger.error('Error launching email client: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Could not open email client. Please try again later.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Helper method to encode query parameters for mailto URI
  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map(
          (e) =>
              '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
        )
        .join('&');
  }

  Future<void> _submitHelpRequest() async {
    if (_selectedIssue == null && _messageController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select an issue or describe your problem'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // Get user information
      final user = context.read<UserBloc>().state.user;
      final userId = user?.uid ?? 'unknown';
      final userEmail =
          _showEmailField ? _emailController.text : user?.email ?? '';
      final userPhoneNumber = user?.phoneNumber ?? '';
      final selectedIssue = _selectedIssue ?? '';
      final message = _messageController.text.trim();

      // Log the help request
      final helpRequest = {
        'userId': userId,
        'email': userEmail,
        'phoneNumber': userPhoneNumber,
        'issue': selectedIssue,
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
      };
      AppLogger.info('Help request submitted: $helpRequest');

      // Launch email client with pre-populated fields
      await _launchEmailClient(
        userEmail: userEmail,
        userId: userId,
        userPhoneNumber: userPhoneNumber,
        selectedIssue: selectedIssue,
        message: message,
      );

      if (mounted) {
        setState(() {
          _isSubmitting = false;
          _showSuccessMessage = true;
        });
      }
    } catch (e) {
      AppLogger.error('Error submitting help request: $e');
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to submit request. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.only(
        top: 20,
        left: 20,
        right: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Get Help with Redemption',
                style: AppTextStyles.title,
              ),
              IconButton(
                icon: const Icon(CupertinoIcons.xmark, color: Colors.white70),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          const SizedBox(height: 20),
          if (!_showSuccessMessage) ...[
            Text(
              'Select your issue:',
              style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
            ),
            const SizedBox(height: 12),

            // Common issues list
            ...List.generate(_commonIssues.length, (index) {
              final issue = _commonIssues[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedIssue = issue;
                    });
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedIssue == issue
                          ? darkAppColors.deepPurple
                              .withAlpha(77) // 30% opacity
                          : Colors.white.withAlpha(13), // 5% opacity
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _selectedIssue == issue
                            ? darkAppColors.deepPurple
                            : Colors.white24,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _selectedIssue == issue
                              ? CupertinoIcons.checkmark_circle_fill
                              : CupertinoIcons.circle,
                          color: _selectedIssue == issue
                              ? darkAppColors.deepPurple
                              : Colors.white70,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            issue,
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),

            const SizedBox(height: 20),
            Text(
              'Additional details (optional):',
              style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
            ),
            const SizedBox(height: 8),

            // Message text field
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(13), // 5% opacity
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.white24),
              ),
              child: TextField(
                controller: _messageController,
                maxLines: 3,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Please describe your issue in detail...',
                  hintStyle: TextStyle(color: Colors.white38),
                  contentPadding: EdgeInsets.all(16),
                  border: InputBorder.none,
                ),
              ),
            ),

            // Email field if needed
            if (_showEmailField) ...[
              const SizedBox(height: 20),
              Text(
                'Your email (optional):',
                style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(13), // 5% opacity
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white24),
                ),
                child: TextField(
                  controller: _emailController,
                  keyboardType: TextInputType.emailAddress,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    hintText: 'Enter your email address',
                    hintStyle: TextStyle(color: Colors.white38),
                    contentPadding: EdgeInsets.all(16),
                    border: InputBorder.none,
                  ),
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Submit button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitHelpRequest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: darkAppColors.deepPurple,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  disabledBackgroundColor:
                      darkAppColors.deepPurple.withAlpha(128), // 50% opacity
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: LoadingWidget(),
                      )
                    : const Text('Submit Request'),
              ),
            ),
          ] else ...[
            // Success message
            Center(
              child: Column(
                children: [
                  const Icon(
                    CupertinoIcons.check_mark_circled,
                    color: Colors.green,
                    size: 64,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    'Thank you for your request!',
                    style: AppTextStyles.bodysmallBold.copyWith(fontSize: 18),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Your email has been prepared with all the details. Please review and send it from your email client.',
                    style:
                        AppTextStyles.bodySmall.copyWith(color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "We'll get back to you within 2-3 business days.",
                    style:
                        AppTextStyles.bodySmall.copyWith(color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 30),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: darkAppColors.deepPurple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Close'),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
