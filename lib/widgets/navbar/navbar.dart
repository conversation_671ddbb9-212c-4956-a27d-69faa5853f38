import 'package:flutter/material.dart';
import 'dart:ui';

import 'package:flutter/services.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/utils/size_utils.dart';

class NavBar extends StatelessWidget {
  final int selectedIndex;
  final void Function(int) onItemTapped;
  final List<GlobalKey>? tabKeys;

  const NavBar({
    required this.selectedIndex,
    required this.onItemTapped,
    this.tabKeys,
    super.key,
  });

  static const Map<String, String> listOfIcons = {
    'Home': 'assets/icons/home.png',
    'beo': 'assets/icons/v.png',
    'Bag': 'assets/icons/bag.png',
    'Profile': 'assets/icons/user.png',
  };

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
        child: Container(
          height: SizeUtils.screenWidth * .19,
          decoration: BoxDecoration(
            color: darkAppColors.secondaryBackgroundColor.withAlpha(120),
            border: Border(
              top: BorderSide(
                width: 0.5,
                color: Colors.white.withAlpha(100),
              ),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(
              listOfIcons.length,
              (index) => Expanded(
                child: InkWell(
                  key: tabKeys != null && index < tabKeys!.length
                      ? tabKeys![index]
                      : null,
                  onTap: () {
                    onItemTapped(index);
                    HapticFeedback.mediumImpact();
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        listOfIcons.values.toList()[index],
                        height: 20,
                        width: 20,
                        cacheHeight: 80,
                        cacheWidth: 80,
                        color: selectedIndex == index
                            ? Colors.white
                            : Colors.white38,
                        fit: BoxFit.cover,
                      ),
                      const SizedBox(height: 5),
                      Text(
                        listOfIcons.keys.toList()[index],
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: index == selectedIndex
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: index == selectedIndex
                              ? Colors.white
                              : Colors.white38,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
