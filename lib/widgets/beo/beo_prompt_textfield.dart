import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/data/suggestions_beo.dart';
import 'package:vibeo/logic/beo/bloc/beo_bloc.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';

class BeoPromptTextField extends StatefulWidget {
  final void Function(String) onPromptSubmitted;
  final bool isChatMode;
  const BeoPromptTextField({
    required this.onPromptSubmitted,
    required this.isChatMode,
    super.key,
  });

  @override
  State<BeoPromptTextField> createState() => _BeoPromptTextFieldState();
}

class _BeoPromptTextFieldState extends State<BeoPromptTextField> {
  final TextEditingController _promptController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _hasText = false;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _promptController.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _onTextChanged() {
    final newHasText = _promptController.text.trim().isNotEmpty;
    if (_hasText != newHasText) {
      setState(() => _hasText = newHasText);
    }
  }

  void _onFocusChanged() {
    if (_isFocused != _focusNode.hasFocus) {
      setState(() => _isFocused = _focusNode.hasFocus);
    }
  }

  @override
  void dispose() {
    _promptController.removeListener(_onTextChanged);
    _focusNode.removeListener(_onFocusChanged);
    _promptController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (!widget.isChatMode)
          RepaintBoundary(
            child: SuggestionSection(beoController: _promptController),
          )
        else
          const _ChatModeText(),
        RepaintBoundary(child: _buildChatBar()),
      ],
    );
  }

  Widget _buildChatBar() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 100),
      padding: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(28)),
        border: Border.all(
          color: Colors.white.withAlpha(_isFocused ? 200 : 26),
          width: 0.3,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _promptController,
              focusNode: _focusNode,
              cursorOpacityAnimates: false, // Prevent cursor animation
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                contentPadding: EdgeInsets.only(left: 16, right: 10),
                border: InputBorder.none,
                fillColor: Colors.transparent,
                hintText: "What's the plan? Where to? What's the vibe?",
                hintStyle: TextStyle(fontSize: 14, color: Colors.white54),
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
            ),
          ),
          _ActionButton(
            hasText: _hasText,
            onSubmit: () {
              widget.onPromptSubmitted(_promptController.text.trim());
              _promptController.clear();
            },
          ),
        ],
      ),
    );
  }
}

class _ChatModeText extends StatelessWidget {
  const _ChatModeText();

  @override
  Widget build(BuildContext context) {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Text(
        'Beo is continuously learning and can make mistakes.',
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white54, fontSize: 13),
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final bool hasText;
  final VoidCallback onSubmit;

  const _ActionButton({required this.hasText, required this.onSubmit});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BeoRecommendationsBloc, BeoRecommendationsState>(
      builder: (context, state) {
        final bool isEnabled = hasText && state is! BeoRecommendationsLoading;
        return Container(
          margin: const EdgeInsets.only(right: 16),
          child: HapticButton(
            onTap: isEnabled ? onSubmit : () {},
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isEnabled ? Colors.white : Colors.white.withAlpha(26),
              ),
              child: Icon(
                CupertinoIcons.paperplane,
                color: isEnabled ? darkAppColors.deepPurple : Colors.white38,
                size: 20,
              ),
            ),
          ),
        );
      },
    );
  }
}

class SuggestionSection extends StatelessWidget {
  final TextEditingController beoController;
  const SuggestionSection({required this.beoController, super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(
            left: 12,
            bottom: 4,
          ),
          child: Text(
            'Suggestions',
            style: TextStyle(
              fontSize: 16,
            ),
          ),
        ),
        SizedBox(
          height: 100,
          child: ListView.builder(
            itemCount: suggestionsBeo.length,
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
            ),
            itemBuilder: (ctx, index) {
              return HapticButton(
                onTap: () {
                  beoController.text = suggestionsBeo[index];
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                  ),
                  constraints: const BoxConstraints(
                    maxWidth: 180,
                  ),
                  margin: const EdgeInsets.only(
                    right: 10,
                    top: 5,
                    bottom: 10,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(24)),
                    gradient: LinearGradient(
                      colors: [
                        darkAppColors.secondaryBackgroundColor,
                        const Color(0xFF111111),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: AppShadowStyles.baseStyle,
                    border: Border.all(
                      color: Colors.white.withAlpha(25) // User bor
                      ,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.all(Radius.circular(20)),
                    child: Center(
                      child: Text(
                        suggestionsBeo[index],
                        textAlign: TextAlign.left, // Center align the text
                        overflow: TextOverflow.ellipsis, // Handle overflow
                        maxLines: 3, // Limit to 3 lines
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                          height: 1.3, // Adjust line height
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(
          height: 5,
        ),
      ],
    );
  }
}
