import 'dart:async';
import 'package:flutter/material.dart';

import 'package:vibeo/themes/text_theme.dart';

class LiveCountdownTimer extends StatefulWidget {
  final String startTime;
  final String endTime;
  final List<String> weekdays;

  const LiveCountdownTimer({
    required this.startTime,
    required this.endTime,
    super.key,
    this.weekdays = const [],
  });

  @override
  State<LiveCountdownTimer> createState() => _LiveCountdownTimerState();
}

class _LiveCountdownTimerState extends State<LiveCountdownTimer> {
  late Timer _timer;
  late DateTime _endTime;
  int _days = 0;
  int _hours = 0;
  int _minutes = 0;
  int _seconds = 0;

  @override
  void initState() {
    super.initState();
    _calculateEndTime();
    _updateCountdown();

    // Update the countdown every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateCountdown();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _calculateEndTime() {
    final now = DateTime.now();

    // Parse end time
    final endTimeParts = widget.endTime.split(':');
    if (endTimeParts.length == 2) {
      final int endHour = int.parse(endTimeParts[0]);
      final int endMinute = int.parse(endTimeParts[1]);

      // Create end time DateTime
      _endTime = DateTime(now.year, now.month, now.day, endHour, endMinute);

      // If end time is earlier than current time, it means it's for tomorrow
      if (_endTime.isBefore(now)) {
        _endTime = _endTime.add(const Duration(days: 1));
      }
    } else {
      // Fallback if end time format is invalid
      _endTime = now.add(const Duration(hours: 1));
    }
  }

  void _updateCountdown() {
    final now = DateTime.now();
    final difference = _endTime.difference(now);

    if (difference.isNegative) {
      // If the end time has passed, recalculate the next end time
      _calculateEndTime();
      return;
    }

    setState(() {
      _days = difference.inDays;
      _hours = difference.inHours.remainder(24);
      _minutes = difference.inMinutes.remainder(60);
      _seconds = difference.inSeconds.remainder(60);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'Ends in: ',
                style: AppTextStyles.body.copyWith(
                  fontSize: 16,
                ),
              ),
              TextSpan(
                text: _days > 0
                    ? '${_days}d ${_hours}h ${_minutes}m'
                    : '${_hours}h ${_minutes}m ${_seconds}s',
                style: AppTextStyles.bodyBold.copyWith(
                  fontSize: 16,
                  color: Colors.greenAccent, // Green for available
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
