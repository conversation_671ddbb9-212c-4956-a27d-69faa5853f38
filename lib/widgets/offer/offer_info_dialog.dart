import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';

import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';

class OfferInfoDialog extends StatefulWidget {
  final OfferModel offer;
  const OfferInfoDialog({required this.offer, super.key});

  static Future<void> show(BuildContext context, OfferModel offer) async {
    return showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(178),
      barrierDismissible: true,
      builder: (context) => PopScope(
        canPop: true,
        child: OfferInfoDialog(offer: offer),
      ),
    );
  }

  @override
  State<OfferInfoDialog> createState() => _OfferInfoDialogState();
}

class _OfferInfoDialogState extends State<OfferInfoDialog> {
  late String _markdownContent;

  @override
  void initState() {
    super.initState();
    _markdownContent = widget.offer.info!;
  }

  // Format time string to include AM/PM
  String _formatTimeWithAMPM(String time) {
    if (time.isEmpty) return '';

    try {
      // Parse the time string (expected format: "HH:MM")
      final parts = time.split(':');
      if (parts.length != 2) return time;

      int hour = int.parse(parts[0]);
      final minute = parts[1];

      // Convert to 12-hour format
      hour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

      return '$hour:$minute';
    } catch (e) {
      // Return original if parsing fails
      return time;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 36),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: MediaQuery.of(context).size.width * 0.9,
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(24)),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color.fromARGB(255, 84, 17, 135).withAlpha(200),
                    const Color.fromARGB(255, 52, 10, 129).withAlpha(200),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.all(Radius.circular(24)),
                border: Border.all(
                  color: Colors.white.withAlpha(51),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    blurRadius: 15,
                    spreadRadius: -5,
                    offset: const Offset(0, 10),
                  ),
                  BoxShadow(
                    color: Colors.white.withAlpha(26),
                    blurRadius: 3,
                    spreadRadius: -1,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with offer title and close button
                  Padding(
                    padding:
                        const EdgeInsets.only(top: 24, left: 24, right: 16),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.offer.title,
                            style: AppTextStyles.title.copyWith(
                              fontSize: 22,
                            ),
                          ),
                        ),
                        HapticButton(
                          onTap: () {
                            RouteUtils.pop(context);
                          },
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(100),
                              color: Colors.white.withAlpha(30),
                            ),
                            child: const Icon(
                              CupertinoIcons.clear,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (widget.offer.discountedValue != null &&
                      widget.offer.offerValue != 0) ...[
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 16,
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withAlpha(100),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                Text(
                                  '\$${widget.offer.offerValue}',
                                  style: AppTextStyles.bodySmall.copyWith(
                                    decoration: TextDecoration.lineThrough,
                                    color: Colors.white70,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '\$${widget.offer.discountedValue}',
                                  style: AppTextStyles.bodysmallBold.copyWith(
                                    color: Colors.white,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Time information
                  if (widget.offer.startTime.isNotEmpty &&
                      widget.offer.endTime.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 6,
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  CupertinoIcons.clock,
                                  color: Colors.amber,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${_formatTimeWithAMPM(widget.offer.startTime)} - ${_formatTimeWithAMPM(widget.offer.endTime)}',
                                  style: AppTextStyles.bodysmallBold.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Weekdays information
                  if (widget.offer.weekdays.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                vertical: 6,
                              ),
                              child: Row(
                                children: [
                                  const Icon(
                                    CupertinoIcons.calendar,
                                    color: Colors.lightBlue,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      widget.offer.weekdays.join(', '),
                                      style:
                                          AppTextStyles.bodysmallBold.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  // Terms and conditions content
                  Expanded(
                    child: RawScrollbar(
                      thumbColor: Colors.white.withAlpha(76),
                      radius: const Radius.circular(20),
                      thickness: 5,
                      child: buildScrollableWithFade(
                        child: SingleChildScrollView(
                          child: Padding(
                            padding: SizeUtils.horizontalPadding.copyWith(
                              top: 20,
                              bottom: 20,
                            ),
                            child: MarkdownBody(
                              data: _markdownContent,
                              styleSheet: MarkdownStyleSheet(
                                p: TextStyle(
                                  color: Colors.white.withAlpha(204),
                                  fontSize: 16,
                                  height: 1.5,
                                ),
                                h1: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                ),
                                h2: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                                listBullet: TextStyle(
                                  color: Colors.white.withAlpha(204),
                                ),
                                strong: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                                em: TextStyle(
                                  color: Colors.white.withAlpha(230),
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
