import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vibeo/themes/text_theme.dart';

class TimeBasedCountdownWidget extends StatefulWidget {
  final String startTime;
  final String endTime;
  final List<String> weekdays;
  final bool Function() isOfferCurrentlyValid;

  const TimeBasedCountdownWidget({
    required this.startTime,
    required this.endTime,
    required this.weekdays,
    required this.isOfferCurrentlyValid,
    super.key,
  });

  @override
  State<TimeBasedCountdownWidget> createState() =>
      _TimeBasedCountdownWidgetState();
}

class _TimeBasedCountdownWidgetState extends State<TimeBasedCountdownWidget> {
  late Timer _timer;
  DateTime? _endTime;
  Duration _difference = Duration.zero;
  bool _isValid = false;

  @override
  void initState() {
    super.initState();
    _updateState();
    // Check validity less frequently (every 10 seconds) but update countdown every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (timer.tick % 10 == 0) {
        // Only check validity every 10 seconds
        _updateValidity();
      }
      _updateCountdown();
    });
  }

  void _updateValidity() {
    if (!mounted) return;

    final newIsValid = widget.isOfferCurrentlyValid();
    if (newIsValid != _isValid) {
      setState(() {
        _isValid = newIsValid;
        _calculateEndTime();
      });
    }
  }

  void _updateCountdown() {
    if (!mounted || _endTime == null) return;

    final newDifference = _endTime!.difference(DateTime.now());
    if (newDifference.inSeconds != _difference.inSeconds) {
      setState(() {
        _difference = newDifference;
      });
    }
  }

  void _updateState() {
    if (!mounted) return;

    setState(() {
      _isValid = widget.isOfferCurrentlyValid();
      _calculateEndTime();
      if (_endTime != null) {
        _difference = _endTime!.difference(DateTime.now());
      }
    });
  }

  void _calculateEndTime() {
    if (!_isValid || widget.endTime.isEmpty) {
      _endTime = null;
      return;
    }

    final now = DateTime.now();

    // Parse end time in "h:mm AM/PM" format
    final parts = widget.endTime.split(' ');
    if (parts.length == 2) {
      final timePart = parts[0];
      final amPmPart = parts[1].toUpperCase();

      final timeComponents = timePart.split(':');
      if (timeComponents.length == 2) {
        int endHour = int.parse(timeComponents[0]);
        final endMinute = int.parse(timeComponents[1]);

        // Adjust hours based on AM/PM
        if (amPmPart == 'PM' && endHour < 12) {
          endHour += 12;
        } else if (amPmPart == 'AM' && endHour == 12) {
          endHour = 0; // 12 AM is 0 hours
        }

        // Create end time DateTime
        _endTime = DateTime(now.year, now.month, now.day, endHour, endMinute);

        // If end time is earlier than current time, it means it's for tomorrow
        if (_endTime!.isBefore(now)) {
          _endTime = _endTime!.add(const Duration(days: 1));
        }
      } else {
        // Fallback if time format is invalid
        _endTime = now.add(const Duration(hours: 1));
      }
    } else {
      // Fallback if time format is invalid
      _endTime = now.add(const Duration(hours: 1));
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_endTime == null || !_isValid) {
      return const SizedBox();
    }

    if (_difference.isNegative) {
      // If the difference is negative, recalculate the end time
      // This can happen if the offer just expired
      _calculateEndTime();
      if (_endTime == null || _difference.isNegative) {
        return const SizedBox();
      }
    }

    final days = _difference.inDays;
    final hours = _difference.inHours.remainder(24);
    final minutes = _difference.inMinutes.remainder(60);
    final seconds = _difference.inSeconds.remainder(60);

    return Align(
      alignment: Alignment.bottomLeft,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'Ends in: ',
                style: AppTextStyles.body.copyWith(
                  fontSize: 16,
                ),
              ),
              TextSpan(
                text: days > 0
                    ? '${days}d ${hours}h ${minutes}m'
                    : '${hours}h ${minutes}m ${seconds}s',
                style: AppTextStyles.bodyBold.copyWith(
                  fontSize: 16,
                  color: Colors.redAccent, // Red for countdown
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
