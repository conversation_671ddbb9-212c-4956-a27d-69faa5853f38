import 'package:bounce/bounce.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/helper/helper.dart';

import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/offer/offer_model.dart';

import 'package:vibeo/services/basket/basket_storage_service.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/views/basket/nfc_redemption_page.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:vibeo/widgets/offer/offer_info_dialog.dart';
import 'package:vibeo/widgets/tutorial/tutorial_manager.dart';
import 'package:vibeo/widgets/venue/countdown_widget.dart';
import 'package:vibeo/widgets/venue/perks_tag.dart';
import 'package:vibeo/widgets/offer/time_based_countdown_widget.dart';

class OfferTile extends StatefulWidget {
  // Changed from final to allow updating the offer state
  final OfferModel offer;
  final bool canNavigate;
  final bool canRedeem;
  final bool canAddToCart;
  final void Function(Widget productWidget, Offset position)? onAddToCart;
  const OfferTile({
    required this.offer,
    required this.canRedeem,
    this.canNavigate = false,
    this.canAddToCart = true,
    this.onAddToCart,
    super.key,
  });

  @override
  State<OfferTile> createState() => _OfferTileState();
}

class _OfferTileState extends State<OfferTile>
    with SingleTickerProviderStateMixin {
  late BasketStorageService basketStorageService;
  bool isAdded = false;
  bool canRedeemMore = false;

  // Animation controllers
  late AnimationController _lockAnimationController;
  late Animation<double> _lockScaleAnimation;
  late Animation<double> _lockOpacityAnimation;
  late Animation<double> _unlockShineAnimation;
  final bool _showUnlockAnimation = false;

  // Cache for validity check to avoid frequent recalculations
  DateTime? _lastValidityCheckTime;
  bool? _cachedValidityResult;
  final int _validityCheckCacheSeconds = 30; // Cache validity for 30 seconds

  // Check if an offer is currently valid based on time and weekday
  bool isOfferCurrentlyValid() {
    final now = DateTime.now();

    // Use cached result if available and not expired
    if (_lastValidityCheckTime != null && _cachedValidityResult != null) {
      final secondsSinceLastCheck =
          now.difference(_lastValidityCheckTime!).inSeconds;
      if (secondsSinceLastCheck < _validityCheckCacheSeconds) {
        return _cachedValidityResult!;
      }
    }

    // If there's no time or weekday restriction, the offer is always valid
    if ((widget.offer.startTime.isEmpty || widget.offer.endTime.isEmpty) &&
        widget.offer.weekdays.isEmpty) {
      _cacheValidityResult(true);
      return true;
    }

    final currentWeekday = _getCurrentWeekday(now);

    // Check weekday restriction
    if (widget.offer.weekdays.isNotEmpty) {
      bool isWeekdayValid = false;
      final weekdays = widget.offer.weekdays;

      // Optimize weekday check with direct lookup
      if (weekdays.contains(currentWeekday)) {
        isWeekdayValid = true;
      } else {
        // Check for ranges (e.g., 'Mon-Wed')
        for (final weekdayEntry in weekdays) {
          if (weekdayEntry.contains('-')) {
            final rangeParts = weekdayEntry.split('-');
            if (rangeParts.length == 2) {
              final startDay = rangeParts[0];
              final endDay = rangeParts[1];

              // Get the order of days for comparison
              const weekdayOrder = [
                'Mon',
                'Tue',
                'Wed',
                'Thu',
                'Fri',
                'Sat',
                'Sun',
              ];
              final startIndex = weekdayOrder.indexOf(startDay);
              final endIndex = weekdayOrder.indexOf(endDay);
              final currentIndex = weekdayOrder.indexOf(currentWeekday);

              // Check if current day is within the range (inclusive)
              if (startIndex <= currentIndex && currentIndex <= endIndex) {
                isWeekdayValid = true;
                break;
              }
            }
          }
        }
      }

      if (!isWeekdayValid) {
        _cacheValidityResult(false);
        return false;
      }
    }

    // Check time restriction
    if (widget.offer.startTime.isNotEmpty && widget.offer.endTime.isNotEmpty) {
      final currentTimeMinutes = now.hour * 60 + now.minute;

      // Parse start and end times with AM/PM format
      final startTimeMinutes = _parseTimeToMinutes(widget.offer.startTime);
      final endTimeMinutes = _parseTimeToMinutes(widget.offer.endTime);

      if (startTimeMinutes >= 0 && endTimeMinutes >= 0) {
        bool isTimeValid;

        // Handle cases where end time is on the next day (e.g., "2:00 AM")
        if (endTimeMinutes < startTimeMinutes) {
          // If we're past midnight but before end time, or before midnight but after start time
          isTimeValid = currentTimeMinutes < startTimeMinutes
              ? currentTimeMinutes <=
                  endTimeMinutes // Early morning (after midnight, before end time)
              : currentTimeMinutes >=
                  startTimeMinutes; // Evening (after start time, before midnight)
        } else {
          // Normal case: start time and end time on the same day
          isTimeValid = currentTimeMinutes >= startTimeMinutes &&
              currentTimeMinutes <= endTimeMinutes;
        }

        _cacheValidityResult(isTimeValid);
        return isTimeValid;
      }
    }

    // If we have weekday restriction but no time restriction, and we passed the weekday check
    if (widget.offer.weekdays.isNotEmpty &&
        (widget.offer.startTime.isEmpty || widget.offer.endTime.isEmpty)) {
      _cacheValidityResult(true);
      return true;
    }

    _cacheValidityResult(false);
    return false;
  }

  // Helper method to cache validity result
  void _cacheValidityResult(bool result) {
    _lastValidityCheckTime = DateTime.now();
    _cachedValidityResult = result;
  }

  // Helper method to parse time in "h:mm AM/PM" format to minutes since midnight
  int _parseTimeToMinutes(String timeStr) {
    try {
      // Split by space to separate time and AM/PM
      final parts = timeStr.split(' ');
      if (parts.length != 2) return -1;

      final timePart = parts[0];
      final amPmPart = parts[1].toUpperCase();

      // Split time by colon
      final timeComponents = timePart.split(':');
      if (timeComponents.length != 2) return -1;

      int hours = int.parse(timeComponents[0]);
      final minutes = int.parse(timeComponents[1]);

      // Adjust hours based on AM/PM
      if (amPmPart == 'PM' && hours < 12) {
        hours += 12;
      } else if (amPmPart == 'AM' && hours == 12) {
        hours = 0; // 12 AM is 0 hours
      }

      return hours * 60 + minutes;
    } catch (e) {
      AppLogger.debug('Error parsing time: $timeStr - $e');
      return -1;
    }
  }

  // Helper to get current weekday as string in the format used by the offer model
  String _getCurrentWeekday(DateTime date) {
    // The weekdays in the offer model are stored as abbreviated forms or ranges
    // like 'Mon', 'Tue', 'Mon-Wed', etc.
    switch (date.weekday) {
      case 1:
        return 'Mon';
      case 2:
        return 'Tue';
      case 3:
        return 'Wed';
      case 4:
        return 'Thu';
      case 5:
        return 'Fri';
      case 6:
        return 'Sat';
      case 7:
        return 'Sun';
      default:
        return '';
    }
  }

  @override
  void initState() {
    super.initState();
    final userID = context.read<UserBloc>().state.user!.uid;

    basketStorageService = BasketStorageService(userID);

    // Check if offer is already in basket
    _checkIfOfferInBasket();

    initAnimation();
    // Start animation if the offer is locked
    if (widget.offer.isLocked) {
      _lockAnimationController.repeat(reverse: false);
    }
  }

  Future<void> _checkIfOfferInBasket() async {
    if (widget.offer.id.isNotEmpty && widget.offer.venueID.isNotEmpty) {
      final inBasket = await basketStorageService.isOfferInBasket(
        widget.offer.venueID,
        widget.offer.id,
      );
      if (mounted) {
        setState(() {
          isAdded = inBasket;
        });
      }
    }
  }

  void initAnimation() {
    // Initialize animation controller
    _lockAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Scale animation for the lock icon
    _lockScaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1, end: 1.2)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.2, end: 1)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 40,
      ),
    ]).animate(_lockAnimationController);

    // Opacity animation for the lock icon
    _lockOpacityAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.7, end: 1)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1, end: 0.7)
            .chain(CurveTween(curve: Curves.easeInOut)),
        weight: 40,
      ),
    ]).animate(_lockAnimationController);

    // Shine animation for unlock effect
    _unlockShineAnimation = Tween<double>(
      begin: -1,
      end: 2,
    ).animate(
      CurvedAnimation(
        parent: _lockAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  Future<void> listClick(
    Widget productWidget,
    GlobalKey productKey,
  ) async {
    if (!isAdded || canRedeemMore) {
      final RenderBox box =
          productKey.currentContext!.findRenderObject()! as RenderBox;
      final position = box.localToGlobal(Offset.zero);

      // Trigger the animation
      widget.onAddToCart!(productWidget, position);

      AppLogger.info('Adding offer to basket: ${widget.offer.id}');
      AppLogger.info('Adding offer to basket: ${widget.offer.venueID}');

      // Add to basket storage
      await basketStorageService.addOfferToBasket(
        widget.offer.venueID,
        widget.offer.id,
      );

      // Trigger add to cart tutorial if this is the first time adding an offer
      _triggerAddToCartTutorialIfNeeded();
    } else {
      // Remove from basket storage
      await basketStorageService.removeOfferFromBasket(
        widget.offer.venueID,
        widget.offer.id,
      );
    }

    setState(() {
      isAdded = !isAdded;
    });
  }

  /// Trigger add to cart tutorial if it hasn't been shown before
  void _triggerAddToCartTutorialIfNeeded() {
    // Small delay to ensure the add to cart animation completes
    Future.delayed(const Duration(milliseconds: 1500), () async {
      if (mounted) {
        final shouldShow = await TutorialManager.instance
            .shouldShowTutorial('add_to_cart_flow');
        if (shouldShow && mounted) {
          await TutorialManager.instance.startAddToCartTutorial(context);
        }
      }
    });
  }

  @override
  void dispose() {
    _lockAnimationController.dispose();
    super.dispose();
  }

  Future<void> onOfferTapped() async {
    // final venue = await VenueRepository().fetchVenueByID(
    //   id: widget.offer.venueID,
    // );
    // if (context.mounted) {
    //   context.read<VenueBloc>().add(
    //         AddVenueEvent(venue),
    //       );

    //   await RouteUtils.pushNamed(
    //     context,
    //     RoutePaths.venueDescPage,
    //     arguments: {
    //       'venue': venue,
    //       'showOffersDirectly': true,
    //     },
    //   );
    // }
    // await Navigator.of(context).push(
    //   MaterialPageRoute(
    //     builder: (context) {
    //       return OfferInfoPage(
    //         offer: widget.offer,
    //       );
    //     },
    //   ),
    // );
    await OfferInfoDialog.show(context, widget.offer);
  }

  @override
  Widget build(BuildContext context) {
    final bool isExclusive = widget.offer.exclusive;
    final bool isLocked = widget.offer.isLocked;
    // Initialize the key in initState to ensure it's truly unique
    final GlobalKey productKey =
        GlobalKey(debugLabel: 'offer_item_${widget.offer.id}');

    final offerImage = offerImageWidget(
      isLocked: isLocked,
      productKey: productKey,
    );

    return Stack(
      children: [
        HapticButton(
          onTap: () {},
          child: IntrinsicHeight(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(bottom: 24),
                    decoration: BoxDecoration(
                      borderRadius: widget.canAddToCart
                          ? const BorderRadius.only(
                              topLeft: Radius.circular(20),
                              bottomLeft: Radius.circular(20),
                            )
                          : const BorderRadius.all(Radius.circular(20)),
                      gradient: const LinearGradient(
                        colors: [
                          Color.fromARGB(255, 38, 38, 38),
                          Color.fromARGB(255, 31, 31, 31),
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          constraints: const BoxConstraints(minHeight: 140),
                          decoration: BoxDecoration(
                            borderRadius: widget.canAddToCart
                                ? const BorderRadius.only(
                                    topLeft: Radius.circular(20),
                                    bottomLeft: Radius.circular(20),
                                  )
                                : const BorderRadius.all(Radius.circular(20)),
                            border: Border.all(
                              color: Colors.white10,
                              width: 1,
                            ),
                            boxShadow: AppShadowStyles.baseStyle,
                            gradient: const LinearGradient(
                              colors: [
                                Color.fromARGB(255, 41, 41, 41),
                                Color.fromARGB(255, 22, 22, 22),
                              ],
                            ),
                          ),
                          child: IntrinsicHeight(
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                offerImage,
                                offerInfoWidget(
                                  context,
                                  isExclusive: isExclusive,
                                ),
                              ],
                            ),
                          ),
                        ),
                        if (widget.offer.showCountdown &&
                            ((widget.offer.startTime.isNotEmpty &&
                                    widget.offer.endTime.isNotEmpty) ||
                                widget.offer.weekdays.isNotEmpty))
                          TimeBasedCountdownWidget(
                            startTime: widget.offer.startTime,
                            endTime: widget.offer.endTime,
                            weekdays: widget.offer.weekdays,
                            isOfferCurrentlyValid: isOfferCurrentlyValid,
                          ),

                        // if (widget.offer.id == '2')
                        //   Align(
                        //     alignment: Alignment.bottomRight,
                        //     child: Padding(
                        //       padding: const EdgeInsets.symmetric(
                        //         horizontal: 16,
                        //         vertical: 12,
                        //       ),
                        //       child: Text(
                        //         'Only 10 Left!',
                        //         style: AppTextStyles.body.copyWith(
                        //           fontSize: 16,
                        //           color: Colors.redAccent,
                        //           fontWeight: FontWeight.bold,
                        //         ),
                        //       ),
                        //     ),
                        //   ),
                      ],
                    ),
                  ),
                ),
                if (widget.canAddToCart)
                  addToBasketWidget(
                    () => listClick(
                      offerImage,
                      productKey,
                    ),
                  ),
              ],
            ),
          ),
        ),
        if (widget.offer.isRedeemedToday && widget.canRedeem)
          alreadyRedeemOfferWidget(),
      ],
    );
  }

  Positioned alreadyRedeemOfferWidget() {
    return Positioned.fill(
      child: Container(
        alignment: Alignment.center,
        margin: const EdgeInsets.only(bottom: 24),
        decoration: BoxDecoration(
          borderRadius: widget.canAddToCart
              ? const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                )
              : const BorderRadius.all(Radius.circular(20)),
          color: Colors.black.withAlpha(120),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // // Lock icon with checkmark
            // Stack(
            //   alignment: Alignment.center,
            //   children: [
            //     // Outer circle with lock effect
            //     Container(
            //       padding: const EdgeInsets.all(16),
            //       decoration: BoxDecoration(
            //         shape: BoxShape.circle,
            //         gradient: LinearGradient(
            //           begin: Alignment.topLeft,
            //           end: Alignment.bottomRight,
            //           colors: [
            //             Colors.teal.withAlpha((255 * 0.9).toInt()),
            //             Colors.cyan.withAlpha((255 * 0.9).toInt()),
            //           ],
            //         ),
            //         boxShadow: [
            //           BoxShadow(
            //             color: Colors.cyanAccent.withAlpha((255 * 0.3).toInt()),
            //             blurRadius: 15,
            //             spreadRadius: 5,
            //           ),
            //         ],
            //         border: Border.all(
            //           color: Colors.white.withAlpha(77),
            //           width: 2,
            //         ),
            //       ),
            //       child: const Icon(
            //         CupertinoIcons.lock_fill,
            //         color: Colors.white,
            //         size: 40,
            //       ),
            //     ),
            //   ],
            // ),
            // const SizedBox(height: 12),
            // const Text(
            //   'Offer Already Redeemed',
            //   style: TextStyle(
            //     color: Colors.white,
            //     fontSize: 16,
            //     fontWeight: FontWeight.bold,
            //   ),
            // ),
          ],
        ),
      ),
    );
  }

  ClipRRect offerImageWidget({
    required GlobalKey productKey,
    bool isLocked = false,
  }) {
    return ClipRRect(
      key: productKey,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(20),
        bottomLeft: Radius.circular(20),
      ),
      child: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              color: Colors.white10,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                bottomLeft: Radius.circular(20),
              ),
            ),
            child: CachedNetworkImage(
              imageUrl: widget.offer.imageLink,
              width: 100, // Fixed width
              maxWidthDiskCache: 200,
              fit: BoxFit.fitHeight,
              imageBuilder: (context, imageProvider) => Stack(
                children: [
                  Container(
                    width: 100,
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                        alignment: Alignment.topCenter,
                        colorFilter: isLocked
                            ? const ColorFilter.matrix([
                                0.2126,
                                0.7152,
                                0.0722,
                                0,
                                0,
                                0.2126,
                                0.7152,
                                0.0722,
                                0,
                                0,
                                0.2126,
                                0.7152,
                                0.0722,
                                0,
                                0,
                                0,
                                0,
                                0,
                                0.5,
                                0,
                              ])
                            : null,
                      ),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.bottomCenter,
                          end: Alignment.topCenter,
                          colors: [
                            Colors.black.withAlpha(200),
                            Colors.transparent,
                          ],
                          stops: const [0.0, 0.6],
                        ),
                      ),
                    ),
                  ),
                  // Shine effect for unlock animation
                  if (_showUnlockAnimation)
                    AnimatedBuilder(
                      animation: _unlockShineAnimation,
                      builder: (context, child) {
                        return Positioned.fill(
                          child: Transform.rotate(
                            angle: 0.7, // ~40 degrees
                            child: Transform.translate(
                              offset: Offset(
                                _unlockShineAnimation.value * 200,
                                0,
                              ),
                              child: Container(
                                width: 50,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                    colors: [
                                      Colors.white.withAlpha(0),
                                      Colors.white.withAlpha(128),
                                      Colors.white.withAlpha(0),
                                    ],
                                    stops: const [0.0, 0.5, 1.0],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                ],
              ),
              placeholder: (context, url) => Shimmer.fromColors(
                baseColor: Colors.grey[300]!,
                highlightColor: Colors.grey[100]!,
                child: Container(
                  width: 100,
                  decoration: const BoxDecoration(
                    color: Colors.white60,
                  ),
                ),
              ),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          // Lock overlay for locked offers
          if (isLocked)
            AnimatedBuilder(
              animation: _lockAnimationController,
              builder: (context, child) {
                return Positioned.fill(
                  child: Center(
                    child: Opacity(
                      opacity: _lockOpacityAnimation.value,
                      child: Transform.scale(
                        scale: _lockScaleAnimation.value,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.black.withAlpha(153),
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white.withAlpha(77),
                              width: 2,
                            ),
                          ),
                          child: const Icon(
                            CupertinoIcons.lock_fill,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  Bounce addToBasketWidget(void Function() listClick) {
    return Bounce(
      onTap: listClick,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(bottom: 24),
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: isAdded
              ? Colors.deepPurpleAccent.withAlpha(80)
              : const Color.fromARGB(63, 255, 255, 255),
          borderRadius: const BorderRadius.only(
            topRight: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
          border: Border.all(
            color: isAdded ? Colors.deepPurpleAccent.shade200 : Colors.white60,
            width: 1,
          ),
        ),
        child: !isAdded || !canRedeemMore
            ? Icon(
                isAdded && !canRedeemMore
                    ? CupertinoIcons.check_mark
                    : CupertinoIcons.add,
                color: Colors.white,
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: listClick,
                    style: IconButton.styleFrom(
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                    ),
                    icon: const Icon(
                      CupertinoIcons.add,
                      color: Colors.white,
                    ),
                  ),
                  IconButton(
                    onPressed: listClick,
                    style: IconButton.styleFrom(
                      iconSize: 20,
                    ),
                    icon: const Icon(
                      CupertinoIcons.minus,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Expanded offerInfoWidget(BuildContext context, {bool isExclusive = false}) {
    final bool isLocked = widget.offer.isLocked;

    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Tags row for offer type and status
            Row(
              children: [
                if (isExclusive)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12, right: 8),
                    child: PerksTag(
                      colors: [
                        darkAppColors.deepPurple.withAlpha(204),
                        darkAppColors.purpleShade.withAlpha(204),
                      ],
                      icon: CupertinoIcons.star_fill,
                      title: 'EXCLUSIVE',
                    ),
                  ),
                if (isLocked)
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: 12,
                      right: 6,
                    ),
                    child: PerksTag(
                      colors: [
                        Colors.grey.withAlpha(204),
                        Colors.grey.shade700.withAlpha(204),
                      ],
                      icon: CupertinoIcons.lock_fill,
                      title: 'LOCKED',
                    ),
                  ),
                // VIBEPOINTS FEATURE COMMENTED OUT
                /*
                if (redemptionType == OfferRedemptionType.vibePointsRedeem ||
                    redemptionType == OfferRedemptionType.vibePointsUnlock)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: PerksTag(
                      colors: [
                        Colors.purple.withAlpha(204),
                        Colors.purpleAccent.withAlpha(204),
                      ],
                      icon: CupertinoIcons.sparkles,
                      title: 'VIBEPOINTS',
                    ),
                  ),
                */
              ],
            ),

            // Title with locked effect if needed
            Text(
              widget.offer.title.trim(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: isLocked
                  ? AppTextStyles.title
                      .copyWith(color: Colors.white.withAlpha(128))
                  : AppTextStyles.title,
            ),
            const SizedBox(
              height: 5,
            ),

            // Description with locked effect if needed
            Text(
              widget.offer.description.trim(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyles.bodySmaller.copyWith(
                color: isLocked ? Colors.white30 : Colors.white70,
                fontSize: 14,
              ),
            ),

            // Time and weekday information on the same line
            if ((widget.offer.startTime.isNotEmpty &&
                    widget.offer.endTime.isNotEmpty) ||
                widget.offer.weekdays.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    // Time information
                    if (widget.offer.startTime.isNotEmpty &&
                        widget.offer.endTime.isNotEmpty) ...[
                      const Icon(
                        CupertinoIcons.clock,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.offer.startTime} - ${widget.offer.endTime}',
                        style: const TextStyle(
                          color: Colors.amber,
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],

                    // Weekdays information
                    if (widget.offer.weekdays.isNotEmpty) ...[
                      Expanded(
                        child: Text(
                          widget.offer.weekdays.join(', '),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                            color: Colors.amber,
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

            // Price widget if applicable
            if (widget.offer.offerValue != null &&
                    widget.offer.offerValue != 0 ||
                widget.offer.discountedValue != null)
              offerPriceWidget(),

            // VIBEPOINTS FEATURE COMMENTED OUT
            // VibePoints cost/requirement indicator
            /*
            if (redemptionType == OfferRedemptionType.vibePointsRedeem ||
                redemptionType == OfferRedemptionType.vibePointsUnlock)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    const Icon(
                      CupertinoIcons.sparkles,
                      color: Colors.purpleAccent,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.offer.vibePointsCost} VibePoints',
                      style: TextStyle(
                        color: userVibePoints >= widget.offer.vibePointsCost
                            ? Colors.purpleAccent
                            : Colors.grey,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (redemptionType ==
                            OfferRedemptionType.vibePointsUnlock &&
                        userVibePoints < widget.offer.vibePointsCost)
                      Padding(
                        padding: const EdgeInsets.only(left: 4),
                        child: Text(
                          '(Need ${widget.offer.vibePointsCost - userVibePoints} more)',
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            */

            // Referral requirement indicator
            // if (redemptionType == OfferRedemptionType.referUnlock)
            //   Padding(
            //     padding: const EdgeInsets.only(top: 8),
            //     child: Row(
            //       children: [
            //         Icon(
            //           CupertinoIcons.person_add,
            //           color: userReferrals >= widget.offer.requiredReferrals
            //               ? Colors.greenAccent
            //               : Colors.grey,
            //           size: 16,
            //         ),
            //         const SizedBox(width: 4),
            //         Text(
            //           '${widget.offer.requiredReferrals} Referrals Required',
            //           style: TextStyle(
            //             color: userReferrals >= widget.offer.requiredReferrals
            //                 ? Colors.greenAccent
            //                 : Colors.grey,
            //             fontWeight: FontWeight.w500,
            //             fontSize: 12,
            //           ),
            //         ),
            //         if (userReferrals < widget.offer.requiredReferrals)
            //           Padding(
            //             padding: const EdgeInsets.only(left: 4),
            //             child: Text(
            //               '(Need ${widget.offer.requiredReferrals - userReferrals} more)',
            //               style: const TextStyle(
            //                 color: Colors.grey,
            //                 fontSize: 12,
            //               ),
            //             ),
            //           ),
            //       ],
            //     ),
            //   ),

            const SizedBox(
              height: 5,
            ),

            // Action buttons
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Details button
                if (widget.offer.info != null)
                  GestureDetector(
                    onTap: onOfferTapped,
                    child: const Padding(
                      padding: EdgeInsets.only(top: 8),
                      child: Text(
                        'Details',
                        style: TextStyle(
                          color: Color.fromARGB(
                            255,
                            226,
                            226,
                            226,
                          ),
                        ),
                      ),
                    ),
                  ),
                const Spacer(),
                // External link button
                if (widget.offer.externalLink != null && !isLocked)
                  Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: GestureDetector(
                      onTap: () {
                        openEventLink(
                          widget.offer.externalLink!,
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 6,
                        ),
                        margin: const EdgeInsets.only(top: 8),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(
                            Radius.circular(16),
                          ),
                          color: Colors.white,
                        ),
                        child: Text(
                          'Claim Offer',
                          style: TextStyle(
                            color: darkAppColors.secondaryBackgroundColor,
                          ),
                        ),
                      ),
                    ),
                  )
                // Free redeem button
                else if (widget.offer.canRedeem &&
                    !isLocked &&
                    widget.canRedeem &&
                    !widget.offer.isRedeemedToday)
                  Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: GestureDetector(
                      onTap: isOfferCurrentlyValid()
                          ? () async {
                              final AnalyticsService analytics =
                                  AnalyticsService.instance;
                              await analytics
                                  .trackOfferRedeemedClicked(widget.offer.id);
                              if (context.mounted) {
                                await showRedemptionDisclaimer(
                                  context,
                                  widget.offer,
                                  widget.offer.venueID,
                                );
                              }
                            }
                          : null,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        margin: const EdgeInsets.only(top: 8),
                        decoration: BoxDecoration(
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withAlpha(40),
                              blurRadius: 5,
                              spreadRadius: 0.001,
                            ),
                          ],
                          color: isOfferCurrentlyValid()
                              ? Colors.white10
                              : Colors.white10.withAlpha(5),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isOfferCurrentlyValid()
                                ? Colors.white.withAlpha(80)
                                : Colors.white.withAlpha(30),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          isOfferCurrentlyValid()
                              ? 'Redeem Now'
                              : 'Available Soon',
                          style: TextStyle(
                            color: isOfferCurrentlyValid()
                                ? Colors.white
                                : Colors.white.withAlpha(100),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                // VibePoints redeem button
                // else if (widget.canRedeem && !isLocked)
                //   Padding(
                //     padding: const EdgeInsets.only(left: 12),
                //     child: GestureDetector(
                //       onTap: isOfferCurrentlyValid()
                //           ? () {
                //               if (userVibePoints >=
                //                   widget.offer.vibePointsCost) {
                //                 // In a real app, deduct points here
                //                 setState(() {
                //                   userVibePoints -= widget.offer.vibePointsCost;
                //                 });
                //                 showRedemptionDisclaimer(context);
                //               } else {
                //                 // Show not enough points message
                //                 ScaffoldMessenger.of(context).showSnackBar(
                //                   SnackBar(
                //                     content: Text(
                //                       'Not enough VibePoints! You need ${widget.offer.vibePointsCost - userVibePoints} more.',
                //                       style:
                //                           const TextStyle(color: Colors.white),
                //                     ),
                //                     backgroundColor: darkAppColors.errorColor,
                //                   ),
                //                 );
                //               }
                //             }
                //           : null,
                //       child: Container(
                //         padding: const EdgeInsets.symmetric(
                //           horizontal: 16,
                //           vertical: 6,
                //         ),
                //         margin: const EdgeInsets.only(top: 8),
                //         decoration: BoxDecoration(
                //           borderRadius: const BorderRadius.all(
                //             Radius.circular(16),
                //           ),
                //           color: !isOfferCurrentlyValid()
                //               ? Colors.white.withAlpha(40)
                //               : userVibePoints >= widget.offer.vibePointsCost
                //                   ? Colors.white
                //                   : Colors.white.withAlpha(77),
                //         ),
                //         child: Row(
                //           children: [
                //             Text(
                //               !isOfferCurrentlyValid()
                //                   ? 'Available Soon'
                //                   : 'Redeem Now',
                //               style: TextStyle(
                //                 color: !isOfferCurrentlyValid()
                //                     ? darkAppColors.lightColor
                //                         .withAlpha(100) // ~70% opacity
                //                     : darkAppColors.secondaryBackgroundColor,
                //                 fontWeight: FontWeight.w400,
                //               ),
                //             ),
                //           ],
                //         ),
                //       ),
                //     ),
                //   )
                // VIBEPOINTS FEATURE COMMENTED OUT
                // Unlock button for VibePoints unlock
                /*
                else if (redemptionType ==
                        OfferRedemptionType.vibePointsUnlock &&
                    isLocked)
                  Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: GestureDetector(
                      onTap: () {
                        if (userVibePoints >= widget.offer.vibePointsCost) {
                          // In a real app, deduct points and unlock here
                          setState(() {
                            userVibePoints -= widget.offer.vibePointsCost;
                            _showUnlockAnimation = true;
                            // widget.offer =
                            //     widget.offer.copyWith(isLocked: false);
                          });

                          // Reset and play the unlock animation
                          _lockAnimationController.reset();
                          _lockAnimationController.forward().then((_) {
                            // After animation completes, update the UI
                            setState(() {
                              _showUnlockAnimation = false;
                            });

                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Offer unlocked successfully!'),
                                backgroundColor: Colors.greenAccent,
                              ),
                            );
                          });
                        } else {
                          // Show not enough points message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Not enough VibePoints! You need ${widget.offer.vibePointsCost - userVibePoints} more.',
                                style: const TextStyle(color: Colors.white),
                              ),
                              backgroundColor: darkAppColors.errorColor,
                            ),
                          );
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 6,
                        ),
                        margin: const EdgeInsets.only(top: 8),
                        decoration: BoxDecoration(
                          borderRadius: const BorderRadius.all(
                            Radius.circular(16),
                          ),
                          color: userVibePoints >= widget.offer.vibePointsCost
                              ? Colors.white
                              : Colors.white.withAlpha(77),
                        ),
                        child: Row(
                          children: [
                            Text(
                              'Unlock for ',
                              style: TextStyle(
                                color: darkAppColors.secondaryBackgroundColor,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            Icon(
                              CupertinoIcons.sparkles,
                              color: darkAppColors.secondaryBackgroundColor,
                              size: 14,
                            ),
                            Text(
                              ' ${widget.offer.vibePointsCost}',
                              style: TextStyle(
                                color: darkAppColors.secondaryBackgroundColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                */
                // Unlock button for Referral unlock
                // else if (redemptionType == OfferRedemptionType.referUnlock &&
                //     isLocked)
                //   Padding(
                //     padding: const EdgeInsets.only(left: 12),
                //     child: GestureDetector(
                //       onTap: () {
                //         if (userReferrals >= widget.offer.requiredReferrals) {
                //           // In a real app, mark as unlocked in the backend
                //           setState(() {
                //             _showUnlockAnimation = true;
                //           });

                //           // Reset and play the unlock animation
                //           _lockAnimationController.reset();
                //           _lockAnimationController.forward().then((_) {
                //             // After animation completes, update the UI
                //             setState(() {
                //               _showUnlockAnimation = false;
                //             });

                //             ScaffoldMessenger.of(context).showSnackBar(
                //               const SnackBar(
                //                 content: Text('Offer unlocked successfully!'),
                //                 backgroundColor: Colors.greenAccent,
                //               ),
                //             );
                //           });
                //         } else {
                //           // Show not enough referrals message
                //           ScaffoldMessenger.of(context).showSnackBar(
                //             SnackBar(
                //               content: Text(
                //                 'Not enough referrals! You need ${widget.offer.requiredReferrals - userReferrals} more.',
                //                 style: const TextStyle(color: Colors.white),
                //               ),
                //               backgroundColor: darkAppColors.errorColor,
                //             ),
                //           );
                //         }
                //       },
                //       child: Container(
                //         padding: const EdgeInsets.symmetric(
                //           horizontal: 16,
                //           vertical: 6,
                //         ),
                //         margin: const EdgeInsets.only(top: 8),
                //         decoration: BoxDecoration(
                //           borderRadius: const BorderRadius.all(
                //             Radius.circular(16),
                //           ),
                //           color: userReferrals >= widget.offer.requiredReferrals
                //               ? Colors.white
                //               : Colors.white.withAlpha(77),
                //         ),
                //         child: Row(
                //           children: [
                //             Text(
                //               'Unlock with Referrals',
                //               style: TextStyle(
                //                 color: darkAppColors.secondaryBackgroundColor,
                //                 fontWeight: FontWeight.w400,
                //                 fontSize: 13,
                //               ),
                //             ),
                //           ],
                //         ),
                //       ),
                //     ),
                //   ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Builder countdownWidget() {
    return Builder(
      builder: (context) {
        final expiryDate = DateTime.parse(widget.offer.endDate);
        // final expiryDate = DateTime.now().add(const Duration(days: 1));
        return Padding(
          padding: const EdgeInsets.only(
            right: 16,
          ),
          child: CountdownWidget(
            expiryDate: expiryDate,
          ),
        );
      },
    );
  }

  Padding offerPriceWidget() {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Row(
        children: [
          if (widget.offer.discountedValue != null)
            Row(
              children: [
                Text(
                  '\$${widget.offer.offerValue}',
                  style: AppTextStyles.bodySmall.copyWith(
                    decoration: TextDecoration.lineThrough,
                    color: Colors.white60,
                  ),
                ),
                const SizedBox(width: 8),
              ],
            )
          else if (widget.offer.discountedValue == null)
            Row(
              children: [
                Text(
                  '\$${widget.offer.offerValue}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ShaderMask(
            blendMode: BlendMode.srcIn,
            shaderCallback: (bounds) => const LinearGradient(
              colors: [
                Colors.greenAccent,
                Colors.lightGreenAccent,
              ],
            ).createShader(
              Rect.fromLTWH(
                0,
                0,
                bounds.width,
                bounds.height,
              ),
            ),
            child: Text(
              '\$${widget.offer.discountedValue!.toInt()}',
              style: AppTextStyles.bodysmallBold.copyWith(
                fontSize: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
