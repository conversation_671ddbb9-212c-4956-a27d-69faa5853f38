import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/constants/video_tiles.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';

class VibeTileShimmer extends StatelessWidget {
  const VibeTileShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Container(
        margin: const EdgeInsets.only(right: 10),
        width: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(VideoTileSizeUtils.borderRadius),
          ),
          boxShadow: AppShadowStyles.baseStyle,
          color: darkAppColors.secondaryBackgroundColor,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.all(
            Radius.circular(VideoTileSizeUtils.borderRadius),
          ),
          child: Stack(
            children: [
              // Base shimmer
              RepaintBoundary(
                child: Shimmer.fromColors(
                  baseColor: Colors.white12,
                  highlightColor: Colors.white38,
                  child: AspectRatio(
                    aspectRatio: 11 / 16,
                    child: Container(
                      color: Colors.black38,
                    ),
                  ),
                ),
              ),

              // Glassmorphic overlay
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        Colors.transparent,
                        Colors.black.withAlpha(102),
                        Colors.black,
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: const [0, 0.5, 0.8, 1],
                    ),
                  ),
                ),
              ),

              // Content shimmer
              Positioned(
                bottom: 20,
                left: 15,
                right: 15,
                child: RepaintBoundary(
                  child: Shimmer.fromColors(
                    baseColor: Colors.grey[800]!,
                    highlightColor: Colors.grey[600]!,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildShimmerRow(),
                        const SizedBox(height: 5),
                        _buildShimmer2Row(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerRow() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 15,
          height: 15,
          decoration: const BoxDecoration(
            color: Colors.white54,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 5),
        Container(
          width: 80,
          height: 15,
          decoration: const BoxDecoration(
            color: Colors.white54,
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmer2Row() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 100,
          height: 20,
          decoration: const BoxDecoration(
            color: Colors.white54,
            borderRadius: BorderRadius.all(Radius.circular(100)),
          ),
        ),
      ],
    );
  }
}
