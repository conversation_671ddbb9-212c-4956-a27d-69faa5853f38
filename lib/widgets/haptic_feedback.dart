import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pinput/pinput.dart';

class HapticButton extends StatelessWidget {
  final Widget child;
  final VoidCallback onTap;
  final HapticFeedbackType type;

  const HapticButton({
    required this.child,
    required this.onTap,
    this.type = HapticFeedbackType.mediumImpact,
    super.key,
  });

  void _handleTap() {
    switch (type) {
      case HapticFeedbackType.lightImpact:
        HapticFeedback.lightImpact();
      case HapticFeedbackType.mediumImpact:
        HapticFeedback.mediumImpact();
      case HapticFeedbackType.heavyImpact:
        HapticFeedback.heavyImpact();
      case HapticFeedbackType.selectionClick:
        HapticFeedback.selectionClick();
      case HapticFeedbackType.vibrate:
        HapticFeedback.vibrate();
      case HapticFeedbackType.disabled:
        break;
    }
    onTap();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: child,
    );
  }
}
