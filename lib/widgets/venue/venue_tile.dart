import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/logic/venue/controller/venue_controller.dart';
import 'package:vibeo/models/venue/venue_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';

import 'package:vibeo/widgets/feed/live_widget.dart';
import 'package:vibeo/widgets/global/buildPerkTag.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:vibeo/widgets/miles_away_widget.dart';

class VenueTile extends StatefulWidget {
  final int index;
  final VenueModel venue;
  final bool addVenueExplicitly;
  const VenueTile({
    required this.index,
    required this.venue,
    this.addVenueExplicitly = false,
    super.key,
  });

  @override
  State<VenueTile> createState() => _VenueTileState();
}

class _VenueTileState extends State<VenueTile> {
  late VenueController _venueController;
  bool _isSaved = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    final userID = context.read<UserBloc>().state.user!.uid;
    _venueController = VenueController(userID);
    _checkSavedStatus();
  }

  Future<void> _checkSavedStatus() async {
    if (!mounted) return;

    try {
      final saved = await _venueController.checkIfVenueSaved(widget.venue.id);
      if (!mounted) return;

      setState(() {
        _isSaved = saved;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleSaveToggle() async {
    if (_isLoading) return;

    try {
      setState(() => _isLoading = true);
      final saved = await _venueController.onVenueSaved(widget.venue.id);
      if (!mounted) return;

      setState(() {
        _isSaved = saved;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: HapticButton(
        onTap: () async {
          final AnalyticsService analytics = AnalyticsService.instance;
          await analytics.logVenueInteraction(
            venueId: widget.venue.id,
            userId: context.read<UserBloc>().state.user!.uid,
          );
          if (context.mounted) {
            if (widget.addVenueExplicitly) {
              context.read<VenueBloc>().add(
                    AddVenueEvent(
                      widget.venue,
                    ),
                  );
            }
            await RouteUtils.pushNamed(
              context,
              RoutePaths.venueDescPage,
              arguments: {'venue': widget.venue},
            );
          }
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            border: Border.all(
              color: Colors.white.withAlpha(26),
            ),
            boxShadow: [
              BoxShadow(
                offset: const Offset(0, -5),
                color: Colors.black.withAlpha(89),
                blurRadius: 5,
                spreadRadius: 0.01,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.all(Radius.circular(16)),
            child: Stack(
              children: [
                CachedNetworkImage(
                  imageUrl: widget.venue.imageLink,
                  width: 400,
                  height: 200,
                  maxHeightDiskCache: 200,
                  maxWidthDiskCache: 400,
                  memCacheWidth: 400,
                  memCacheHeight: 200,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Shimmer.fromColors(
                    baseColor: darkAppColors.secondaryBackgroundColor,
                    highlightColor: darkAppColors.secondaryBackgroundColor,
                    child: ColoredBox(
                      color: darkAppColors.secondaryBackgroundColor,
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[300],
                    child: const Icon(Icons.error),
                  ),
                ),

                // Existing glassmorphic overlay...
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerRight,
                        end: Alignment.centerLeft,
                        colors: [
                          Colors.black45,
                          Colors.black38,
                          Colors.black26,
                          Colors.black.withAlpha(204),
                          Colors.black.withAlpha(217),
                        ],
                      ),
                    ),
                  ),
                ),
                // Live indicator
                if (widget.venue.isLive &&
                    !widget.venue.perksType.contains(PerkVenueType.offers))
                  const Positioned(
                    top: 16,
                    left: 16,
                    child: LiveWidget(),
                  ),
                // Offer badge
                if (widget.venue.perksType.isNotEmpty || widget.venue.exclusive)
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Row(
                      children: [
                        // Determine which perk tag to show based on available perks
                        buildPerkTag(
                          widget.venue.perksType,
                          isExclusive: widget.venue.exclusive,
                        ),

                        // Show live indicator if venue is live
                        if (widget.venue.isLive)
                          const Padding(
                            padding: EdgeInsets.only(left: 8),
                            child: LiveWidget(),
                          ),
                      ],
                    ),
                  ),

                // Content
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.only(
                      left: 16,
                      top: 16,
                      right: 16,
                      bottom: 16,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: widget.venue.name,
                                style: const TextStyle(
                                  fontFamily: 'PulpDisplay',
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (widget.venue.exclusive)
                                WidgetSpan(
                                  alignment: PlaceholderAlignment
                                      .bottom, // Align with the middle of text
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      left: 6,
                                    ), // Small space between text and badge
                                    child: Image.asset(
                                      'assets/venue/badge.png',
                                      height: 22,
                                      width: 22,
                                      cacheHeight: 40,
                                      cacheWidth: 40,
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        // Description
                        Text(
                          widget.venue.description,
                          style: TextStyle(
                            color: Colors.white.withAlpha(204),
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 12),

                        Row(
                          children: [
                            const Icon(
                              CupertinoIcons.location_fill,
                              color: Colors.white,
                              size: 15,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              widget.venue.area,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 15,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            if (widget.venue.milesAway != null)
                              MilesAwayWidget(
                                milesAway:
                                    widget.venue.milesAway!.toStringAsFixed(1),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Favorite Button
                Positioned(
                  top: 8,
                  right: 8,
                  child: GestureDetector(
                    onTap: _handleSaveToggle,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : Icon(
                              _isSaved
                                  ? CupertinoIcons.heart_fill
                                  : CupertinoIcons.heart,
                              color: _isSaved ? Colors.pink : Colors.white,
                              size: 20,
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
