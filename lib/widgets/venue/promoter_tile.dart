import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/models/promoters/promoter_model.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';

class PromoterTile extends StatelessWidget {
  const PromoterTile({
    required this.promoter,
    required this.venueID,
    super.key,
  });

  final PromoterModel promoter;
  final String venueID;

  @override
  Widget build(BuildContext context) {
    return Container(
      clipBehavior: Clip.none,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(
          Radius.circular(20),
        ),
        boxShadow: AppShadowStyles.baseStyle,
        gradient: const LinearGradient(
          colors: [
            Color.fromARGB(255, 14, 16, 109),
            Color.fromARGB(255, 29, 9, 79),
          ],
        ),
      ),
      child: HapticButton(
        onTap: () async {
          final AnalyticsService analytics = AnalyticsService.instance;
          await analytics.trackPromoterDM(
            venueID,
            promoter.id,
          );
          await openSocialHandleLink(
            promoter.socialHandle,
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.only(
                  top: 16,
                  left: 16,
                  right: 16,
                  bottom: 16,
                ),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(
                    Radius.circular(20),
                  ),
                  boxShadow: AppShadowStyles.baseStyle,
                  gradient: const LinearGradient(
                    colors: [
                      Color.fromARGB(255, 18, 20, 147),
                      Color.fromARGB(255, 84, 27, 227),
                    ],
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                promoter.title,
                                style: AppTextStyles.title.copyWith(
                                  color: Colors.white70,
                                ),
                              ),
                              const SizedBox(width: 10),
                              const Icon(Icons.verified),
                            ],
                          ),
                          const SizedBox(height: 5),
                          Text(
                            promoter.subTitle,
                            style: AppTextStyles.titleMedium.copyWith(
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 5),
                          Text(
                            promoter.description,
                            style: AppTextStyles.bodySmaller.copyWith(
                              color: Colors.white60,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 5),
                    Stack(
                      children: [
                        ClipRRect(
                          borderRadius: const BorderRadius.all(
                            Radius.circular(15),
                          ),
                          child: CachedNetworkImage(
                            imageUrl: promoter.image,
                            fit: BoxFit.cover,
                            maxHeightDiskCache: 180,
                            maxWidthDiskCache: 130,
                            imageBuilder: (context, imageProvider) => Container(
                              width: 130,
                              height: 180,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                  alignment: Alignment.topCenter,
                                ),
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.topCenter,
                                    colors: [
                                      Colors.black.withAlpha(200),
                                      Colors.transparent,
                                    ],
                                    stops: const [0.0, 0.6],
                                  ),
                                ),
                              ),
                            ),
                            // memCacheWidth: (130 *
                            //         MediaQuery.of(context)
                            //             .devicePixelRatio)
                            //     .toInt(),
                            // memCacheHeight: (180 *
                            //         MediaQuery.of(context)
                            //             .devicePixelRatio)
                            //     .toInt(),
                            placeholder: (context, url) => Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              child: Container(
                                width: 130,
                                height: 180,
                                decoration: const BoxDecoration(
                                  borderRadius: BorderRadius.all(
                                    Radius.circular(15),
                                  ),
                                  color: Colors.white60,
                                ),
                              ),
                            ),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        ),
                        SizedBox(
                          width: 120,
                          child: Stack(
                            children: [
                              Positioned(
                                bottom: 10,
                                right: 10,
                                child: Container(
                                  height: 35,
                                  width: 35,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                      100,
                                    ),
                                    color: Colors.white.withAlpha(
                                      (255 * 0.2).toInt(),
                                    ),
                                    border: Border.all(
                                      color: Colors.white.withAlpha(
                                        (255 * 0.3).toInt(),
                                      ),
                                      width: 1,
                                    ),
                                  ),
                                  child: const Icon(
                                    CupertinoIcons.paperplane_fill,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
