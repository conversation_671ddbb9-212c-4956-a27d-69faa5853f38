import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vibeo/themes/text_theme.dart';

class CountdownWidget extends StatefulWidget {
  final DateTime expiryDate;

  const CountdownWidget({required this.expiryDate, super.key});

  @override
  State<CountdownWidget> createState() => _CountdownWidgetState();
}

class _CountdownWidgetState extends State<CountdownWidget> {
  late Timer _timer;
  late DateTime _expiryDate;
  Duration _difference = Duration.zero;

  @override
  void initState() {
    super.initState();
    _expiryDate = widget.expiryDate;
    _updateDifference();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateDifference();
    });
  }

  void _updateDifference() {
    setState(() {
      _difference = _expiryDate.difference(DateTime.now());
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_difference.isNegative) {
      return const SizedBox();
    }

    final days = _difference.inDays;
    final hours = _difference.inHours.remainder(24);
    final minutes = _difference.inMinutes.remainder(60);
    final seconds = _difference.inSeconds.remainder(60);

    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'Ends in: ',
                style: AppTextStyles.body,
              ),
              TextSpan(
                text: '${days}d ${hours}h ${minutes}m ${seconds}s',
                style: AppTextStyles.bodyBold
                    .copyWith(color: const Color.fromARGB(255, 255, 136, 136)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
