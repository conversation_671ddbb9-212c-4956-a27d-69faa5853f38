import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/constants/video_tiles.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/widgets/feed/live_widget.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

Widget buildVenueItem(
  VenueModel venue,
  int index,
  BuildContext context,
  VoidCallback handleVenueTap,
) {
  return HapticButton(
    onTap: handleVenueTap,
    child: Container(
      margin: const EdgeInsets.only(
        right: 12,
      ),
      width: 280,
      decoration: BoxDecoration(
        color: darkAppColors.secondaryBackgroundColor,
        boxShadow: AppShadowStyles.baseStyle,
        borderRadius: BorderRadius.all(
          Radius.circular(VideoTileSizeUtils.borderRadius),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        child: Stack(
          children: [
            CachedNetworkImage(
              imageUrl: venue.imageLink,
              width: 400,
              height: 200,
              maxHeightDiskCache: 200,
              maxWidthDiskCache: 400,
              fit: BoxFit.cover,
              imageBuilder: (context, imageProvider) => Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: imageProvider,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),

            // Glassmorphic overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [
                      Colors.black26,
                      Colors.black26,
                      Colors.black26,
                      Colors.black.withAlpha(140),
                      Colors.black.withAlpha(180),
                    ],
                  ),
                ),
              ),
            ),

            // Live indicator
            if (venue.isLive == true)
              const Positioned(
                top: 16,
                right: 16,
                child: LiveWidget(),
              ),

            // Perk badge
            // if (venue.perksType.isNotEmpty)
            //   Positioned(
            //     top: 16,
            //     left: 16,
            //     child: buildPerkTag(
            //       venue.perksType,
            //     ),
            //   ),

            // Content
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.only(
                  left: 16,
                  top: 16,
                  right: 16,
                  bottom: 16,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: venue.name,
                            style: const TextStyle(
                              fontFamily: 'PulpDisplay',
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (venue.exclusive)
                            WidgetSpan(
                              alignment: PlaceholderAlignment
                                  .bottom, // Align with the middle of text
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  left: 6,
                                ), // Small space between text and badge
                                child: Image.asset(
                                  'assets/venue/badge.png',
                                  height: 22,
                                  width: 22,
                                  cacheHeight: 40,
                                  cacheWidth: 40,
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                        ],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 6),

                    Row(
                      children: [
                        const Icon(
                          CupertinoIcons.location_fill,
                          color: Colors.white,
                          size: 15,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          venue.area,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    ),
  );
}
