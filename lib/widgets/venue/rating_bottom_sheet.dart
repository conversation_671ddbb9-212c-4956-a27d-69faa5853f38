import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/logic/venue/controller/venue_controller.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/content/add_button.dart';

void showRatingBottomSheet(BuildContext context, String venueID) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    isScrollControlled: true,
    builder: (ctx) {
      return RatingBottomSheet(
        venueID: venueID,
      );
    },
  );
}

class RatingBottomSheet extends StatefulWidget {
  final String venueID;
  const RatingBottomSheet({required this.venueID, super.key});

  @override
  State<RatingBottomSheet> createState() => _RatingBottomSheetState();
}

class _RatingBottomSheetState extends State<RatingBottomSheet> {
  int selectedRating = 0;
  final TextEditingController _feedbackController = TextEditingController();
  late final VenueController _venueController;
  late final VenueRepository _venueRepository;
  late String userID;
  bool _isLoading = true;
  bool _isAlreadyRated = false;

  @override
  void initState() {
    super.initState();
    userID = context.read<UserBloc>().state.user!.uid;
    _venueController = VenueController(userID);
    _venueRepository = VenueRepository();
    _checkRatingStatus();
  }

  Future<void> _checkRatingStatus() async {
    try {
      final isRated = await _venueController.checkIfVenueRated(widget.venueID);
      setState(() {
        _isAlreadyRated = isRated;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleRating(String venueID) async {
    try {
      final isRated = await _venueController.checkIfVenueRated(venueID);

      if (isRated) {
        _showThankYouDialog();
        return;
      }

      if (selectedRating == 0) {
        // Show error that rating is required
        return;
      }

      final success = await _venueRepository.rateVenue(
        venueID: venueID,
        userID: userID, // Replace with actual user ID
        rating: selectedRating,
        feedback: _feedbackController.text.trim(),
      );

      if (success) {
        await _venueController.onRateVenue(venueID);
        if (mounted) {
          RouteUtils.pop(context);
          _showThankYouDialog();
        }
      }
    } catch (e) {
      // Handle error
    }
  }

  void _showThankYouDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: CupertinoAlertDialog(
          title: const Text('Thank You!'),
          content: const Text('We appreciate your feedback.'),
          actions: [
            CupertinoDialogAction(
              child: const Text('OK'),
              onPressed: () => RouteUtils.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_isAlreadyRated) {
      return ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
            decoration: BoxDecoration(
              color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(24)),
              border: Border.all(color: Colors.white.withAlpha(26)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.only(bottom: 20),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(76),
                      borderRadius: const BorderRadius.all(Radius.circular(2)),
                    ),
                  ),
                ),
                Text(
                  'Thank you for your feedback!',
                  style: TextStyle(
                    color: Colors.white.withAlpha(229),
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                addContentButton(
                  title: 'Close',
                  onTap: () => RouteUtils.pop(context),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      );
    }

    return StatefulBuilder(
      builder: (context, setState) {
        return Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 20,
                ),
                decoration: BoxDecoration(
                  color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(24)),
                  border: Border.all(
                    color: Colors.white.withAlpha(26),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(51),
                      blurRadius: 10,
                      spreadRadius: -5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Center(
                      child: Container(
                        width: 40,
                        height: 4,
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(76),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(2)),
                        ),
                      ),
                    ),
                    Text(
                      'Rate Your Experience',
                      style: TextStyle(
                        color: Colors.white.withAlpha(229),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: List.generate(5, (index) {
                        return GestureDetector(
                          onTap: () {
                            HapticFeedback.lightImpact();
                            setState(() {
                              selectedRating = index + 1;
                            });
                          },
                          child: Icon(
                            index < selectedRating
                                ? CupertinoIcons.star_fill
                                : CupertinoIcons.star,
                            color: Colors.amber,
                            size: 40,
                          ),
                        );
                      }),
                    ),
                    const SizedBox(height: 15),
                    TextField(
                      controller: _feedbackController,
                      maxLines: 3,
                      cursorColor: Colors.white,
                      decoration: const InputDecoration(
                        fillColor: Colors.transparent,
                        hintText:
                            'Write something about the venue... (Optional)',
                        enabledBorder:
                            UnderlineInputBorder(borderSide: BorderSide.none),
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(color: Colors.white),
                        ),
                      ),
                    ),
                    const SizedBox(height: 30),
                    addContentButton(
                      title: 'Submit',
                      onTap: () => _handleRating(widget.venueID),
                    ),
                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
