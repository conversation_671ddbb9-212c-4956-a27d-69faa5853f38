// import 'package:flutter/material.dart';
// import 'package:vibeo/helper/perks_text_string.dart';
// import 'package:vibeo/models/models.dart';
// import 'package:vibeo/themes/constant_theme.dart';
// import 'package:vibeo/widgets/feed/bounce_button_animation.dart';
// import 'package:vibeo/widgets/haptic_feedback.dart';

// class BounceButton extends StatelessWidget {
//   final VoidCallback openBottomSheet;
//   final bool fromVenuePage;
//   final PerksType perksType;
//   const BounceButton({
//     required this.openBottomSheet,
//     required this.fromVenuePage,
//     required this.perksType,
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return RepaintBoundary(
//       child: HapticButton(
//         onTap: openBottomSheet,
//         child: Container(
//           width: double.infinity,
//           margin: const EdgeInsets.only(top: 28),
//           child: Column(
//             children: [
//               AnimatedGradientBorderContainer(
//                 borderRadius: const BorderRadius.all(Radius.circular(100)),
//                 animate: perksType != PerksType.none && !fromVenuePage,
//                 child: Container(
//                   height: 50,
//                   decoration: BoxDecoration(
//                     color: darkAppColors.deepPurple.withAlpha(200),
//                     borderRadius: BorderRadius.circular(100),
//                   ),
//                   child: Center(
//                     child: Text(
//                       fromVenuePage ? 'Explore More' : perksText(perksType),
//                       style: const TextStyle(
//                         color: Colors.white,
//                         fontSize: 16,
//                         fontWeight: FontWeight.w600,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
