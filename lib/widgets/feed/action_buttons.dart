import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:like_button/like_button.dart';

import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/feed/animated_feed_bar.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';

Widget buildLikeButton({
  required String label,
  required bool liked,
  required void Function({required bool isLiked}) onTap,
}) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    child: Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: LikeButton(
        size: 40,
        isLiked: liked,
        onTap: (isLiked) async {
          await HapticFeedback.mediumImpact();
          onTap(isLiked: !isLiked);
          return !isLiked;
        },
        likeBuilder: (isLiked) {
          return Icon(
            isLiked ? CupertinoIcons.heart_fill : CupertinoIcons.heart,
            color: isLiked ? Colors.pink : Colors.white,
            size: 35,
          );
        },
        animationDuration: const Duration(milliseconds: 800),
      ),
    ),
  );
}

Widget buildShareButton({
  required IconData icon,
  required String label,
  required VoidCallback onTap,
}) {
  return HapticButton(
    onTap: onTap,
    child: Padding(
      padding: const EdgeInsets.only(top: 12),
      child: Icon(
        icon,
        size: 35,
      ),
    ),
  );
}

Widget buildProgressBars(
  int feedCount,
  AnimationController animationController,
  int currentFeedIndex,
) {
  return Padding(
    padding: SizeUtils.horizontalPadding,
    child: Container(
      margin: const EdgeInsets.only(top: 45),
      child: Row(
        children: List.generate(
          feedCount,
          (i) => Expanded(
            child: AnimatedBar(
              animController: animationController,
              position: i,
              currentIndex: currentFeedIndex,
            ),
          ),
        ),
      ),
    ),
  );
}

// Widget buildHookButton({
//   required String label,
//   required VoidCallback onTap,
//   bool isSpecial = false,
// }) {
//   return HapticButton(
//     onTap: onTap,
//     child: Padding(
//       padding: const EdgeInsets.symmetric(vertical: 5),
//       child: Column(
//         children: [
//           AnimatedGradientBorderButton(
//             label: label,
//             isSpecial: true,
//             child: Container(
//               width: 55,
//               height: 55,
//               decoration: BoxDecoration(
//                 shape: BoxShape.circle,
//                 color: isSpecial
//                     ? darkAppColors.purpleShade.withAlpha(128)
//                     : Colors.white.withAlpha(51),
//               ),
//               child: ClipRRect(
//                 borderRadius: const BorderRadius.all(Radius.circular(24)),
//                 child: BackdropFilter(
//                   filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
//                   child: const Icon(Icons.arrow_drop_up_rounded, size: 50),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     ),
//   );
// }
