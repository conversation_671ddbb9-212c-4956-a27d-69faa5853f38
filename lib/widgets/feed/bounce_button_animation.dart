import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';

class AnimatedGradientBorderContainer extends StatefulWidget {
  final BorderRadius borderRadius;
  final Widget child;
  final bool animate;
  final bool enabled;

  const AnimatedGradientBorderContainer({
    required this.child,
    required this.borderRadius,
    this.animate = true,
    this.enabled = true,
    super.key,
  });

  @override
  State<AnimatedGradientBorderContainer> createState() =>
      _AnimatedGradientBorderContainerState();
}

class _AnimatedGradientBorderContainerState
    extends State<AnimatedGradientBorderContainer>
    with SingleTickerProviderStateMixin {
  AnimationController? _animationController;
  late Animation<double>? _progressAnimation;

  @override
  void initState() {
    super.initState();
    if (!widget.enabled) return;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    );

    _progressAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_animationController!);

    if (widget.animate) {
      _animationController?.repeat();
    }
  }

  @override
  void didUpdateWidget(AnimatedGradientBorderContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (!widget.enabled) return;

    if (widget.animate != oldWidget.animate) {
      if (widget.animate) {
        _animationController?.repeat();
      } else {
        _animationController?.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) return widget.child;
    if (!widget.animate) {
      return Container(
        width: double.infinity,
        decoration: BoxDecoration(
          boxShadow: AppShadowStyles.baseStyle,
          borderRadius: widget.borderRadius,
        ),
        padding: const EdgeInsets.all(2),
        child: widget.child,
      );
    }

    return RepaintBoundary(
      child: AnimatedBuilder(
        animation: _animationController!,
        builder: (context, child) {
          return CustomPaint(
            painter: GradientBorderPainter(
              progress: _progressAnimation!.value,
              primaryColor: darkAppColors.deepPurple,
              secondaryColor: Colors.white30,
              borderRadius: widget.borderRadius,
            ),
            child: child,
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(2),
          child: widget.child,
        ),
      ),
    );
  }
}

class GradientBorderPainter extends CustomPainter {
  final double progress;
  final Color primaryColor;
  final Color secondaryColor;
  final BorderRadius borderRadius; // Add this property

  GradientBorderPainter({
    required this.progress,
    required this.primaryColor,
    required this.secondaryColor,
    required this.borderRadius, // Add this parameter
  });

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final paint = Paint()
      ..shader = SweepGradient(
        colors: [
          Colors.transparent,
          primaryColor.withAlpha((255 * 0.5).toInt()),
          secondaryColor.withAlpha((255 * 0.7).toInt()),
          primaryColor.withAlpha((255 * 0.5).toInt()),
          Colors.transparent,
        ],
        stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
        transform: GradientRotation(progress * 2 * 3.14159),
      ).createShader(rect);

    // Use the passed borderRadius instead of hardcoded value
    final path = Path()
      ..addRRect(
        RRect.fromRectAndCorners(
          rect,
          topLeft: borderRadius.topLeft,
          topRight: borderRadius.topRight,
          bottomLeft: borderRadius.bottomLeft,
          bottomRight: borderRadius.bottomRight,
        ),
      );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(GradientBorderPainter oldDelegate) {
    return oldDelegate.progress != progress ||
        oldDelegate.primaryColor != primaryColor ||
        oldDelegate.secondaryColor != secondaryColor ||
        oldDelegate.borderRadius != borderRadius; // Add this check
  }
}
