import 'package:flutter/material.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/widgets/feed/perk_widget.dart';

class PerkTimerExample extends StatelessWidget {
  const PerkTimerExample({super.key});

  @override
  Widget build(BuildContext context) {
    // Example expiry date 24 hours from now
    final expiryDate = DateTime.now().add(const Duration(hours: 24));
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('Perk Timer Example'),
        backgroundColor: Colors.black,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Example with timer
            const Text(
              'Perk with Timer:',
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 16),
            PerkWidget(
              perksType: PerksType.offer,
              expiryDate: expiryDate,
            ),
            const SizedBox(height: 32),
            
            // Example without timer
            const Text(
              'Perk without Timer:',
              style: TextStyle(color: Colors.white),
            ),
            const SizedBox(height: 16),
            const PerkWidget(
              perksType: PerksType.exclusive,
            ),
          ],
        ),
      ),
    );
  }
}
