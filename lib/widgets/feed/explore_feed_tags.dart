import 'package:flutter/material.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/video_tiles/vibe_tags.dart';

class ExploreFeedTags extends StatelessWidget {
  final List<Map<String, dynamic>> tags;
  final bool isLoading;
  final int selectedIndex;
  final Function onTap;

  const ExploreFeedTags({
    required this.tags,
    required this.isLoading,
    required this.selectedIndex,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      margin: const EdgeInsets.only(bottom: 24),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: tags.length,
        padding: SizeUtils.horizontalPadding,
        clipBehavior: Clip.none,
        itemBuilder: (ctx, index) => isLoading
            ? buildShimmerTag()
            : buildVibeTag(
                isSelected: selectedIndex == index,
                onTap: () => onTap(index),
                title: tags[index]['area'] as String,
                isLive: tags[index]['live'] as bool,
              ),
      ),
    );
  }
}
