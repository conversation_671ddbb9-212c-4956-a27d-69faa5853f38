// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:vibeo/themes/constant_theme.dart';

// class AnimatedGradientBorderButton extends StatefulWidget {
//   final Widget child;
//   final String label;
//   final bool isSpecial;

//   const AnimatedGradientBorderButton({
//     required this.child,
//     required this.label,
//     this.isSpecial = false,
//     super.key,
//   });

//   @override
//   State<AnimatedGradientBorderButton> createState() =>
//       _AnimatedGradientBorderButtonState();
// }

// class _AnimatedGradientBorderButtonState
//     extends State<AnimatedGradientBorderButton> {
//   late Timer _timer;
//   double _progress = 0;

//   @override
//   void initState() {
//     super.initState();
//     _startAnimation();
//   }

//   void _startAnimation() {
//     _timer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
//       setState(() {
//         _progress = (_progress + 0.02) % 1;
//       });
//     });
//   }

//   @override
//   void dispose() {
//     _timer.cancel();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       children: [
//         Container(
//           decoration: BoxDecoration(
//             shape: BoxShape.circle,
//             gradient: SweepGradient(
//               colors: [
//                 Colors.transparent,
//                 darkAppColors.deepPurple.withAlpha(76),
//                 Colors.white.withAlpha(128),
//                 darkAppColors.deepPurple.withAlpha(76),
//                 Colors.transparent,
//               ],
//               stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
//               transform: GradientRotation(_progress * 2 * 3.14159),
//             ),
//           ),
//           padding: const EdgeInsets.all(2),
//           child: widget.child,
//         ),
//         const SizedBox(height: 4),
//         Text(
//           widget.label,
//           style: const TextStyle(
//             color: Colors.white,
//             fontSize: 12,
//             fontWeight: FontWeight.w800,
//           ),
//         ),
//       ],
//     );
//   }
// }
