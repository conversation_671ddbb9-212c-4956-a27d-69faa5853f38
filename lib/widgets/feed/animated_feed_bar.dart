import 'package:flutter/material.dart';

class AnimatedBar extends StatelessWidget {
  final AnimationController animController;
  final int position;
  final int currentIndex;

  const AnimatedBar({
    required this.animController,
    required this.position,
    required this.currentIndex,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(
        top: 10,
        bottom: 20,
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Stack(
            children: <Widget>[
              _buildContainer(
                double.infinity,
                position < currentIndex ? Colors.white : Colors.white24,
              ),
              if (position == currentIndex)
                AnimatedBuilder(
                  animation: animController,
                  builder: (context, child) {
                    return _buildContainer(
                      constraints.maxWidth * animController.value,
                      Colors.white,
                    );
                  },
                )
              else
                const SizedBox.shrink(),
            ],
          );
        },
      ),
    );
  }
}

Container _buildContainer(double width, Color color) {
  return Container(
    height: 2,
    width: width,
    margin: const EdgeInsets.only(right: 2),
    decoration: BoxDecoration(
      color: color,
      borderRadius: const BorderRadius.all(
        Radius.circular(100),
      ),
    ),
  );
}
