import 'package:flutter/material.dart';

class EnergyWidget extends StatelessWidget {
  final int vibeScore;
  const EnergyWidget({required this.vibeScore, super.key});

  @override
  Widget build(BuildContext context) {
    // Pre-calculate the common color for inactive bars
    final inactiveColor = Colors.grey.withAlpha((255 * 0.3).toInt());
    const activeColor = Color.fromARGB(255, 148, 116, 255);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        _buildEnergyBar(0, 5, vibeScore > 0 ? activeColor : inactiveColor),
        _buildEnergyBar(1, 7, vibeScore > 1 ? activeColor : inactiveColor),
        _buildEnergyBar(2, 9, vibeScore > 2 ? activeColor : inactiveColor),
        _buildEnergyBar(3, 11, vibeScore > 3 ? activeColor : inactiveColor),
        _buildEnergyBar(4, 13, vibeScore > 4 ? activeColor : inactiveColor),
      ],
    );
  }

  Widget _buildEnergyBar(int index, double height, Color color) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 1),
      width: 3,
      height: height,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(6)),
        color: color,
      ),
    );
  }
}
