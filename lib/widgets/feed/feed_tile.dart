import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:vibeo/widgets/feed/live_widget.dart';
import 'package:vibeo/widgets/feed/perk_widget.dart';

class FeedTile extends StatelessWidget {
  final String? description;
  const FeedTile({
    required this.feed,
    this.description,
    super.key,
  });

  final FeedModel feed;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          child: CachedNetworkImage(
            imageUrl: feed.thumbnailURL,
            imageBuilder: (context, imageProvider) {
              return Container(
                height: 400,
                width: 300,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: const BorderRadius.all(Radius.circular(16)),
                  boxShadow: AppShadowStyles.baseStyle,
                  image: DecorationImage(
                    image: imageProvider,
                    fit: BoxFit.cover,
                  ),
                ),
              );
            },
            maxHeightDiskCache: 400,
            maxWidthDiskCache: 300,
            placeholder: (context, url) => Shimmer.fromColors(
              baseColor: darkAppColors.lightColor.withAlpha(50),
              highlightColor:
                  darkAppColors.secondaryBackgroundColor.withAlpha(100),
              child: Container(
                width: 300,
                height: 400,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                ),
              ),
            ),
          ),
        ),
        if (feed.isLive ?? false)
          const Positioned(
            top: 16,
            right: 10,
            child: LiveWidget(),
          ),
        if (feed.perksType != null && feed.perksType != PerksType.none)
          Positioned(
            top: 16,
            left: 10,
            child: PerkWidget(
              perksType: feed.perksType!,
              // Check if there are offers with expiry dates
              expiryDate: feed.offers.isNotEmpty
                  ? DateTime.tryParse(feed.offers.first.endDate)
                  : null,
            ),
          ),
        ...[
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(16)),
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withAlpha(200),
                    Colors.black,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 0.5, 0.8, 1],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 5),
                      SizedBox(
                        width: 120,
                        child: Text(
                          feed.venueName,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          style: AppTextStyles.bodyBold.copyWith(
                            color: Colors.white,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (description != null) ...[
                    const SizedBox(
                      height: 5,
                    ),
                    SizedBox(
                      width: 180,
                      child: Text(
                        description!,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 3,
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
        Positioned.fill(
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(
                  (255 * 0.5).toInt(),
                ), // Semi-transparent background
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CupertinoIcons.play,
                color: Colors.white70,
                size: 24,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
