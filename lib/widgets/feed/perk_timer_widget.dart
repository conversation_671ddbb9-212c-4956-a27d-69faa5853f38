import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

class PerkTimerWidget extends StatefulWidget {
  final DateTime expiryDate;

  const PerkTimerWidget({
    required this.expiryDate,
    super.key,
  });

  @override
  State<PerkTimerWidget> createState() => _PerkTimerWidgetState();
}

class _PerkTimerWidgetState extends State<PerkTimerWidget> {
  late Timer _timer;
  late DateTime _expiryDate;
  Duration _difference = Duration.zero;

  @override
  void initState() {
    super.initState();
    _expiryDate = widget.expiryDate;
    _updateDifference();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateDifference();
    });
  }

  void _updateDifference() {
    setState(() {
      _difference = _expiryDate.difference(DateTime.now());
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_difference.isNegative) {
      return const SizedBox();
    }

    final days = _difference.inDays;
    final hours = _difference.inHours.remainder(24);
    final minutes = _difference.inMinutes.remainder(60);
    final seconds = _difference.inSeconds.remainder(60);

    String timerText;
    if (days > 0) {
      timerText = '${days}d ${hours}h';
    } else if (hours > 0) {
      timerText = '${hours}h ${minutes}m';
    } else {
      timerText = '${minutes}m ${seconds}s';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: darkAppColors.secondaryBackgroundColor.withAlpha(
            (255 * 0.4).toInt(),
          ),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            CupertinoIcons.stopwatch_fill,
            color: Colors.pink[300],
            size: 12,
          ),
          const SizedBox(width: 4),
          Text(
            timerText,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
