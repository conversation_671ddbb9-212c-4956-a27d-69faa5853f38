import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

import 'package:vibeo/widgets/feed/live_widget.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

Widget buildFeedItem(
  BuildContext context,
  int index,
  List<FeedModel> feeds, {
  bool isVenuePage = false,
}) {
  final List<double> heights = [240, 270, 290, 310, 340];
  final height = heights[index % heights.length];

  return HapticButton(
    onTap: () {
      RouteUtils.pushNamed(
        context,
        RoutePaths.feedPlayingPage,
        arguments: {
          'feeds': feeds,
          'initialIndex': index,
          'isVenuePage': isVenuePage,
          'title': 'Community Picks',
        },
      );
    },
    child: Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          child: CachedNetworkImage(
            imageUrl: feeds[index].thumbnailURL,
            maxHeightDiskCache: height.toInt(),
            maxWidthDiskCache: 200,
            imageBuilder: (context, imageProvider) {
              return Container(
                height: height,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: const BorderRadius.all(Radius.circular(12)),
                  boxShadow: AppShadowStyles.baseStyle,
                  image: DecorationImage(
                    image: imageProvider,
                    fit: BoxFit.cover,
                  ),
                ),
              );
            },
            // memCacheWidth: 576, // Fixed cache width
            // memCacheHeight: height.toInt(), // Variable height
            // maxWidthDiskCache: 576, // Fixed disk cache width
            // maxHeightDiskCache: height.toInt(), // Variable height
            placeholder: (context, url) => Shimmer.fromColors(
              baseColor: darkAppColors.lightColor.withAlpha(50),
              highlightColor:
                  darkAppColors.secondaryBackgroundColor.withAlpha(100),
              child: Container(
                height: height,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
              ),
            ),
          ),
        ),
        if (feeds[index].isLive ?? false)
          const Positioned(
            top: 10,
            right: 10,
            child: LiveWidget(),
          ),
        if (!isVenuePage) ...[
          Positioned.fill(
            child: DecoratedBox(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(12)),
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withAlpha(102),
                    Colors.black,
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  stops: const [0, 0.5, 0.8, 1],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 5),
                  SizedBox(
                    width: 120,
                    child: Text(
                      feeds[index].venueName,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: AppTextStyles.bodyBold.copyWith(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
        Positioned.fill(
          child: Center(
            child: Container(
              padding: const EdgeInsets.all(5),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(
                  (255 * 0.5).toInt(),
                ), // Semi-transparent background
                shape: BoxShape.circle,
              ),
              child: const Icon(
                CupertinoIcons.play,
                color: Colors.white70,
                size: 24,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}
