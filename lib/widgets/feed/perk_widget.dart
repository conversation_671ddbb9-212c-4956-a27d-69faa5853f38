import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/widgets/feed/bounce_button_animation.dart';
import 'package:vibeo/widgets/feed/perk_timer_widget.dart';

class PerkWidget extends StatelessWidget {
  final PerksType perksType;
  final DateTime? expiryDate;
  final String? title;
  const PerkWidget({
    required this.perksType,
    this.title,
    this.expiryDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // Get configuration based on perk type
    final config = _getPerkConfig(
      perksType,
      title: title,
    );
    if (config == null) {
      return const SizedBox();
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AnimatedGradientBorderContainer(
          enabled: config.label == 'FREE',
          borderRadius: const BorderRadius.all(Radius.circular(10)),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 4,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: config.gradientColors,
              ),
              borderRadius: const BorderRadius.all(
                Radius.circular(8),
              ), // Slightly larger radius
              border: Border.all(
                color: config.borderColor,
                width: 1.5, // Thicker border
              ),
              boxShadow: [
                BoxShadow(
                  color: config.borderColor.withAlpha(
                    (255 * 0.3).toInt(),
                  ), // Colored shadow based on perk type
                  blurRadius: 20,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              textBaseline: TextBaseline.alphabetic,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (config.iconData != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 4),
                    child: Icon(
                      config.iconData,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                Text(
                  config.label,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 1,
                  ),
                ),
              ],
            ),
          ),
        ),
        if (expiryDate != null) ...[
          PerkTimerWidget(expiryDate: expiryDate!),
        ],
      ],
    );
  }

  _PerkConfig? _getPerkConfig(PerksType type, {String? title}) {
    switch (type) {
      case PerksType.free:
        const baseColor = Colors.green;
        return _PerkConfig(
          label: 'FREE',
          gradientColors: [
            const Color.fromARGB(255, 98, 210, 102)
                .withAlpha((255 * 0.85).toInt()), // More opaque
            const Color.fromARGB(255, 78, 201, 83)
                .withAlpha((255 * 0.6).toInt()), // More opaque
          ],
          borderColor:
              baseColor.withAlpha((255 * 0.8).toInt()), // More visible border
          iconData: CupertinoIcons.hourglass,
        );
      case PerksType.exclusive:
        const baseColor = Colors.deepPurple;

        return _PerkConfig(
          label: 'EXCLUSIVE',
          gradientColors: [
            baseColor.withAlpha(255 * 1), // More vibrant
            baseColor.withAlpha((255 * 0.8).toInt()), // More vibrant
          ],
          borderColor:
              baseColor.withAlpha((255 * 0.8).toInt()), // More visible border
        );
      case PerksType.offer:
        const baseColor = Colors.purpleAccent;

        return _PerkConfig(
          label: title ?? 'OFFERS',
          gradientColors: [
            baseColor.withAlpha((255 * 0.8).toInt()), // More vibrant
            baseColor.withAlpha((255 * 0.6).toInt()), // More vibrant
          ],
          borderColor:
              baseColor.withAlpha((255 * 0.8).toInt()), // More visible border
        );
      case PerksType.promoter:
        const baseColor = Colors.pinkAccent;

        return _PerkConfig(
          label: 'GET ENTRY',
          gradientColors: [
            baseColor.withAlpha((255 * 0.8).toInt()), // More vibrant
            baseColor.withAlpha((255 * 0.6).toInt()), // More vibrant
          ],
          borderColor:
              baseColor.withAlpha((255 * 0.8).toInt()), // More visible border
        );
      case PerksType.both:
        const baseColor = Colors.teal;
        return _PerkConfig(
          label: 'PERKS',
          gradientColors: [
            baseColor.withAlpha((255 * 0.8).toInt()), // More vibrant
            baseColor.withAlpha((255 * 0.6).toInt()), // More vibrant
          ],
          borderColor:
              baseColor.withAlpha((255 * 0.8).toInt()), // More visible border
        );
      default:
        return null;
    }
  }
}

// Helper class to organize configuration
class _PerkConfig {
  final String label;
  final List<Color> gradientColors;
  final Color borderColor;
  final IconData? iconData;

  const _PerkConfig({
    required this.label,
    required this.gradientColors,
    required this.borderColor,
    this.iconData,
  });
}
