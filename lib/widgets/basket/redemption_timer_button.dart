import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:vibeo/logic/redemption/provider/redemption_provider.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/screens/basket/redemption_page.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:video_player/video_player.dart';

class RedemptionTimerButton extends StatefulWidget {
  final VoidCallback onTap;
  final OfferModel offer;
  const RedemptionTimerButton({
    required this.onTap,
    required this.offer,
    super.key,
  });

  @override
  State<RedemptionTimerButton> createState() => _RedemptionTimerButtonState();
}

class _RedemptionTimerButtonState extends State<RedemptionTimerButton>
    with TickerProviderStateMixin {
  late VideoPlayerController _videoPlayerController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  late Timer _timer;
  int _secondsLeft = 5;
  bool _isButtonEnabled = false;

  @override
  void initState() {
    super.initState();

    _videoPlayerController = VideoPlayerController.asset(
      'assets/venue/redeem.mp4',
      videoPlayerOptions: VideoPlayerOptions(
        mixWithOthers: false,
        allowBackgroundPlayback: false,
      ),
    )..initialize().then((_) {
        _videoPlayerController
          ..play()
          ..setLooping(true);
      });

    // Timer animation controller
    _animationController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..addListener(() {
        setState(() {});
      });

    _animation = Tween<double>(begin: 0, end: 1).animate(_animationController);
    _animationController.forward();

    // Timer for countdown
    _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      if (_secondsLeft > 0) {
        setState(() {
          _secondsLeft--;
          _isButtonEnabled = false;
        });
      } else {
        timer.cancel();
        setState(() {
          _isButtonEnabled = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();

    _videoPlayerController.dispose();
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with neumorphic design
        Padding(
          padding: const EdgeInsets.only(
            bottom: 24,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topRight,
                child: TextButton.icon(
                  icon: const Icon(
                    CupertinoIcons.info,
                    color: Colors.grey,
                    size: 16,
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey,
                    textStyle: AppTextStyles.body.copyWith(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) {
                          return RedemptionProvider(
                            child: RedemptionPage(
                              offer: widget.offer,
                              venueID: widget.offer.venueID,
                            ),
                          );
                        },
                      ),
                    );
                  },
                  label: const Text('Try Different Way'),
                ),
              ),
              Row(
                children: [
                  Icon(
                    CupertinoIcons.gift_fill,
                    color: darkAppColors.redShade,
                    size: 24,
                  ),
                  const SizedBox(width: 10),
                  Text(
                    'Let’s get you your perk!',
                    style: AppTextStyles.title,
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'This offer will be ready to redeem in 5 seconds. Hand your phone to the bartender once the button activates.',
                style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
              ),
            ],
          ),
        ),

        // Redemption Steps
        // AnimatedBuilder(
        //   animation: _stepsAnimation,
        //   builder: (context, child) {
        //     return Opacity(
        //       opacity: _stepsAnimation.value,
        //       child: Transform.translate(
        //         offset: Offset(0, 20 * (1 - _stepsAnimation.value)),
        //         child: Container(
        //           padding: const EdgeInsets.all(20),
        //           margin: const EdgeInsets.only(bottom: 24),
        //           decoration: BoxDecoration(
        //             color: darkAppColors.secondaryBackgroundColor.withAlpha(76),
        //             borderRadius: BorderRadius.circular(16),
        //             border: Border.all(
        //               color: darkAppColors.primary.withAlpha(51),
        //             ),
        //           ),
        //           child: Column(
        //             crossAxisAlignment: CrossAxisAlignment.start,
        //             children: [
        //               Text(
        //                 'How to redeem:',
        //                 style: AppTextStyles.bodyBold.copyWith(
        //                   color: Colors.white,
        //                 ),
        //               ),
        //               const SizedBox(height: 16),
        //               // Step 1: Show to staff
        //               _buildRedemptionStep(
        //                 icon: CupertinoIcons.person_2_fill,
        //                 iconColor: darkAppColors.purpleShade,
        //                 stepNumber: 2,
        //                 title: 'Show to the staff',
        //                 description: 'Present this screen to the bartender',
        //               ),

        //               // Connector line
        //               Padding(
        //                 padding: const EdgeInsets.only(left: 15),
        //                 child: Container(
        //                   width: 2,
        //                   height: 20,
        //                   color: darkAppColors.primary.withAlpha(76),
        //                 ),
        //               ),

        //               // Step 3: Successful redemption
        //               _buildRedemptionStep(
        //                 icon: CupertinoIcons.checkmark_circle_fill,
        //                 iconColor: Colors.green,
        //                 stepNumber: 3,
        //                 title: 'Successfully redeemed!',
        //                 description: 'Enjoy your offer at ${widget.venueName}',
        //               ),
        //             ],
        //           ),
        //         ),
        //       ),
        //     );
        //   },
        // ),

        SizedBox(
          height: 350,
          width: double.infinity,
          child: VideoPlayer(
            _videoPlayerController,
          ),
        ),

        // Venue information with neumorphic design

        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.only(
            top: 12,
            bottom: 12,
          ),
          child: const Text(
            'This offer must be redeemed in person at the venue.',
            style: TextStyle(
              color: Colors.red,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // Timer button with neumorphic design
        HapticButton(
          onTap: _isButtonEnabled ? widget.onTap : () {},
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 18),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: _isButtonEnabled
                        ? [
                            Colors.white,
                            Colors.white,
                          ]
                        : [
                            Colors.grey[800]!,
                            Colors.grey[900]!,
                          ],
                    stops: _isButtonEnabled
                        ? const [0.0, 1.0]
                        : [_animation.value, _animation.value],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow:
                      _isButtonEnabled ? AppShadowStyles.baseStyle : null,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (_isButtonEnabled) const SizedBox(width: 10),
                    if (!_isButtonEnabled) const SizedBox(width: 10),
                    Text(
                      _isButtonEnabled
                          ? 'Tap to Redeem Now'
                          : 'Ready in ${_formatTime(_secondsLeft)}s',
                      style: AppTextStyles.body.copyWith(
                        color: _isButtonEnabled
                            ? darkAppColors.secondaryBackgroundColor
                            : Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 12),
        Center(
          child: TextButton(
            onPressed: () {
              RouteUtils.pop(context);
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey,
              textStyle: AppTextStyles.body.copyWith(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            child: const Text('Cancel'),
          ),
        ),
      ],
    );
  }

  String _formatTime(int seconds) {
    return seconds.toString();
  }
}
