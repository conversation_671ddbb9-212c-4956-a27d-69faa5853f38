import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/screens/basket/categorized_offers_page.dart';
import 'package:vibeo/widgets/global/buildPerkTag.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class BasketOfferTile extends StatefulWidget {
  final VenueModel venue;
  final Future<int> offersAvailable;
  final VoidCallback? onViewVenue;
  final VoidCallback? onViewBasket;
  final DateTime? expiryDate;

  const BasketOfferTile({
    required this.venue,
    required this.offersAvailable,
    required this.onViewVenue,
    required this.onViewBasket,
    this.expiryDate,
    super.key,
  });

  @override
  State<BasketOfferTile> createState() => _BasketOfferTileState();
}

class _BasketOfferTileState extends State<BasketOfferTile> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return HapticButton(
      onTap: () {
        // Navigate to venue details page
        widget.onViewBasket?.call();
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withAlpha(25),
            width: 0.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Venue header with name and timer
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          widget.venue.name,
                          style: AppTextStyles.title,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  // Offers count
                  Row(
                    children: [
                      Icon(
                        Icons.card_giftcard,
                        color: Colors.pink[300],
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      FutureBuilder<int>(
                        future: widget.offersAvailable,
                        builder: (context, snapshot) {
                          final count = snapshot.data ?? 0;
                          return Text(
                            '$count ${count <= 1 ? 'Offer' : 'Offers'} Available',
                            style: AppTextStyles.bodysmallBold.copyWith(
                              color: Colors.pink[300],
                              fontWeight: FontWeight.w500,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Divider
            Divider(
              color: Colors.white.withAlpha(25),
              height: 1,
            ),

            // Venue image and details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Venue image
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: widget.venue.imageLink,
                      width: 70,
                      height: 70,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) {
                        return Container(
                          width: 70,
                          height: 70,
                          color: Colors.grey.shade800,
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.white54,
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(width: 12),

                  // Venue details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Perk tag
                        buildPerkTag([PerkVenueType.offers]),
                        const SizedBox(height: 8),

                        Text(
                          widget.venue.description,
                          style: AppTextStyles.bodySmaller.copyWith(
                            color: Colors.white.withAlpha(179),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 16),

                        // Action buttons
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // View Venue Button
                            Expanded(
                              child: TextButton(
                                onPressed: widget.onViewVenue,
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  backgroundColor: Colors.black.withAlpha(102),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(
                                      color: Colors.white.withAlpha(26),
                                      width: 0.5,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      CupertinoIcons.building_2_fill,
                                      color: Colors.white,
                                      size: 14,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'View Venue',
                                      style: AppTextStyles.bodySmaller.copyWith(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            // View Basket Button
                            Expanded(
                              child: TextButton(
                                onPressed: _isLoading
                                    ? null
                                    : () async {
                                        // Set loading state
                                        setState(() {
                                          _isLoading = true;
                                        });
                                        final AnalyticsService analytics =
                                            AnalyticsService.instance;
                                        await analytics.trackViewBasket(
                                          widget.venue.id,
                                        );
                                        try {
                                          // Call the onViewBasket callback or navigate to CategorizedOffersPage
                                          if (widget.onViewBasket != null) {
                                            widget.onViewBasket!();
                                          } else {
                                            if (context.mounted) {
                                              await Navigator.of(context).push(
                                                MaterialPageRoute(
                                                  builder: (context) =>
                                                      CategorizedOffersPage(
                                                    venue: widget.venue,
                                                  ),
                                                ),
                                              );
                                            }
                                          }
                                        } finally {
                                          // Reset loading state after navigation
                                          if (mounted) {
                                            setState(() {
                                              _isLoading = false;
                                            });
                                          }
                                        }
                                      },
                                style: TextButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  backgroundColor: Colors.white10,
                                  shadowColor: Colors.white.withAlpha(26),
                                  shape: RoundedRectangleBorder(
                                    side: const BorderSide(
                                      color: Colors.white,
                                      width: 0.5,
                                    ),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: LoadingWidget(),
                                      )
                                    : Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            CupertinoIcons.bag_fill,
                                            color: Colors.white,
                                            size: 14,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            'View Basket',
                                            style: AppTextStyles.bodySmaller
                                                .copyWith(
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
