import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vibeo/themes/text_theme.dart';

class CountdownTimerVenueWidget extends StatefulWidget {
  final DateTime expiryDate;

  const CountdownTimerVenueWidget({required this.expiryDate, super.key});

  @override
  State<CountdownTimerVenueWidget> createState() =>
      _CountdownTimerVenueWidgetState();
}

class _CountdownTimerVenueWidgetState extends State<CountdownTimerVenueWidget> {
  late Timer _timer;
  late DateTime _expiryDate;
  Duration _difference = Duration.zero;

  @override
  void initState() {
    super.initState();
    _expiryDate = widget.expiryDate;
    _updateDifference();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) {
      _updateDifference();
    });
  }

  void _updateDifference() {
    setState(() {
      _difference = _expiryDate.difference(DateTime.now());
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_difference.isNegative) {
      return const SizedBox();
    }

    final days = _difference.inDays;
    final hours = _difference.inHours.remainder(24);
    final minutes = _difference.inMinutes.remainder(60);
    final seconds = _difference.inSeconds.remainder(60);

    // Create a more compact display based on the time remaining
    String timerText;
    if (days > 0) {
      timerText = '${days}d ${hours}h';
    } else if (hours > 0) {
      timerText = '${hours}h ${minutes}m';
    } else {
      timerText = '${minutes}m ${seconds}s';
    }

    return Text(
      timerText,
      style: AppTextStyles.body.copyWith(
        color: Colors.white,
        fontSize: 14,
      ),
    );
  }
}
