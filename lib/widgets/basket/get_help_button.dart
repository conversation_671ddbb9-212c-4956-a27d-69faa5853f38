import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/widgets/help/redemption_help_dialog.dart';

Widget buildGetHelpButton(BuildContext context) {
  return GestureDetector(
    onTap: () {
      RedemptionHelpDialog.show(context);
    },
    child: Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 12,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            CupertinoIcons.question_circle,
            color: Colors.white70,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            'Get Help',
            style: AppTextStyles.bodySmaller.copyWith(
              color: Colors.white70,
            ),
          ),
        ],
      ),
    ),
  );
}
