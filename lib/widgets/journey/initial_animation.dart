import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

class InitialAnimation extends StatefulWidget {
  final AnimationController controller;

  const InitialAnimation({
    required this.controller,
    super.key,
  });

  @override
  State<InitialAnimation> createState() => _InitialAnimationState();
}

class _InitialAnimationState extends State<InitialAnimation>
    with SingleTickerProviderStateMixin {
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  int _currentTextIndex = 0;
  final List<String> _texts = [
    "What's your mood today?",
    'Do you want me to plan your journey?',
    "Let's create your perfect night out!",
  ];

  @override
  void initState() {
    super.initState();

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: widget.controller,
      curve: const Interval(0, 0.2, curve: Curves.easeInOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1,
    ).animate(CurvedAnimation(
      parent: widget.controller,
      curve: const Interval(0, 0.4, curve: Curves.elasticOut),
    ));

    _startTextCycle();
  }

  Future<void> _startTextCycle() async {
    for (int i = 0; i < _texts.length; i++) {
      if (mounted) {
        setState(() {
          _currentTextIndex = i;
        });
        await Future.delayed(const Duration(milliseconds: 1200));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimatedBuilder(
        animation: widget.controller,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
                margin: const EdgeInsets.symmetric(horizontal: 24),
                decoration: BoxDecoration(
                  color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: Colors.white.withAlpha(25),
                    width: 0.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: darkAppColors.deepPurple.withAlpha(51),
                      blurRadius: 20,
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 10,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Animated gradient icon
                    Container(
                      width: 80,
                      height: 80,
                      margin: const EdgeInsets.only(bottom: 24),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            darkAppColors.primary,
                            darkAppColors.deepPurple,
                            darkAppColors.secondary,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: darkAppColors.deepPurple.withAlpha(76),
                            blurRadius: 15,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.explore,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),

                    // Animated text
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 600),
                      transitionBuilder:
                          (Widget child, Animation<double> animation) {
                        return FadeTransition(
                          opacity: animation,
                          child: SlideTransition(
                            position: Tween<Offset>(
                              begin: const Offset(0, 0.3),
                              end: Offset.zero,
                            ).animate(animation),
                            child: child,
                          ),
                        );
                      },
                      child: Text(
                        _texts[_currentTextIndex],
                        key: ValueKey<int>(_currentTextIndex),
                        style: AppTextStyles.heading2.copyWith(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Subtitle
                    Text(
                      'Swipe through questions to personalize your experience',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
