import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

class ResultsPage extends StatefulWidget {
  final Map<String, dynamic> responses;

  const ResultsPage({
    required this.responses,
    super.key,
  });

  @override
  State<ResultsPage> createState() => _ResultsPageState();
}

class _ResultsPageState extends State<ResultsPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _onExploreMore() {
    HapticFeedback.mediumImpact();
    // Navigate to explore page
  }

  void _onStartJourney() {
    HapticFeedback.mediumImpact();
    // Navigate to first suggestion
  }

  String _generatePersonalizedMessage() {
    final mood = widget.responses['mood'] ?? 'Happy';
    final venueType = widget.responses['venue_type'] ?? 'Club';
    final budget = widget.responses['budget'] ?? 'Moderate';

    return 'Based on your $mood mood and preference for ${venueType.toLowerCase()}s with a $budget budget, here are your perfect matches!';
  }

  List<Map<String, dynamic>> _generateSuggestions() {
    // This would typically come from an API call based on responses
    return [
      {
        'name': 'Skybar Rooftop',
        'type': 'Rooftop Bar',
        'rating': 4.8,
        'distance': '0.5 km',
        'image': 'https://via.placeholder.com/300x200',
        'highlights': ['Great views', 'Live DJ', 'Craft cocktails'],
      },
      {
        'name': 'Neon Nights Club',
        'type': 'Nightclub',
        'rating': 4.6,
        'distance': '1.2 km',
        'image': 'https://via.placeholder.com/300x200',
        'highlights': ['Electronic music', 'Dance floor', 'VIP area'],
      },
      {
        'name': 'The Lounge',
        'type': 'Cocktail Lounge',
        'rating': 4.7,
        'distance': '0.8 km',
        'image': 'https://via.placeholder.com/300x200',
        'highlights': ['Intimate setting', 'Premium drinks', 'Live jazz'],
      },
    ];
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final suggestions = _generateSuggestions();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Success icon
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      margin: const EdgeInsets.only(bottom: 24),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            darkAppColors.primary,
                            darkAppColors.deepPurple,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: darkAppColors.deepPurple.withAlpha(76),
                            blurRadius: 20,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ),

                  // Title
                  Text(
                    'Your Perfect Night Out!',
                    style: AppTextStyles.heading1.copyWith(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 16),

                  // Personalized message
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color:
                          darkAppColors.secondaryBackgroundColor.withAlpha(128),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withAlpha(25),
                        width: 0.5,
                      ),
                    ),
                    child: Text(
                      _generatePersonalizedMessage(),
                      style: AppTextStyles.body.copyWith(
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Suggestions title
                  Text(
                    'Recommended for You',
                    style: AppTextStyles.heading2.copyWith(
                      fontSize: 22,
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Suggestions list
                  ...suggestions.asMap().entries.map((entry) {
                    final index = entry.key;
                    final suggestion = entry.value;

                    return Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: _buildSuggestionCard(suggestion, index),
                    );
                  }),

                  const SizedBox(height: 32),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: _buildActionButton(
                          'Explore More',
                          Icons.explore,
                          _onExploreMore,
                          isPrimary: false,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildActionButton(
                          'Start Journey',
                          Icons.navigation,
                          _onStartJourney,
                          isPrimary: true,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSuggestionCard(Map<String, dynamic> suggestion, int index) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withAlpha(25),
          width: 0.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          // Venue image placeholder
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [
                  darkAppColors.primary.withAlpha(128),
                  darkAppColors.deepPurple.withAlpha(128),
                ],
              ),
            ),
            child: const Icon(
              Icons.location_on,
              color: Colors.white,
              size: 32,
            ),
          ),

          const SizedBox(width: 16),

          // Venue details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  suggestion['name'] as String,
                  style: AppTextStyles.subtitle.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  suggestion['type'] as String,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: darkAppColors.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      color: Colors.amber,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      suggestion['rating'].toString(),
                      style: AppTextStyles.bodySmaller.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(width: 16),
                    const Icon(
                      Icons.location_on,
                      color: Colors.white70,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      suggestion['distance'] as String,
                      style: AppTextStyles.bodySmaller.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String text,
    IconData icon,
    VoidCallback onTap, {
    bool isPrimary = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isPrimary ? darkAppColors.deepPurple : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isPrimary
                ? darkAppColors.deepPurple
                : Colors.white.withAlpha(51),
            width: 1,
          ),
          boxShadow: isPrimary
              ? [
                  BoxShadow(
                    color: darkAppColors.deepPurple.withAlpha(76),
                    blurRadius: 15,
                    spreadRadius: 0,
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
