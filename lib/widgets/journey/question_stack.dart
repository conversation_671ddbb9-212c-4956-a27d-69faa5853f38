import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/journey/question_card.dart';
import 'package:vibeo/widgets/journey/processing_animation.dart';
import 'package:vibeo/widgets/journey/results_page.dart';

class QuestionStack extends StatefulWidget {
  final void Function(Map<String, dynamic>) onComplete;

  const QuestionStack({
    required this.onComplete,
    super.key,
  });

  @override
  State<QuestionStack> createState() => _QuestionStackState();
}

class _QuestionStackState extends State<QuestionStack>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  int _currentQuestionIndex = 0;
  bool _isProcessing = false;
  bool _showResults = false;

  final Map<String, dynamic> _responses = {};

  final List<Map<String, dynamic>> _questions = [
    {
      'id': 'mood',
      'question': "How's your mood?",
      'type': 'emoji',
      'options': ['😊', '🔥', '😎', '🥳'],
      'labels': ['Happy', 'Energetic', 'Cool', 'Party'],
    },
    {
      'id': 'venue_type',
      'question': 'What type of venue do you prefer?',
      'type': 'text',
      'options': ['Club', 'Bar', 'Lounge', 'Rooftop'],
    },
    {
      'id': 'group_size',
      'question': 'How many people in your group?',
      'type': 'text',
      'options': ['Solo', '2-3 people', '4-6 people', '7+ people'],
    },
    {
      'id': 'budget',
      'question': "What's your budget range?",
      'type': 'text',
      'options': ['Budget-friendly', 'Moderate', 'Premium', 'Luxury'],
    },
    {
      'id': 'time_preference',
      'question': 'When do you want to go out?',
      'type': 'text',
      'options': [
        'Early evening',
        'Prime time',
        'Late night',
        'After midnight',
      ],
    },
  ];

  @override
  void initState() {
    super.initState();

    _pageController = PageController();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
  }

  void _handleAnswer(String questionId, dynamic answer) {
    HapticFeedback.mediumImpact();

    setState(() {
      _responses[questionId] = answer;
    });

    if (_currentQuestionIndex < _questions.length - 1) {
      _nextQuestion();
    } else {
      _finishQuestionnaire();
    }
  }

  Future<void> _nextQuestion() async {
    await _slideController.reverse();

    if (mounted) {
      setState(() {
        _currentQuestionIndex++;
      });

      await _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      await _slideController.forward();
    }
  }

  Future<void> _previousQuestion() async {
    if (_currentQuestionIndex > 0) {
      await _slideController.reverse();

      if (mounted) {
        setState(() {
          _currentQuestionIndex--;
        });

        await _pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );

        await _slideController.forward();
      }
    }
  }

  void _skipQuestion() {
    HapticFeedback.lightImpact();

    if (_currentQuestionIndex < _questions.length - 1) {
      _nextQuestion();
    } else {
      _finishQuestionnaire();
    }
  }

  Future<void> _finishQuestionnaire() async {
    setState(() {
      _isProcessing = true;
    });

    // Simulate processing time
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isProcessing = false;
        _showResults = true;
      });

      // Call the completion callback
      widget.onComplete(_responses);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isProcessing) {
      return const ProcessingAnimation();
    }

    if (_showResults) {
      return ResultsPage(responses: _responses);
    }

    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.primaryVelocity! > 0) {
          // Swiped right - go back
          _previousQuestion();
        } else if (details.primaryVelocity! < 0) {
          // Swiped left - skip
          _skipQuestion();
        }
      },
      child: Column(
        children: [
          // Progress indicator
          Container(
            margin: const EdgeInsets.all(24),
            child: Row(
              children: List.generate(_questions.length, (index) {
                return Expanded(
                  child: Container(
                    height: 4,
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    decoration: BoxDecoration(
                      color: index <= _currentQuestionIndex
                          ? darkAppColors.deepPurple
                          : Colors.white.withAlpha(51),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                );
              }),
            ),
          ),

          // Question cards
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _questions.length,
              itemBuilder: (context, index) {
                return SlideTransition(
                  position: _slideAnimation,
                  child: QuestionCard(
                    question: _questions[index],
                    onAnswer: _handleAnswer,
                    onSkip: _skipQuestion,
                    currentIndex: index,
                    totalQuestions: _questions.length,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
