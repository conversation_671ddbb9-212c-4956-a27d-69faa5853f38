import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

class QuestionCard extends StatefulWidget {
  final Map<String, dynamic> question;
  final void Function(String, dynamic) onAnswer;
  final VoidCallback onSkip;
  final int currentIndex;
  final int totalQuestions;

  const QuestionCard({
    required this.question,
    required this.onAnswer,
    required this.onSkip,
    required this.currentIndex,
    required this.totalQuestions,
    super.key,
  });

  @override
  State<QuestionCard> createState() => _QuestionCardState();
}

class _QuestionCardState extends State<QuestionCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  int? _selectedIndex;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  void _selectOption(int index, dynamic value) {
    HapticFeedback.mediumImpact();

    setState(() {
      _selectedIndex = index;
    });

    // Delay to show selection animation
    Future.delayed(const Duration(milliseconds: 300), () {
      widget.onAnswer(widget.question['id'] as String, value);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              margin: const EdgeInsets.all(24),
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: Colors.white.withAlpha(25),
                  width: 0.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: darkAppColors.deepPurple.withAlpha(51),
                    blurRadius: 20,
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 10,
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Question number
                  Text(
                    '${widget.currentIndex + 1} of ${widget.totalQuestions}',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: darkAppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Question text
                  Text(
                    widget.question['question'] as String,
                    style: AppTextStyles.heading2.copyWith(
                      fontSize: 28,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 40),

                  // Options
                  ...List.generate(
                    (widget.question['options'] as List).length,
                    _buildOption,
                  ),

                  const SizedBox(height: 32),

                  // Skip button
                  GestureDetector(
                    onTap: widget.onSkip,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Colors.white.withAlpha(51),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        'Skip',
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Swipe hint
                  Text(
                    'Swipe left to skip • Swipe right to go back',
                    style: AppTextStyles.bodySmaller.copyWith(
                      color: Colors.white38,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildOption(int index) {
    final options = widget.question['options'] as List;
    final option = options[index];
    final isEmoji = widget.question['type'] == 'emoji';
    final isSelected = _selectedIndex == index;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: GestureDetector(
        onTap: () => _selectOption(index, option),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
          decoration: BoxDecoration(
            color: isSelected
                ? darkAppColors.deepPurple.withAlpha(128)
                : darkAppColors.backgroundColor.withAlpha(128),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isSelected
                  ? darkAppColors.deepPurple
                  : Colors.white.withAlpha(25),
              width: isSelected ? 2 : 0.5,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: darkAppColors.deepPurple.withAlpha(76),
                      blurRadius: 15,
                      spreadRadius: 0,
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withAlpha(25),
                      blurRadius: 5,
                      spreadRadius: 0,
                    ),
                  ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (isEmoji) ...[
                Text(
                  option as String,
                  style: const TextStyle(fontSize: 32),
                ),
                const SizedBox(width: 16),
                Text(
                  (widget.question['labels'] as List)[index] as String,
                  style: AppTextStyles.body.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ] else ...[
                Text(
                  option as String,
                  style: AppTextStyles.body.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
