import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class MilesAwayWidget extends StatelessWidget {
  const MilesAwayWidget({
    required this.milesAway,
    super.key,
  });

  final String milesAway;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Icon(
          CupertinoIcons.map_pin,
          color: Colors.white,
          size: 15,
        ),
        const SizedBox(width: 4),
        Text(
          '$milesAway mi',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 15,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
