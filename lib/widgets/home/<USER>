import 'package:flutter/material.dart';

import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/models/feed/vibe_feed_model.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/widgets/feed/feed_loading_list.dart';
import 'package:vibeo/widgets/shimmer/vibe_tile_shimmer.dart';
import 'package:vibeo/widgets/video_tiles/vibe_tile.dart';

class TilesView extends StatelessWidget {
  final FeedState state;
  final bool stateLoading;
  final List<VibeFeedModel> vibes;
  final String title;
  final String? area;
  final String? scene;
  const TilesView({
    required ScrollController scrollController,
    required this.state,
    required this.stateLoading,
    required this.vibes,
    required this.title,
    this.area,
    this.scene,
    super.key,
  }) : _scrollController = scrollController;

  final ScrollController _scrollController;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 300,
      child: () {
        if (!vibes.any((vibe) => vibe.feeds.isNotEmpty) || stateLoading) {
          return buildLoadingList();
        } else if (vibes.isNotEmpty) {
          final itemCount =
              vibes.last.isEndMarker ? vibes.length - 1 : vibes.length;

          if (itemCount > 0) {
            return ListView.builder(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              itemCount: vibes.last.isEndMarker
                  ? itemCount
                  : itemCount + (vibes.length > 2 ? 1 : 0),
              padding: SizeUtils.horizontalPadding,
              clipBehavior: Clip.none,
              itemBuilder: (ctx, index) {
                if (index == itemCount) {
                  return const VibeTileShimmer();
                }
                return VibeTile(
                  vibes: vibes.sublist(0, itemCount),
                  index: index,
                  title: title,
                  area: area,
                  scene: scene,
                );
              },
            );
          }
        }

        // Show shimmer loading for initial state
        return const SizedBox();
      }(),
    );
  }
}
