import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/text/gradient_text.dart';
import 'package:vibeo/widgets/venue/perks_tag.dart';

class TitleWidget extends StatelessWidget {
  final bool isLoading;
  final String? title;
  final bool exclusive;
  final bool isLive;
  final bool isBeo;
  const TitleWidget({
    required this.isLoading,
    required this.title,
    this.exclusive = false,
    this.isLive = false,
    this.isBeo = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: SizeUtils.horizontalPadding.copyWith(bottom: isBeo ? 4 : 14),
      child: isLoading && title == null
          ? RepaintBoundary(
              child: Shimmer.fromColors(
                baseColor: darkAppColors.lightColor.withAlpha(50),
                highlightColor:
                    darkAppColors.secondaryBackgroundColor.withAlpha(100),
                child: Container(
                  width: 100,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: Colors.grey,
                    borderRadius: BorderRadius.all(
                      Radius.circular(100),
                    ),
                  ),
                ),
              ),
            )
          : Row(
              children: [
                if (isBeo)
                  Padding(
                    padding: const EdgeInsets.only(right: 8, bottom: 4),
                    child: GradientText(
                      'beo',
                      gradient: LinearGradient(
                        colors: [
                          const Color.fromARGB(255, 131, 65, 223),
                          // darkAppColors.deepPurple,
                          darkAppColors.lightColor,
                        ],
                        stops: const [0.4, 0.8],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      style: AppTextStyles.heading1,
                    ),
                  ),
                Flexible(
                  child: Text(
                    title!,
                    style: AppTextStyles.headingTitle,
                  ),
                ),
                if (exclusive)
                  Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: PerksTag(
                      colors: [
                        darkAppColors.deepPurple.withAlpha(204),
                        darkAppColors.purpleShade.withAlpha(204),
                      ],
                      icon: CupertinoIcons.star_fill,
                      title: 'EXCLUSIVE',
                    ),
                  ),
                if (isLive)
                  Padding(
                    padding: const EdgeInsets.only(left: 12),
                    child: PerksTag(
                      colors: [
                        Colors.red.withAlpha(204),
                        Colors.pink.withAlpha(204),
                      ],
                      icon: CupertinoIcons.circle_filled,
                      iconSize: 8,
                      title: 'LIVE',
                    ),
                  ),
              ],
            ),
    );
  }
}
