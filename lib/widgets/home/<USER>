import 'package:flutter/material.dart';
import 'package:flutter_carousel_slider/carousel_slider.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/models/feed/vibe_feed_model.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/widgets/shimmer/vibe_tile_shimmer.dart';
import 'package:vibeo/widgets/video_tiles/vibe_tile.dart';

class LiveSliderTilesView extends StatelessWidget {
  final FeedState state;
  final bool stateLoading;
  final List<VibeFeedModel> vibes;
  final String title;
  const LiveSliderTilesView({
    required ScrollController scrollController,
    required this.state,
    required this.stateLoading,
    required this.vibes,
    required this.title,
    super.key,
  }) : _scrollController = scrollController;

  final ScrollController _scrollController;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 420,
      child: () {
        // Show shimmer only on initial load when no data exists
        if (!vibes.any((vibe) => vibe.feeds.isNotEmpty) && stateLoading) {
          return buildLoadingList();
        }

        if (vibes.isNotEmpty) {
          final itemCount =
              vibes.last.isEndMarker ? vibes.length - 1 : vibes.length;

          if (itemCount > 0) {
            final List<Widget> carouselItems = [];

            // Add all vibe tiles
            for (int i = 0; i < itemCount; i++) {
              carouselItems.add(
                VibeTile(
                  vibes: vibes.sublist(0, itemCount),
                  index: i,
                  title: title,
                ),
              );
            }

            // Add shimmer if needed
            if (!vibes.last.isEndMarker && vibes.length > 2) {
              carouselItems.add(const VibeTileShimmer());
            }

            return CarouselSlider.builder(
              unlimitedMode: false,
              controller: CarouselSliderController(),
              slideBuilder: (index) {
                return carouselItems[index];
              },
              slideTransform: const DefaultTransform(),
              itemCount: carouselItems.length,
              viewportFraction: 0.68,
              enableAutoSlider: false,
              scrollPhysics: const BouncingScrollPhysics(),
              onSlideChanged: (index) {
                // Check if we're near the end to trigger pagination
                if (index >= carouselItems.length - 2) {
                  // Simulate scroll listener behavior
                  if (_scrollController.hasClients) {
                    _scrollController
                        .jumpTo(_scrollController.position.maxScrollExtent);
                  }
                }
              },
            );
          }
        }

        // Show shimmer loading for initial state
        return CarouselSlider.builder(
          unlimitedMode: true,
          controller: CarouselSliderController(),
          slideBuilder: (index) => const VibeTileShimmer(),
          slideTransform: const DefaultTransform(),
          itemCount: 5,
          viewportFraction: 0.8,
          enableAutoSlider: false,
        );
      }(),
    );
  }

  Widget buildLoadingList() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: 5,
      padding: SizeUtils.horizontalPadding,
      itemBuilder: (ctx, index) => const VibeTileShimmer(),
    );
  }
}
