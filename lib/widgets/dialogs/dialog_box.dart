import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/widgets/auth/auth_button.dart';

class GlassmorphicDialog extends StatelessWidget {
  final bool canPop;
  final String title;
  final String description;
  final String buttonText;
  final VoidCallback onPressed;

  const GlassmorphicDialog({
    required this.title,
    required this.description,
    required this.buttonText,
    required this.onPressed,
    this.canPop = true,
    super.key,
  });

  static Future<void> show(
    BuildContext context, {
    required String title,
    required String description,
    required String buttonText,
    required VoidCallback onPressed,
    bool canPop = true,
  }) {
    return showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(178),
      barrierDismissible: canPop, // Changed to allow dismissal
      builder: (context) => PopScope(
        canPop: false, // Still prevent system back button
        child: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: canPop ? () => Navigator.pop(context) : null,
          child: GlassmorphicDialog(
            title: title,
            description: description,
            buttonText: buttonText,
            onPressed: onPressed,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: canPop ? () => RouteUtils.pop(context) : null,
        child: Center(
          child: GestureDetector(
            onTap: () {},
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 32),
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(24)),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withAlpha((255 * 0.1).toInt()),
                          Colors.white.withAlpha((255 * 0.05).toInt()),
                        ],
                      ),
                      borderRadius: const BorderRadius.all(Radius.circular(24)),
                      border: Border.all(
                        color: Colors.white.withAlpha((255 * 0.2).toInt()),
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            description,
                            style: TextStyle(
                              color: Colors.white.withAlpha(204),
                              fontSize: 16,
                              height: 1.5,
                            ),
                          ),
                          const SizedBox(height: 24),
                          authButton(
                            title: buttonText,
                            onTap: onPressed,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
