import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/routes/route.dart';

class InfoDialog extends StatelessWidget {
  final String title;
  final String description;

  const InfoDialog({
    required this.title,
    required this.description,
    super.key,
  });

  static Future<void> showInfo(
    BuildContext context, {
    required String title,
    required String description,
  }) {
    return showDialog(
      context: context,
      barrierColor: Colors.black.withAlpha(178),
      barrierDismissible: true, // Changed to true
      builder: (context) => PopScope(
        canPop: true, // Changed to true
        child: GestureDetector(
          onTap: () => Navigator.of(context).pop(), // Add this
          child: InfoDialog(
            title: title,
            description: description,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 32),
        child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(24)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withAlpha((255 * 0.1).toInt()),
                    Colors.white.withAlpha((255 * 0.05).toInt()),
                  ],
                ),
                borderRadius: const BorderRadius.all(Radius.circular(24)),
                border: Border.all(
                  color: Colors.white.withAlpha((255 * 0.2).toInt()),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 18,
                  horizontal: 24,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            RouteUtils.pop(context);
                          },
                          icon: const Icon(
                            CupertinoIcons.clear,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.white.withAlpha(204),
                        fontSize: 16,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
