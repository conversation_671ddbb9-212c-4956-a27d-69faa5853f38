import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/feed/drive_there_logic.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/explore/loc_widget.dart';

void showLocBottomSheet(
  String feedID,
  String address,
  BuildContext context, {
  bool isGlassmorphism = true,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    builder: (ctx) {
      return ClipRRect(
        borderRadius: isGlassmorphism
            ? const BorderRadius.vertical(top: Radius.circular(24))
            : BorderRadius.zero,
        child: BackdropFilter(
          filter: isGlassmorphism
              ? ImageFilter.blur(sigmaX: 10, sigmaY: 10)
              : ImageFilter.blur(sigmaX: 100, sigmaY: 100),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 12,
            ),
            decoration: BoxDecoration(
              color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
              borderRadius: isGlassmorphism
                  ? const BorderRadius.vertical(top: Radius.circular(24))
                  : null,
              border: Border.all(
                color: Colors.white.withAlpha(26),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  spreadRadius: -5,
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Drag indicator
                if (isGlassmorphism)
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      margin: const EdgeInsets.only(bottom: 20),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(76),
                        borderRadius:
                            const BorderRadius.all(Radius.circular(2)),
                      ),
                    ),
                  )
                else
                  const SizedBox(
                    height: 10,
                  ),
                Text(
                  'Catch this Vibe',
                  style: TextStyle(
                    color: Colors.white.withAlpha(229),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  'Choose your best way to get there.',
                  style: TextStyle(
                    color: Colors.white.withAlpha(178),
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    LocWidget(
                      image: 'assets/images/uber.png',
                      onTap: () async {
                        final AnalyticsService analytics =
                            AnalyticsService.instance;
                        await analytics.trackNavigationChoice(feedID, 'Uber');
                        if (context.mounted) {
                          await openUber(
                            address,
                            context: context,
                          );
                        }
                      },
                    ),
                    LocWidget(
                      image: 'assets/images/lyft.png',
                      onTap: () async {
                        final AnalyticsService analytics =
                            AnalyticsService.instance;
                        await analytics.trackNavigationChoice(feedID, 'Lyft');
                        await openLyft(
                          address,
                        );
                      },
                    ),
                    LocWidget(
                      image: 'assets/images/google_maps.png',
                      onTap: () async {
                        final AnalyticsService analytics =
                            AnalyticsService.instance;
                        await analytics.trackNavigationChoice(
                          feedID,
                          'GoogleMaps',
                        );
                        await openGoogleMaps(
                          address,
                        );
                      },
                    ),
                    LocWidget(
                      image: 'assets/images/apple_maps.png',
                      onTap: () async {
                        final AnalyticsService analytics =
                            AnalyticsService.instance;
                        await analytics.trackNavigationChoice(
                          feedID,
                          'AppleMaps',
                        );
                        await openAppleMaps(
                          address,
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      );
    },
  );
}
