import 'package:flutter/material.dart';

import 'package:shimmer/shimmer.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

Widget buildShimmerTag() {
  return Padding(
    padding: const EdgeInsets.only(
      right: 8,
    ),
    child: ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(24)),
      child: Shimmer.fromColors(
        baseColor: Colors.white.withAlpha((255 * 0.05).toInt()),
        highlightColor: Colors.white.withAlpha((255 * 0.2).toInt()),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withAlpha((255 * 0.2).toInt()),
            borderRadius: const BorderRadius.all(Radius.circular(24)),
          ),
          child: Container(
            width: 80, // Approximate width of location text
            height: 14, // Match font size
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ),
      ),
    ),
  );
}

Widget buildVibeTag({
  required bool isSelected,
  required String title,
  required VoidCallback onTap,
  bool isLive = false,
  bool isBeo = false,
}) {
  return Padding(
    padding: const EdgeInsets.only(
      right: 8,
    ),
    child: HapticButton(
      onTap: onTap,
      child: Center(
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutQuart,
          padding: EdgeInsets.symmetric(
            horizontal: 18,
            vertical: isBeo ? 12 : 8,
          ),
          decoration: BoxDecoration(
            gradient: isSelected
                ? isBeo
                    ? const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color.fromARGB(255, 187, 145, 250),
                          Color.fromARGB(255, 151, 109, 215),
                          Color.fromARGB(255, 125, 56, 235),
                        ],
                      )
                    : const LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color.fromARGB(255, 255, 255, 255),
                          Color.fromARGB(255, 213, 213, 213),
                        ],
                      )
                : const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.fromARGB(255, 36, 36, 36),
                      Color.fromARGB(255, 22, 22, 22),
                    ],
                  ),
            borderRadius: const BorderRadius.all(Radius.circular(100)),
            border: Border.all(
              color: Colors.white.withAlpha((255 * 0.15).toInt()),
              width: isSelected ? 1.5 : 1.0,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: darkAppColors.lightColor
                          .withAlpha((255 * 0.4).toInt()),
                      blurRadius: 5,
                      spreadRadius: 0.001,
                    ),
                  ]
                : null,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isLive)
                Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: _BlinkingDot(),
                ),
              Text(
                title,
                style: TextStyle(
                  color: isSelected && !isBeo
                      ? Colors.black
                      : Colors.white.withAlpha((255 * 0.85).toInt()),
                  fontSize: isBeo ? 15 : 14,
                  fontWeight: isBeo ? FontWeight.bold : null,
                  letterSpacing: 0.2,
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  );
}

class _BlinkingDot extends StatefulWidget {
  @override
  State<_BlinkingDot> createState() => _BlinkingDotState();
}

class _BlinkingDotState extends State<_BlinkingDot>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    )..repeat(reverse: true);

    _animation = Tween<double>(
      begin: 0.3,
      end: 1,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: Container(
        width: 8,
        height: 8,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.red,
          boxShadow: [
            BoxShadow(
              color: Colors.red.withAlpha((255 * 0.5).toInt()),
              blurRadius: 6,
              spreadRadius: 2,
            ),
          ],
        ),
      ),
    );
  }
}
