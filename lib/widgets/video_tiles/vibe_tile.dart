import 'package:flutter/material.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/constants/video_tiles.dart';

import 'package:vibeo/models/models.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

import 'package:vibeo/widgets/feed/live_widget.dart';

import 'package:vibeo/widgets/feed/perk_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class VibeTile extends StatelessWidget {
  final int index;
  final List<VibeFeedModel> vibes;
  final String title;
  final String? area;
  final String? scene;

  const VibeTile({
    required this.index,
    required this.vibes,
    required this.title,
    this.area,
    this.scene,
    super.key,
  });

  Future<void> onTap(BuildContext context) async {
    final AnalyticsService analytics = AnalyticsService.instance;
    await analytics.logHomepageInteraction(
      interactionType: title,
    );
    if (context.mounted) {
      await RouteUtils.pushNamed(
        context,
        RoutePaths.vibeFeedPlayingPage,
        arguments: {
          'initialVenueIndex': index,
          'venues': vibes,
          'title': title,
          'area': area,
          'scene': scene,
        },
      );
      // await RouteUtils.pushNamed(
      //   context,
      //   RoutePaths.videoPlayPage,
      //   arguments: {
      //     'initialVenueIndex': index,
      //     'venues': vibes,
      //     'title': title,
      //   },
      // );
    }
  }

  @override
  Widget build(BuildContext context) {
    final perksType = vibes[index].feeds.first.perksType;
    final feed = vibes[index].feeds.first;

    return GestureDetector(
      onTap: () => onTap(context),
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        width: 200,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(VideoTileSizeUtils.borderRadius),
          ),
          boxShadow: AppShadowStyles.baseStyle,
          color: darkAppColors.secondaryBackgroundColor,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.all(
            Radius.circular(VideoTileSizeUtils.borderRadius),
          ),
          child: Stack(
            children: [
              AspectRatio(
                aspectRatio: 12 / 18,
                child: ShaderMask(
                  shaderCallback: (bounds) {
                    return LinearGradient(
                      colors: [
                        Colors.black.withAlpha(140),
                        Colors.black45,
                        Colors.transparent,
                        Colors.black.withAlpha(120),
                        Colors.black87,
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: const [0, 0.2, 0.5, 0.8, 1],
                    ).createShader(bounds);
                  },
                  blendMode: BlendMode.darken,
                  child: CachedNetworkImage(
                    imageUrl: feed.thumbnailURL,
                    fit: BoxFit.cover,
                    width: 200,
                    height: 400,
                    maxHeightDiskCache: 400,
                    maxWidthDiskCache: 200,
                    memCacheWidth: 200,
                    memCacheHeight: 400,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: darkAppColors.secondaryBackgroundColor,
                      highlightColor: darkAppColors.secondaryBackgroundColor,
                      child: ColoredBox(
                        color: darkAppColors.secondaryBackgroundColor,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.error),
                    ),
                  ),
                ),
              ),
              if (feed.isLive ?? false)
                const Positioned(
                  top: 16,
                  right: 10,
                  child: LiveWidget(),
                ),
              if (perksType != null && perksType != PerksType.none)
                Positioned(
                  top: 16,
                  left: 10,
                  child: PerkWidget(
                    perksType: perksType,
                    title: feed.title,
                  ),
                ),
              // if (feed.venueID != null &&
              //     feed.venueID! == 'd9200d39-4309-451e-a96b-95f250ce003a')
              //   Positioned(
              //     left: 10,
              //     top: 42,
              //     child: PerkTimerWidget(
              //       expiryDate: DateTime.now().add(
              //         const Duration(hours: 2),
              //       ),
              //     ),
              //   ),
              Positioned(
                bottom: 14,
                left: 12,
                right: 12,
                child: _buildVenueInfo(feed),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVenueInfo(FeedModel feed) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow(feed.venueName, feed),
        // const SizedBox(height: 4),
        // _buildBottomInfo(feed),
      ],
    );
  }

  Widget _buildInfoRow(String text, FeedModel feed) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          textBaseline: TextBaseline.alphabetic,
          children: [
            const Icon(
              Icons.location_on,
              size: 16,
              color: Colors.grey,
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                text,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
                style: AppTextStyles.bodyBold.copyWith(
                  height: 1.2,
                  color: Colors.white,
                  fontSize: 18,
                  shadows: [
                    const Shadow(
                      color: Colors.black,
                      offset: Offset(0, 1),
                      blurRadius: 1,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        if (feed.area != '' && feed.area.split(' ').length <= 5)
          Padding(
            padding: const EdgeInsets.only(
              top: 2,
              left: 22,
            ),
            child: Text(
              '${feed.area}',
              style: AppTextStyles.body.copyWith(
                color: darkAppColors.lightColor,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}
