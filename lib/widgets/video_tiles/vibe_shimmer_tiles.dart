import 'package:flutter/material.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/shimmer/vibe_tile_shimmer.dart';

Widget buildShimmerGrid({double top = 120}) {
  return GridView.builder(
    shrinkWrap: true,
    padding: SizeUtils.horizontalPadding.copyWith(
      top: top,
      bottom: 60,
    ),
    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2,
      mainAxisSpacing: 5,
      crossAxisSpacing: 5,
      childAspectRatio: 1.2 / 2,
    ),
    itemCount: 10,
    itemBuilder: (context, index) => const VibeTileShimmer(),
  );
}
