import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';

Container tagWidget(String tag) {
  return Container(
    padding: const EdgeInsets.symmetric(
      horizontal: 15,
      vertical: 5,
    ),
    decoration: BoxDecoration(
      color: darkAppColors.lightColor.withAlpha(26),
      borderRadius: const BorderRadius.all(
        Radius.circular(24),
      ),
    ),
    child: Text(
      tag,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 15,
      ),
    ),
  );
}
