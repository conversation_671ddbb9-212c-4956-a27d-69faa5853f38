import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/venue/perks_tag.dart';

Widget buildPerkTag(List<PerkVenueType> perksType, {bool isExclusive = false}) {
  // Check for different combinations of perks
  final hasOffers = perksType.contains(PerkVenueType.offers);
  final hasPromoters = perksType.contains(PerkVenueType.promoters);
  final hasExclusive =
      perksType.contains(PerkVenueType.exclusiveOffers) || isExclusive;
  final hasEvents = perksType.contains(PerkVenueType.events);

  // Determine tag text and colors based on available perks
  String tagText;
  List<Color> tagColors;
  IconData tagIcon;

  if (hasExclusive) {
    // Exclusive offers have highest priority
    tagText = 'EXCLUSIVE';
    tagColors = [
      Colors.amber.withAlpha(204),
      Colors.orange.withAlpha(204),
    ];
    tagIcon = CupertinoIcons.star_fill;
  } else if (hasOffers && hasPromoters) {
    // Both offers and promoters available
    tagText = 'PERKS';
    tagColors = [
      Colors.teal.withAlpha(204),
      Colors.cyan.withAlpha(204),
    ];
    tagIcon = CupertinoIcons.gift_fill;
  } else if (hasPromoters) {
    // Only promoters available
    tagText = 'GET ENTRY';
    tagColors = [
      Colors.red.withAlpha(204),
      Colors.pink.withAlpha(204),
    ];
    tagIcon = CupertinoIcons.person_crop_circle_fill_badge_checkmark;
  } else if (hasOffers) {
    // Only offers available
    tagText = 'OFFERS';
    tagColors = [
      darkAppColors.deepPurple.withAlpha(204),
      darkAppColors.purpleShade.withAlpha(204),
    ];
    tagIcon = CupertinoIcons.tag_fill;
  } else if (hasEvents) {
    // Only events available
    tagText = 'EVENTS';
    tagColors = [
      Colors.orange.withAlpha(204),
      Colors.amber.withAlpha(204),
    ];
    tagIcon = CupertinoIcons.calendar_badge_plus;
  } else {
    // Fallback case (though this shouldn't happen if this widget only shows when perks exist)
    return const SizedBox.shrink();
  }

  return PerksTag(
    colors: tagColors,
    icon: tagIcon,
    title: tagText,
  );
}
