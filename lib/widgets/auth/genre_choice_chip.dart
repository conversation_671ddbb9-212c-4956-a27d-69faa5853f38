import 'package:flutter/material.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

Widget buildGenreChip(
  String label,
  VoidCallback onTap, {
  required bool isSelected,
}) {
  return HapticButton(
    onTap: onTap,
    child: AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.symmetric(
        horizontal: 26,
        vertical: 8,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.white.withAlpha(26),
        borderRadius: const BorderRadius.all(Radius.circular(24)),
        border: Border.all(
          color: isSelected ? Colors.white : Colors.transparent,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: Colors.white.withAlpha(76),
                  blurRadius: 8,
                  spreadRadius: 1,
                ),
              ]
            : null,
      ),
      child: AnimatedDefaultTextStyle(
        duration: const Duration(milliseconds: 200),
        style: TextStyle(
          color: isSelected ? Colors.black : Colors.white,
          fontSize: 18,
        ),
        child: Text(label),
      ),
    ),
  );
}
