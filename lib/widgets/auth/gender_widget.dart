import 'package:flutter/material.dart';
import 'package:vibeo/utils/size_utils.dart';

enum Gender { man, woman, nonbinary, other }

class GenderWidget extends StatelessWidget {
  final VoidCallback onclick;
  final String title;
  final IconData icon;

  final bool isSelected;

  const GenderWidget({
    required this.isSelected,
    required this.onclick,
    required this.title,
    required this.icon,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 12,
      ),
      child: ElevatedButton(
        onPressed: onclick,
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(
            vertical: 12,
            horizontal: 20,
          ),
          alignment: Alignment.topLeft,
          textStyle: const TextStyle(
            fontSize: 20,
            color: Colors.white,
          ),
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(100)),
          ),
          backgroundColor: isSelected ? Colors.white : Colors.white10,
          side: BorderSide(
            width: 1,
            color: isSelected ? Colors.white : Colors.white10,
          ),
        ),
        child: SizedBox(
          width: SizeUtils.screenWidth,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? Colors.black : Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
