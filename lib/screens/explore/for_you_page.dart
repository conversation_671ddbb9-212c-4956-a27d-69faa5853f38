import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/views/explore/beo_rec_tiles.dart';
import 'package:vibeo/views/explore/day_special_tile.dart';
import 'package:vibeo/views/explore/explore_banner_tiles.dart';
import 'package:vibeo/views/explore/sponsor_section_tile.dart';
import 'package:vibeo/views/explore/vibe_type_tiles.dart';
import 'package:vibeo/views/view.dart';
import 'package:vibeo/widgets/tutorial/tutorial_manager.dart';

class ForYouPage extends StatefulWidget {
  final VoidCallback? setPageIndex;
  const ForYouPage({required this.setPageIndex, super.key});

  @override
  State<ForYouPage> createState() => _ForYouPageState();
}

class _ForYouPageState extends State<ForYouPage> {
  // Global keys for tutorial targeting
  final GlobalKey _liveTilesKey = GlobalKey();
  final GlobalKey _byAreaTilesKey = GlobalKey();
  final GlobalKey _byMusicTilesKey = GlobalKey();
  final GlobalKey _placesNearYouKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    // Register tutorial keys
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TutorialManager.instance.registerTargetKey('live_tiles', _liveTilesKey);
      TutorialManager.instance
          .registerTargetKey('by_area_tiles', _byAreaTilesKey);
      TutorialManager.instance
          .registerTargetKey('by_music_tiles', _byMusicTilesKey);
      TutorialManager.instance
          .registerTargetKey('places_near_you', _placesNearYouKey);
    });
  }

  @override
  void dispose() {
    // Unregister tutorial keys
    TutorialManager.instance.unregisterTargetKey('live_tiles');
    TutorialManager.instance.unregisterTargetKey('by_area_tiles');
    TutorialManager.instance.unregisterTargetKey('by_music_tiles');
    TutorialManager.instance.unregisterTargetKey('places_near_you');
    super.dispose();
  }

  Future<void> onRefresh(BuildContext context) async {
    final state = context.read<FeedBloc>().state;
    final location = context.read<UserBloc>().state.user!.location;
    final currentLocation = await getCurrentLocation();

    // Batch all refresh events together to reduce multiple rebuilds
    final List<FeedEvent> refreshEvents = [
      const FetchLiveFeedsEvent(
        limit: 4,
        forceRefresh: true,
      ),
    ];

    if (context.mounted) {
      refreshEvents.add(
        FetchNearbyFeedsEvent(
          limit: 4,
          skip: 0,
          location: currentLocation ?? location,
          forceRefresh: true,
        ),
      );

      // Add area feeds
      state.areaFeeds.forEach((area, feeds) {
        refreshEvents.add(
          FetchAreaFeedsEvent(
            area,
            limit: 4,
            forceRefresh: true,
          ),
        );
      });

      // Add vibes
      refreshEvents.addAll([
        const FetchTalkVibesEvent(
          limit: 4,
          forceRefresh: true,
        ),
        const FetchDanceVibesEvent(
          limit: 4,
          forceRefresh: true,
        ),
      ]);

      // Add scene feeds
      state.sceneFeeds.forEach((scene, feeds) {
        refreshEvents.add(
          FetchSceneFeedsEvent(
            scene,
            limit: 4,
            forceRefresh: true,
          ),
        );
      });

      // Dispatch all events at once
      for (final event in refreshEvents) {
        context.read<FeedBloc>().add(event);
      }
    }

    return Future.delayed(Durations.medium2);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<FeedBloc, FeedState>(
        buildWhen: (previous, current) {
          return previous.liveVibes != current.liveVibes ||
              previous.nearbyFeeds != current.nearbyFeeds ||
              previous.areaFeeds != current.areaFeeds ||
              previous.sceneFeeds != current.sceneFeeds;
        },
        builder: (context, state) {
          final bool hasLiveTilesContent =
              state.liveVibes != null && state.liveVibes!.isNotEmpty;

          return RefreshIndicator.adaptive(
            onRefresh: () => onRefresh(context),
            color: Colors.white,
            displacement: 40,
            child: CustomScrollView(
              key: const PageStorageKey('for_you_scroll'),
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: [
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return _buildLazyWidget(
                        index: index,
                        hasLiveTilesContent: hasLiveTilesContent,
                        setPageIndex: widget.setPageIndex,
                      );
                    },
                    childCount: 10,
                  ),
                ),
                const SliverPadding(
                  padding: EdgeInsets.only(bottom: 20),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLazyWidget({
    required int index,
    required bool hasLiveTilesContent,
    required VoidCallback? setPageIndex,
  }) {
    return RepaintBoundary(
      child: hasLiveTilesContent
          ? _getLazyLiveTilesContentWidget(index, setPageIndex)
          : _getLazyNoLiveTilesContentWidget(index, setPageIndex),
    );
  }

  Widget _getLazyLiveTilesContentWidget(int index, VoidCallback? setPageIndex) {
    // Lazy load widgets using a builder pattern
    return LayoutBuilder(
      builder: (context, constraints) {
        switch (index) {
          case 0:
            return LiveTiles(key: _liveTilesKey);
          case 1:
            return const SponsorSectionTile();
          case 2:
            return ByAreaTiles(key: _byAreaTilesKey);
          case 3:
            return const BeoAIRecTiles();
          case 4:
            return ByMusicTiles(key: _byMusicTilesKey);
          case 5:
            return ExploreBannerTiles(onTap: setPageIndex);
          case 6:
            return const BySceneTiles();
          case 7:
            return const VibeTypeTiles();
          case 8:
            return const DaySpecialTile();
          case 9:
            return PlacesNearYouTiles(key: _placesNearYouKey);
          default:
            return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _getLazyNoLiveTilesContentWidget(
    int index,
    VoidCallback? setPageIndex,
  ) {
    // Lazy load widgets using a builder pattern
    return LayoutBuilder(
      builder: (context, constraints) {
        switch (index) {
          case 0:
            return LiveTiles(key: _liveTilesKey);
          case 1:
            return ByAreaTiles(key: _byAreaTilesKey);
          case 2:
            return const SponsorSectionTile();
          case 3:
            return const BeoAIRecTiles();
          case 4:
            return ByMusicTiles(key: _byMusicTilesKey);
          case 5:
            return ExploreBannerTiles(onTap: setPageIndex);
          case 6:
            return const BySceneTiles();
          case 7:
            return const VibeTypeTiles();
          case 8:
            return const DaySpecialTile();
          case 9:
            return PlacesNearYouTiles(key: _placesNearYouKey);
          default:
            return const SizedBox.shrink();
        }
      },
    );
  }
}
