import 'package:extended_nested_scroll_view/extended_nested_scroll_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/content/bloc/content_bloc.dart';

import 'package:vibeo/views/view.dart';
import 'package:vibeo/widgets/content/uploading_widget.dart';

class ExplorePage extends StatelessWidget {
  final int? initialTabIndex;

  const ExplorePage({super.key, this.initialTabIndex});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BuildNestedScrollView(
        initialTabIndex: initialTabIndex,
      ),
    );
  }
}

class BuildNestedScrollView extends StatefulWidget {
  final int? initialTabIndex;

  const BuildNestedScrollView({
    required this.initialTabIndex,
    super.key,
  });

  @override
  State<BuildNestedScrollView> createState() => _BuildNestedScrollViewState();
}

class _BuildNestedScrollViewState extends State<BuildNestedScrollView>
    with SingleTickerProviderStateMixin {
  late TabController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TabController(
      length: 3,
      vsync: this,
      initialIndex: widget.initialTabIndex ?? 0,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ExtendedNestedScrollView(
      onlyOneScrollInBody: true,
      physics: const ClampingScrollPhysics(),
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return [
          ExploreAppBar(
            controller: _controller,
          ),

          // StatefulBuilder(
          //   builder: (BuildContext context, StateSetter setState) {
          //     return (_showExtraHeader ||
          //                 widget.initialTabIndex != null) &&
          //             _controller.index == 1
          //         ? SliverPersistentHeader(
          //             pinned: true,
          //             delegate: _SliverAppBarDelegate(
          //               child: const FilterVenueSection(),
          //               maxHeight: 90,
          //               minHeight: 90,
          //             ),
          //           )
          //         : const SliverToBoxAdapter(child: SizedBox.shrink());
          //   },
          // ),
          BlocBuilder<ContentBloc, ContentState>(
            builder: (context, state) {
              if (state is ContentInitial) {
                return const SliverToBoxAdapter(child: SizedBox.shrink());
              }
              if (state is FeedSuccess) {
                return const SliverToBoxAdapter(child: SizedBox.shrink());
              }
              if (state is FeedUploading) {
                return SliverPersistentHeader(
                  pinned: true,
                  delegate: _SliverAppBarDelegate(
                    child: const RepaintBoundary(
                      child: UploadingWidget(),
                    ),
                    maxHeight: 50,
                    minHeight: 50,
                  ),
                );
              }
              return const SliverToBoxAdapter(child: SizedBox.shrink());
            },
            buildWhen: (previous, current) => previous != current,
          ),
        ];
      },
      body: ExplorePages(
        controller: _controller,
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double maxHeight;
  final double minHeight;

  _SliverAppBarDelegate({
    required this.child,
    required this.maxHeight,
    required this.minHeight,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child.runtimeType != oldDelegate.child.runtimeType;
  }
}
