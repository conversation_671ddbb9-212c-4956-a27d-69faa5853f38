import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/services.dart';
import 'package:vibeo/constants/auth/age_months.dart';
import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/utils/validations.dart';
import 'package:vibeo/widgets/auth/app_bar.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';

class AgePage extends StatefulWidget {
  const AgePage({super.key});

  @override
  State<AgePage> createState() => _AgePageState();
}

class _AgePageState extends State<AgePage> {
  int selectedMonthIndex = 5;
  int selectedDay = 9;
  int selectedYear = DateTime(1996).year;
  bool isValid = false;
  late int age;

  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;
  late FixedExtentScrollController _yearController;

  @override
  void initState() {
    super.initState();

    _monthController = FixedExtentScrollController(
      initialItem: selectedMonthIndex,
    );

    _dayController = FixedExtentScrollController(
      initialItem: selectedDay - 1,
    );

    _yearController = FixedExtentScrollController(
      initialItem: DateTime.now().year - selectedYear,
    );

    age = DateTime.now().year - selectedYear;

    isValid = age >= 21;
  }

  @override
  void dispose() {
    _monthController.dispose();
    _dayController.dispose();
    _yearController.dispose();
    super.dispose();
  }

  void navigateToNextPage() {
    if (Validators.validateWheelDateOfBirth(
      context,
      month: selectedMonthIndex,
      day: selectedDay,
      year: selectedYear,
    )) {
      final DateTime dob = DateTime(
        selectedYear,
        selectedMonthIndex + 1,
        selectedDay,
      );

      final String dobString = dob.toIso8601String();
      age = DateTime.now().year - selectedYear;
      context.read<RegistrationCubit>().updateAge(
            age,
            dobString,
          );
      RouteUtils.pushNamed(context, RoutePaths.genderPage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: authAppBar(),
      body: SafeArea(
        child: Padding(
          padding: SizeUtils.horizontalPadding.copyWith(top: 20, bottom: 40),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'When is your birthday?',
                style: AppTextStyles.heading1,
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                'You must be at least 21 years old to register.',
                style: AppTextStyles.titleLight.copyWith(
                  fontWeight: isValid ? FontWeight.normal : FontWeight.bold,
                  color: isValid ? Colors.white : Colors.red,
                ),
              ),
              const SizedBox(
                height: 40,
              ),
              Row(
                children: [
                  Expanded(
                    child: _buildScrollColumn(
                      items: months,
                      controller: _monthController,
                      onSelected: (index) =>
                          setState(() => selectedMonthIndex = index),
                      selectedIndex: selectedMonthIndex,
                    ),
                  ),
                  Expanded(
                    child: _buildScrollColumn(
                      items: List.generate(31, (index) => '${index + 1}'),
                      controller: _dayController,
                      onSelected: (index) =>
                          setState(() => selectedDay = index + 1),
                      selectedIndex: selectedDay - 1,
                    ),
                  ),
                  Expanded(
                    child: _buildScrollColumn(
                      items: List.generate(
                        80,
                        (index) => '${DateTime.now().year - index}',
                      ),
                      controller: _yearController,
                      onSelected: (index) => setState(() {
                        selectedYear = DateTime.now().year - index;

                        isValid = DateTime.now().year - selectedYear >= 21;
                      }),
                      selectedIndex: DateTime.now().year - selectedYear,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              authButton(
                title: 'Almost there',
                onTap: navigateToNextPage,
                enabled: isValid,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScrollColumn({
    required List<String> items,
    required ScrollController controller,
    required void Function(int) onSelected,
    required int selectedIndex,
  }) {
    const itemHeight = 50.0;

    return SizedBox(
      height: 300,
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollEndNotification) {
            HapticFeedback.selectionClick();
          }
          return true;
        },
        child: ListWheelScrollView.useDelegate(
          controller: controller as FixedExtentScrollController,
          itemExtent: itemHeight,
          perspective: 0.005,
          diameterRatio: 1.2,
          physics: const FixedExtentScrollPhysics(),
          onSelectedItemChanged: onSelected,
          childDelegate: ListWheelChildBuilderDelegate(
            childCount: items.length,
            builder: (context, index) => Center(
              child: AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  fontSize: selectedIndex == index ? 38 : 24,
                  color: selectedIndex == index ? Colors.white : Colors.white30,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'PulpDisplay',
                ),
                child: Text(items[index]),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
