import 'dart:async';

import 'package:flutter/material.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/exceptions/try_wrapper.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/utils/validations.dart';
import 'package:vibeo/widgets/auth/app_bar.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';

import 'package:pinput/pinput.dart';

class OtpPage extends StatefulWidget {
  const OtpPage({super.key});

  @override
  State<OtpPage> createState() => _OtpPageState();
}

class _OtpPageState extends State<OtpPage> {
  late final TextEditingController _controller;
  final List<FocusNode> _focusNodes = List.generate(6, (_) => FocusNode());
  bool isValid = false;
  bool isResendActive = false;
  int resendTimer = 60;
  Timer? _timer;

  String? _verificationId;

  @override
  void initState() {
    _controller = TextEditingController();
    final authState = context.read<AuthBloc>().state;
    if (authState is PhoneVerificationSent) {
      _verificationId = authState.verificationId;
    }
    super.initState();
    _startResendTimer();
  }

  void _startResendTimer() {
    setState(() {
      isResendActive = false;
      resendTimer = 60;
    });

    _timer?.cancel();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      if (resendTimer > 0) {
        setState(() {
          resendTimer--;
        });
      } else {
        setState(() {
          isResendActive = true;
        });
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _controller.dispose();
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void navigateToNextPage() {
    if (Validators.validateOTP(
      context,
      _controller.text.trim(),
    )) {
      tryWrapperAsync(
        () async {
          final authBloc = context.read<AuthBloc>();

          if (_verificationId != null) {
            authBloc.add(
              VerifyOTP(
                verificationId: _verificationId!,
                smsCode: _controller.text.trim(),
                context: context,
              ),
            );
          }
        },
        context,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: authAppBar(),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is PhoneVerified) {
            // final fullName = context.read<RegistrationCubit>().state.fullName;
            // if (fullName == null || fullName.trim().isEmpty) {
            RouteUtils.pushNamed(
              context,
              RoutePaths.fullNamePage,
            );
            // } else {
            //   RouteUtils.pushNamed(
            //     context,
            //     RoutePaths.agePage,
            //   );
            // }
          }
        },
        child: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: buildScrollableWithFade(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: SizeUtils.horizontalPadding.copyWith(top: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Enter your\nverification code',
                            style: AppTextStyles.heading1,
                          ),
                          const SizedBox(
                            height: 40,
                          ),
                          Pinput(
                            onClipboardFound: (value) {
                              setState(() {
                                _controller.text = value;
                              });
                            },
                            length: 6,
                            controller: _controller,
                            defaultPinTheme: PinTheme(
                              width: 80,
                              textStyle: const TextStyle(fontSize: 25),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 20,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(12),
                                ),
                                color: darkAppColors.lightColor.withAlpha(26),
                                boxShadow: AppShadowStyles.textFeildShadow,
                              ),
                            ),
                            validator: (value) {
                              return _controller.text.trim().isEmpty
                                  ? 'Please enter a valid code'
                                  : null;
                            },
                            onChanged: (value) {
                              setState(() {
                                isValid = _controller.text.trim().length == 6;
                              });
                            },
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text("Didn't get it?"),
                              TextButton(
                                onPressed:
                                    isResendActive ? _startResendTimer : null,
                                child: Text(
                                  isResendActive
                                      ? 'Tap to resend'
                                      : 'Resend in $resendTimer seconds',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: isResendActive
                                        ? darkAppColors.primary
                                        : Colors.grey,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(
                            height: MediaQuery.of(context).size.height * 0.15,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  if (state is AuthLoading) {
                    return const LoadingWidget();
                  }
                  return Padding(
                    padding: SizeUtils.horizontalPadding.copyWith(
                      bottom: 40,
                      top: 20,
                    ),
                    child: authButton(
                      title: 'Verify me',
                      onTap: navigateToNextPage,
                      enabled: isValid,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
      resizeToAvoidBottomInset: true,
    );
  }
}
