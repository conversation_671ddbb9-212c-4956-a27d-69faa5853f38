import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/auth/app_bar.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';
import 'package:vibeo/widgets/auth/gender_widget.dart';

class GenderPage extends StatefulWidget {
  const GenderPage({super.key});

  @override
  State<GenderPage> createState() => _GenderPageState();
}

class _GenderPageState extends State<GenderPage> {
  late Gender selected;

  @override
  void initState() {
    selected = Gender.man;
    super.initState();
  }

  void navigateToNextPage() {
    context.read<RegistrationCubit>().updateGender(
          selected.name,
        );
    RouteUtils.pushNamed(context, RoutePaths.genrePage);
  }

  void navigateToNextPageWithoutGender() {
    context.read<RegistrationCubit>().updateGender(
          'null',
        );
    RouteUtils.pushNamed(context, RoutePaths.genrePage);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: authAppBar(),
      body: SafeArea(
        child: Padding(
          padding: SizeUtils.horizontalPadding.copyWith(top: 20, bottom: 40),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      alignment: Alignment.centerLeft,
                      child: Text(
                        "What's your gender?",
                        style: AppTextStyles.heading1,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                'Helps tailor your experience.',
                style: AppTextStyles.titleLight,
              ),
              const SizedBox(
                height: 40,
              ),
              GenderWidget(
                onclick: () {
                  setState(() {
                    selected = Gender.man;
                  });
                },
                isSelected: Gender.man == selected,
                title: 'Man',
                icon: Icons.male,
              ),
              GenderWidget(
                isSelected: Gender.woman == selected,
                onclick: () {
                  setState(() {
                    selected = Gender.woman;
                  });
                },
                title: 'Woman',
                icon: Icons.female,
              ),
              GenderWidget(
                isSelected: Gender.nonbinary == selected,
                onclick: () {
                  setState(() {
                    selected = Gender.nonbinary;
                  });
                },
                title: 'Non-Binary',
                icon: Icons.transgender,
              ),
              GenderWidget(
                isSelected: Gender.other == selected,
                onclick: () {
                  setState(() {
                    selected = Gender.other;
                  });
                },
                title: 'Other',
                icon: Icons.transgender,
              ),
              Align(
                alignment: Alignment.center,
                child: TextButton(
                  onPressed: navigateToNextPageWithoutGender,
                  child: const Text(
                    "I'd prefer not to say",
                    style: TextStyle(
                      fontSize: 20,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const Spacer(),
              authButton(
                title: 'Final Step',
                onTap: navigateToNextPage,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
