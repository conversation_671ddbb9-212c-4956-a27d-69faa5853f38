import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/data/genres.dart';

import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/exceptions/try_wrapper.dart';
import 'package:vibeo/utils/location/location_permission.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/utils/validations.dart';
import 'package:vibeo/widgets/auth/app_bar.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';
import 'package:vibeo/widgets/auth/genre_choice_chip.dart';

class GenrePage extends StatefulWidget {
  const GenrePage({super.key});

  @override
  State<GenrePage> createState() => _GenrePageState();
}

class _GenrePageState extends State<GenrePage> {
  bool isValid = false;
  final Set<String> selectedGenres = {};

  void registerUser() {
    tryWrapperAsync(
      () async {
        if (Validators.validateGenresList(context, selectedGenres)) {
          context.read<RegistrationCubit>().updateGenres(
                selectedGenres.toList(),
              );
          final location = await getCurrentPosition();
          if (location != null) {
            if (mounted) {
              context.read<RegistrationCubit>().updateLocation(
                    location.latitude,
                    location.longitude,
                  );
            }
          }
          if (mounted) {
            final registrationCubit = context.read<RegistrationCubit>().state;
            context.read<UserBloc>().add(
                  RegisterUser(
                    registrationData: registrationCubit,
                  ),
                );

            await Future.delayed(Duration.zero);
          }
        }
      },
      context,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: authAppBar(),
      body: SafeArea(
        child: Padding(
          padding: SizeUtils.horizontalPadding.copyWith(top: 20, bottom: 40),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Let's choose your",
                style: AppTextStyles.heading1,
              ),
              ShaderMask(
                blendMode: BlendMode.srcIn,
                shaderCallback: (bounds) => LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    darkAppColors.primary,
                    darkAppColors.lightColor,
                  ],
                ).createShader(
                  Rect.fromLTWH(
                    0,
                    0,
                    bounds.width,
                    bounds.height,
                  ),
                ),
                child: Text(
                  'Music Genres',
                  style: AppTextStyles.heading1,
                ),
              ),
              const SizedBox(
                height: 5,
              ),
              Text(
                'This will help us create the best experience for you!',
                style: AppTextStyles.bodySmall,
              ),
              Expanded(
                child: buildScrollableWithFade(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                      vertical: 32,
                    ),
                    child: Wrap(
                      spacing: 10,
                      runSpacing: 12,
                      alignment: WrapAlignment.center,
                      children: genres
                          .map(
                            (interest) => buildGenreChip(
                              interest,
                              () {
                                setState(() {
                                  if (selectedGenres.contains(interest)) {
                                    selectedGenres.remove(interest);
                                  } else {
                                    selectedGenres.add(interest);
                                  }
                                  isValid = selectedGenres.length >= 3;
                                });
                              },
                              isSelected: selectedGenres.contains(interest),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                ),
              ),
              Center(
                child: Text(
                  selectedGenres.isEmpty
                      ? 'Select at least 3'
                      : selectedGenres.length <= 3
                          ? '${selectedGenres.length} Selected out of 3'
                          : '${selectedGenres.length} Selected',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white60,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              BlocConsumer<UserBloc, UserState>(
                listener: (context, state) {
                  if (state is UserExists) {
                    RouteUtils.pushNamedAndRemoveUntil(
                      context,
                      RoutePaths.home,
                      (route) => route.isFirst,
                    );
                  }
                },
                builder: (context, state) {
                  if (state is UserRegistering) {
                    return const Center(
                      child: LoadingWidget(),
                    );
                  }
                  return authButton(
                    title: "Let's Vibeooo",
                    onTap: registerUser,
                    enabled: isValid,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
