import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/loading_widget.dart';

import 'package:vibeo/utils/exceptions/try_wrapper.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/views/auth/onboard_slider_page.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/views/auth/background_video.dart';
import 'package:vibeo/widgets/auth/apple_sign_in_button.dart';
import 'package:vibeo/widgets/auth/dialogs/generalinfo_dialog.dart';
import 'package:vibeo/widgets/auth/google_sign_in_button.dart';

class OnboardPage extends StatefulWidget {
  const OnboardPage({super.key});

  @override
  State<OnboardPage> createState() => _OnboardPageState();
}

class _OnboardPageState extends State<OnboardPage> {
  void signInWithGoogle() {
    tryWrapperAsync(
      () async {
        context.read<AuthBloc>().add(SignInWithGoogle(context));
        return;
      },
      context,
    );
  }

  void signInWithApple() {
    tryWrapperAsync(
      () async {
        context.read<AuthBloc>().add(SignInWithApple(context));
        return;
      },
      context,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        fit: StackFit.expand,
        children: [
          const BackgroundVideo(),
          Column(
            children: [
              const Expanded(child: OnboardingSlider()),
              BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  if (state is AuthLoading) {
                    return const SizedBox(
                      height: 120,
                      child: LoadingWidget(),
                    );
                  }
                  return Padding(
                    padding: SizeUtils.horizontalPadding,
                    child: Column(
                      children: [
                        buildGoogleSigninButton(
                          'Continue with Google',
                          onPressed: signInWithGoogle,
                        ),
                        const SizedBox(height: 10),
                        buildAppleSigninButton(
                          'Continue with Apple',
                          onPressed: signInWithApple,
                        ),
                      ],
                    ),
                  );
                },
              ),
              termsAndConditions(context),
            ],
          ),
        ],
      ),
    );
  }
}

Widget termsAndConditions(BuildContext context) {
  return Padding(
    padding: const EdgeInsets.only(
      left: 12,
      right: 12,
      bottom: 30,
      top: 24,
    ),
    child: Column(
      children: [
        Text(
          'By signing in, you agree to our',
          textAlign: TextAlign.center,
          style: AppTextStyles.bodySmaller,
        ),
        InkWell(
          onTap: () {
            GeneralInfoDialog.show(context, 'assets/files/terms.md');
          },
          child: Text(
            'Terms and Conditions',
            style: AppTextStyles.bodysmallBold,
          ),
        ),
      ],
    ),
  );
}
