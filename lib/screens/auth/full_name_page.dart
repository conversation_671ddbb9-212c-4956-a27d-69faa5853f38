import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/utils/validations.dart';
import 'package:vibeo/widgets/auth/app_bar.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';

class FullNamePage extends StatefulWidget {
  const FullNamePage({super.key});

  @override
  State<FullNamePage> createState() => _FullNamePageState();
}

class _FullNamePageState extends State<FullNamePage> {
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  bool isValid = false;

  @override
  void initState() {
    _firstNameController = TextEditingController();
    _lastNameController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    super.dispose();
  }

  void navigateToNextPage() {
    if (Validators.validateFirstName(
      context,
      _firstNameController.text.trim(),
    )) {
      final fullName = '${_firstNameController.text.trim()}'
          '${_lastNameController.text.trim().isNotEmpty ? ' ${_lastNameController.text.trim()}' : ''}';

      context.read<RegistrationCubit>().updateFullName(
            fullName.trim(),
          );
      RouteUtils.pushNamed(context, RoutePaths.agePage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: authAppBar(),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: buildScrollableWithFade(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: SizeUtils.horizontalPadding.copyWith(top: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "What's your name?",
                          style: AppTextStyles.heading1,
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Text(
                          'Your identity stays anonymous!',
                          style: AppTextStyles.titleLight,
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        nameTextField(
                          controller: _firstNameController,
                          hintText: 'First Name *',
                          onChanged: (val) {
                            setState(() {
                              isValid =
                                  _firstNameController.text.trim().length >= 3;
                            });
                          },
                        ),
                        nameTextField(
                          controller: _lastNameController,
                          hintText: 'Last Name',
                          onChanged: (val) {},
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.15,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: SizeUtils.horizontalPadding.copyWith(
                bottom: 40,
                top: 20,
              ),
              child: authButton(
                title: 'Continue',
                onTap: navigateToNextPage,
                enabled: isValid,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

Padding nameTextField({
  required String hintText,
  required TextEditingController controller,
  required Function onChanged,
}) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 10),
    child: TextField(
      controller: controller,
      style: const TextStyle(
        fontWeight: FontWeight.bold,
        color: Colors.white,
        fontSize: 24,
      ),
      keyboardType: TextInputType.name,
      cursorColor: Colors.white,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        hintText: hintText,
        hintStyle: const TextStyle(
          fontSize: 24,
          color: Colors.white38,
        ),
        border: InputBorder.none,
        disabledBorder: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: const UnderlineInputBorder(
          borderSide: BorderSide(
            color: Colors.white,
            width: 2,
          ),
        ),
      ),
      onChanged: (val) => onChanged(val),
    ),
  );
}
