import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:vibeo/logic/exception/exception_bloc.dart';
import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/utils/validations.dart';
import 'package:vibeo/widgets/auth/auth_button.dart';
import 'package:vibeo/widgets/auth/custom_checkbox.dart';

class PhoneVerificationPage extends StatefulWidget {
  const PhoneVerificationPage({super.key});

  @override
  State<PhoneVerificationPage> createState() => _PhoneVerificationPageState();
}

class _PhoneVerificationPageState extends State<PhoneVerificationPage> {
  late TextEditingController _phonenumberController;
  bool isValid = false;
  bool optSms = true;

  @override
  void initState() {
    _phonenumberController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _phonenumberController.dispose();
    super.dispose();
  }

  void navigateToNextPage(BuildContext context) {
    if (Validators.validatePhoneNumber(
      context,
      _phonenumberController.text.trim(),
    )) {
      final phoneNumber = _phonenumberController.text.trim();
      final formattedNumber =
          '+1${phoneNumber.replaceAll(RegExp(r'[^\d]'), '')}';
      context.read<AuthBloc>().add(
            PhoneSignIn(
              formattedNumber,
              context,
            ),
          );
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: buildScrollableWithFade(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: SizeUtils.horizontalPadding.copyWith(
                      top: 80,
                      bottom: MediaQuery.of(context).size.height * 0.15,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome 👋',
                          style: AppTextStyles.heading1,
                        ),
                        const SizedBox(
                          height: 5,
                        ),
                        Text(
                          "Let's get to know you better!",
                          style: AppTextStyles.titleLight,
                        ),
                        const SizedBox(height: 40),
                        Text(
                          'Phone number',
                          style: AppTextStyles.heading4.copyWith(
                            color: Colors.white70,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          height: 56,
                          decoration: BoxDecoration(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(28)),
                            color: darkAppColors.lightColor.withAlpha(26),
                            boxShadow: AppShadowStyles.textFeildShadow,
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8),
                                child: Row(
                                  children: [
                                    const Image(
                                      image: AssetImage(
                                        'assets/icons/us_flag.png',
                                      ),
                                      width: 24,
                                      height: 16,
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      '+1',
                                      style: AppTextStyles.heading4,
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: TextField(
                                  controller: _phonenumberController,
                                  style: const TextStyle(color: Colors.white),
                                  keyboardType: TextInputType.phone,
                                  cursorColor: Colors.white,
                                  decoration: InputDecoration(
                                    filled: false,
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    focusedBorder: InputBorder.none,
                                    hintText: '2015550123',
                                    hintStyle: AppTextStyles.heading4.copyWith(
                                      color: Colors.white38,
                                    ),
                                  ),
                                  onChanged: (val) {
                                    if (_phonenumberController.text
                                            .trim()
                                            .length ==
                                        10) {
                                      setState(() {
                                        isValid = true;
                                      });
                                    } else {
                                      setState(() {
                                        isValid = false;
                                      });
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // Bottom section that slides up
            Padding(
              padding: SizeUtils.horizontalPadding.copyWith(bottom: 40),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomCheckbox(
                        value: optSms,
                        onChanged: (val) {
                          setState(() {
                            optSms = val;
                          });
                        },
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: Text(
                          'Opt in for SMS and email updates to get the best vibes around the city.',
                          style: AppTextStyles.bodySmall,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  BlocConsumer<AuthBloc, AuthState>(
                    listener: (context, state) {
                      if (state is PhoneVerificationSent) {
                        context.read<RegistrationCubit>().updatePhoneNumber(
                              '+1${_phonenumberController.text.trim()}',
                            );
                        context.read<RegistrationCubit>().updateExtras(
                              '{"sms": $optSms}',
                            );
                        if (context.mounted) {
                          RouteUtils.pushNamed(context, RoutePaths.otpPage);
                        }
                      }
                    },
                    builder: (context, state) {
                      return BlocBuilder<GlobalErrorBloc, GlobalErrorState>(
                        builder: (context, errorState) {
                          if (errorState is GlobalErrorOccurred &&
                              state is! PhoneVerificationSent) {
                            return authButton(
                              title: 'Send code',
                              onTap: () => navigateToNextPage(context),
                              enabled: isValid,
                            );
                          }
                          if (state is AuthLoading) {
                            return const LoadingWidget();
                          }
                          return authButton(
                            title: 'Send code',
                            onTap: () => navigateToNextPage(context),
                            enabled: isValid,
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
