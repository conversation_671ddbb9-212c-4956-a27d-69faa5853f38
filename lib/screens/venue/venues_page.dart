import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/cubits/filter/filter_cubit.dart';
import 'package:vibeo/helper/get_current_location.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/views/shimmer/venue_shimmer_tile.dart';
import 'package:vibeo/views/state/not_found_screen.dart';
import 'package:vibeo/views/state/something_went_wrong_screen.dart';
import 'package:vibeo/widgets/venue/venue_tile.dart';

class VenuesPage extends StatefulWidget {
  const VenuesPage({super.key});

  @override
  State<VenuesPage> createState() => _VenuesPageState();
}

class _VenuesPageState extends State<VenuesPage> {
  bool _isFetchingMore = false;
  Location? currentLocation;
  late FilterCubit _filterCubit;

  bool onNotification(ScrollNotification scrollInfo) {
    if (scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 200 &&
        !_isFetchingMore) {
      _isFetchingMore = true;
      final state = context.read<VenueBloc>().state;
      if (state.venues.isNotEmpty &&
          !state.isLoadingMore &&
          state.venues.length < state.totalVenues) {
        final Location? location =
            context.read<UserBloc>().state.user!.location;

        if (location != null) {
          context.read<VenueBloc>().add(
                FetchVenuesEvent(
                  skip: state.venues.length,
                  location: currentLocation ?? location,
                ),
              );
        } else {
          context.read<VenueBloc>().add(
                FetchVenuesEvent(
                  skip: state.venues.length,
                  location: currentLocation,
                ),
              );
        }
      }
      Future.delayed(const Duration(seconds: 1), () {
        _isFetchingMore = false;
      });
    }
    return true;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _filterCubit = context.read<FilterCubit>();
  }

  @override
  void initState() {
    super.initState();
    _initializeVenues();
  }

  @override
  void dispose() {
    _filterCubit.clearFilters();
    super.dispose();
  }

  Future<void> onRefresh() async {
    final location = context.read<UserBloc>().state.user!.location;
    final currentLocation = await getCurrentLocation();
    if (mounted) {
      context.read<VenueBloc>().add(
            RefreshVenuesEvent(
              location: currentLocation ?? location,
            ),
          );
    }

    return Future.delayed(Durations.medium2);
  }

  Future<void> _initializeVenues() async {
    final venueState = context.read<VenueBloc>().state;
    if (venueState.venues.isEmpty ||
        venueState.venues.length <= 5 ||
        venueState is VenueInitial) {
      final location = context.read<UserBloc>().state.user!.location;
      currentLocation = await getCurrentLocation();

      if (mounted) {
        context.read<VenueBloc>().add(
              FetchVenuesEvent(
                location: currentLocation ?? location,
              ),
            );
      }
    }
  }

  List<VenueModel> _filterVenues(List<VenueModel> venues, String? filter) {
    if (filter == null) return venues;

    switch (filter) {
      case 'Trending':
        return venues.where((venue) => venue.exclusive == true).toList();
      case 'Offers':
        return venues
            .where((venue) => venue.perksType.contains(PerkVenueType.offers))
            .toList();
      case 'Happy Hours':
        return venues
            .where(
              (venue) =>
                  venue.perksType.contains(PerkVenueType.exclusiveOffers),
            )
            .toList();
      case 'Live':
        return venues.where((venue) => venue.isLive == true).toList();
      case 'Events':
        return venues
            .where((venue) => venue.perksType.contains(PerkVenueType.events))
            .toList();
      case 'Get Entry':
        return venues
            .where((venue) => venue.perksType.contains(PerkVenueType.promoters))
            .toList();
      case 'West Loop':
        return venues.where((venue) => venue.area == 'West Loop').toList();
      case 'Wrigleyville':
        return venues.where((venue) => venue.area == 'Wrigleyville').toList();
      case 'River North':
        return venues.where((venue) => venue.area == 'River North').toList();
      case 'Live Music':
        return venues
            .where((venue) => venue.tags.contains('Live Music') == true)
            .toList();
      default:
        return venues;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<VenueBloc, VenueState>(
        builder: (context, state) {
          if (state is VenueError) {
            return Padding(
              padding: const EdgeInsets.only(top: 70),
              child: SomethingWentWrong(
                onRetry: onRefresh,
              ),
            );
          }
          if (state.venues.isEmpty && state is VenueLoading) {
            return ListView.builder(
              padding: SizeUtils.horizontalPadding.copyWith(
                top: 16,
                bottom: 20,
              ),
              itemCount: 10,
              itemBuilder: (ctx, index) => const VenueTileShimmer(),
            );
          }

          if (!state.venues.isNotEmpty) {
            return const SizedBox();
          }

          return BlocBuilder<FilterCubit, FilterState>(
            buildWhen: (previous, current) => previous != current,
            builder: (context, filterState) {
              final filteredVenues =
                  _filterVenues(state.venues, filterState.selectedFilter);

              if (filteredVenues.isEmpty &&
                  filterState.selectedFilter != null) {
                return Center(
                  child: NoResultsFoundScreen(
                    title: 'Oops! Nothing found',
                    icon: CupertinoIcons.exclamationmark,
                    description:
                        'No venues match the "${filterState.selectedFilter}" filter.',
                  ),
                );
              }

              return NotificationListener<ScrollNotification>(
                onNotification: onNotification,
                child: RefreshIndicator.adaptive(
                  onRefresh: onRefresh,
                  color: Colors.white,
                  elevation: 10,
                  edgeOffset: 10,
                  displacement: 40,
                  child: ListView.builder(
                    padding: SizeUtils.horizontalPadding.copyWith(
                      top: 32,
                      bottom: 20,
                    ),
                    itemCount: filteredVenues.length +
                        ((filterState.selectedFilter == null &&
                                state.venues.length != state.totalVenues)
                            ? 1
                            : 0),
                    itemBuilder: (context, index) {
                      if (index >= filteredVenues.length - 1 &&
                          filteredVenues.length < 5) {
                        if (!state.isLoadingMore &&
                            state.venues.length < state.totalVenues &&
                            !_isFetchingMore) {
                          final location =
                              context.read<UserBloc>().state.user!.location;
                          context.read<VenueBloc>().add(
                                FetchVenuesEvent(
                                  skip: state.venues.length,
                                  location: currentLocation ?? location,
                                  filter: filterState.selectedFilter,
                                ),
                              );
                        }
                      }

                      if (index >= filteredVenues.length) {
                        return const VenueTileShimmer();
                      }
                      return VenueTile(
                        index: index,
                        venue: filteredVenues[index],
                      );
                    },
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
