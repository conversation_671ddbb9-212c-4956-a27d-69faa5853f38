import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/controller/venue_controller.dart';
import 'package:vibeo/models/venue/venue_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/routes/route_utils.dart';
import 'package:vibeo/screens/home/<USER>';
import 'package:vibeo/screens/venue/perks_page.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/views/venue/venue_desc_vibes.dart';
import 'package:vibeo/widgets/dialogs/location_dialog.dart';
import 'package:vibeo/widgets/global/tag_widget.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:vibeo/widgets/text/gradient_text.dart';
import 'package:vibeo/widgets/venue/rating_bottom_sheet.dart';

class VenueDescPage extends StatefulWidget {
  final RouteSettings settings;
  const VenueDescPage({required this.settings, super.key});

  @override
  State<VenueDescPage> createState() => _VenueDescPageState();
}

class _VenueDescPageState extends State<VenueDescPage>
    with TickerProviderStateMixin {
  late VenueModel venue;
  final PageController _pageController = PageController();
  late TabController _tabController;
  int _currentPage = 0;
  late VenueController _venueController;
  bool _isSaved = false;

  bool isFromNotification = false;
  bool showOffersDirectly = false;

  late AnimationController _controller;

  // Animation status
  bool _isAnimating = false;

  // Store the starting position of the product
  Offset? _startPosition;

  // Store the target position (cart position)
  Offset? _endPosition;

  // The widget that will fly to the cart
  Widget? _flyingWidget;

  // Cart item count
  int _cartItemCount = 0;

  // Key to get cart position
  final GlobalKey _cartKey = GlobalKey();

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    if (venue.perksType.isNotEmpty) {
      _controller.dispose();
    }

    super.dispose();
  }

  @override
  void initState() {
    final userID = context.read<UserBloc>().state.user!.uid;
    _venueController = VenueController(userID);
    final data = widget.settings.arguments! as Map<String, dynamic>;
    venue = data['venue'] as VenueModel;
    isFromNotification = (data['isFromNotification'] as bool?) ?? false;
    showOffersDirectly = (data['showOffersDirectly'] as bool?) ?? false;

    _tabController = TabController(length: 3, vsync: this);

    if (showOffersDirectly) {
      _tabController.index = 1;
    }

    _checkSavedStatus();
    if (venue.perksType.isNotEmpty) {
      _controller = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );

      _controller.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          setState(() {
            _isAnimating = false;
            _cartItemCount++;
          });
        }
      });
    }

    super.initState();
  }

  void _runAddToCartAnimation(
    BuildContext context,
    Widget productWidget,
    Offset productPosition,
  ) {
    // Get the cart position
    final RenderBox? cartBox =
        _cartKey.currentContext?.findRenderObject() as RenderBox?;
    if (cartBox == null) return;

    final Offset cartPosition = cartBox.localToGlobal(Offset.zero);

    // Center of the cart icon
    final double cartCenterX = cartPosition.dx;
    final double cartCenterY = cartPosition.dy;

    setState(() {
      _startPosition = productPosition;
      _endPosition = Offset(cartCenterX, cartCenterY);
      _flyingWidget = productWidget;
      _isAnimating = true;
      _controller
        ..reset()
        ..forward();
    });
  }

  Future<void> _checkSavedStatus() async {
    final saved = await _venueController.checkIfVenueSaved(venue.id);
    setState(() {
      _isSaved = saved;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) => [
              SliverAppBar(
                expandedHeight: SizeUtils.screenHeight * 0.25,
                pinned: true,
                floating: true,

                automaticallyImplyLeading: false,
                backgroundColor: darkAppColors.backgroundColor,
                scrolledUnderElevation: 0,
                forceElevated: false,
                forceMaterialTransparency: false,
                leadingWidth: 60, // Increased to accommodate margin
                centerTitle: false,
                title: Row(
                  children: [
                    Container(
                      height: 50,
                      width: 50,
                      constraints: const BoxConstraints(
                        maxHeight: 50,
                        maxWidth: 50,
                      ),
                      child: GestureDetector(
                        onTap: () => RouteUtils.pop(context),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: const BoxDecoration(
                            color: Colors.black87,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            CupertinoIcons.chevron_back,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ),
                    if (innerBoxIsScrolled)
                      FittedBox(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: Row(
                            children: [
                              RichText(
                                text: TextSpan(
                                  children: [
                                    WidgetSpan(
                                      child: Container(
                                        constraints: BoxConstraints(
                                          maxWidth: SizeUtils.screenWidth * 0.6,
                                        ),
                                        child: Text(venue.name),
                                      ),
                                      style: const TextStyle(
                                        overflow: TextOverflow.ellipsis,
                                        fontFamily: 'PulpDisplay',
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (venue.exclusive)
                                      WidgetSpan(
                                        alignment: PlaceholderAlignment.bottom,
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                            left: 6,
                                          ),
                                          child: Image.asset(
                                            'assets/venue/badge.png',
                                            height: 20,
                                            width: 20,
                                            cacheHeight: 40,
                                            cacheWidth: 40,
                                            fit: BoxFit.contain,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    const Spacer(),
                    Row(
                      children: [
                        if (venue.perksType.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(
                              right: 8,
                            ),
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                Container(
                                  decoration: const BoxDecoration(
                                    color: Colors.black87,
                                    shape: BoxShape.circle,
                                  ),
                                  child: IconButton(
                                    key: _cartKey,
                                    icon:
                                        const Icon(Icons.shopping_bag_rounded),
                                    color: Colors.white,
                                    onPressed: () {
                                      Navigator.of(context).pushAndRemoveUntil(
                                        MaterialPageRoute(
                                          builder: (context) {
                                            return const HomePage(
                                              initialIndex: 2,
                                            );
                                          },
                                        ),
                                        (route) => false,
                                      );
                                    },
                                  ),
                                ),
                                if (_cartItemCount > 0)
                                  Positioned(
                                    right: 6,
                                    top: 6,
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        color: darkAppColors.deepPurple,
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      constraints: const BoxConstraints(
                                        minWidth: 16,
                                        minHeight: 16,
                                      ),
                                      child: Text(
                                        '$_cartItemCount',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        GestureDetector(
                          onTap: () async {
                            final saved =
                                await _venueController.onVenueSaved(venue.id);
                            setState(() {
                              _isSaved = saved;
                            });
                          },
                          child: Container(
                            height: 50,
                            width: 50,
                            constraints: const BoxConstraints(
                              maxHeight: 50,
                              maxWidth: 50,
                            ),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: const BoxDecoration(
                                color: Colors.black87,
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                _isSaved
                                    ? CupertinoIcons.heart_fill
                                    : CupertinoIcons.heart,
                                color: _isSaved ? Colors.pink : Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    children: [
                      // Image Carousel
                      PageView.builder(
                        controller: _pageController,
                        onPageChanged: (index) {
                          setState(() {
                            _currentPage = index;
                          });
                        },
                        itemCount: venue.thumbnails.length,
                        itemBuilder: (context, index) {
                          return CachedNetworkImage(
                            imageUrl: venue.thumbnails[index],
                            width: double.infinity,
                            fit: BoxFit.cover,
                            imageBuilder: (context, imageProvider) => Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: imageProvider,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      // Offers Badge
                      if (venue.perksType.isNotEmpty)
                        Positioned(
                          bottom: MediaQuery.of(context).padding.bottom + 20,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  darkAppColors.deepPurple,
                                  darkAppColors.purpleShade,
                                ],
                              ),
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(20),
                                bottomLeft: Radius.circular(20),
                              ),
                              boxShadow: AppShadowStyles.baseStyle,
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  CupertinoIcons.tag_fill,
                                  color: Colors.white,
                                  size: 14,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'SPECIAL OFFERS',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                      // Image Indicator
                      Positioned(
                        bottom: 16,
                        left: 0,
                        right: 0,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.black.withAlpha(76),
                                borderRadius: const BorderRadius.all(
                                  Radius.circular(20),
                                ),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  ...List.generate(
                                    venue.thumbnails.length,
                                    (index) => Container(
                                      width: 8,
                                      height: 8,
                                      margin: const EdgeInsets.symmetric(
                                        horizontal: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: _currentPage == index
                                            ? Colors.white
                                            : Colors.white.withAlpha(128),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: SizeUtils.horizontalPadding.copyWith(
                        top: 20,
                        bottom: 20,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Text(
                          //   venue.name,
                          //   textAlign: TextAlign.center,
                          //   style: const TextStyle(
                          //     color: Colors.white,
                          //     fontSize: 28,
                          //     fontWeight: FontWeight.bold,
                          //   ),
                          // ),
                          RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: venue.name,
                                  style: const TextStyle(
                                    fontFamily: 'PulpDisplay',
                                    color: Colors.white,
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                if (venue.exclusive)
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment
                                        .bottom, // Align with the middle of text
                                    child: Padding(
                                      padding: const EdgeInsets.only(
                                        left: 6,
                                      ), // Small space between text and badge
                                      child: Image.asset(
                                        'assets/venue/badge.png',
                                        height: 24,
                                        width: 24,
                                        cacheHeight: 40,
                                        cacheWidth: 40,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (venue.milesAway != null)
                                Text(
                                  '${venue.milesAway} miles .',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.white70,
                                  ),
                                ),
                              const SizedBox(
                                width: 10,
                              ),
                              Text(
                                '${venue.area} .',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: Colors.white70,
                                ),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              const Text(
                                'Closes late',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),

                          // const SizedBox(height: 15),

                          // Description
                          // Text(
                          //   venue.description!,
                          //   style: TextStyle(
                          //     color: Colors.white.withAlpha(204),
                          //     fontSize: 15,
                          //   ),
                          // ),
                          const SizedBox(
                            height: 20,
                          ),
                          Column(
                            children: [
                              Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                crossAxisAlignment: WrapCrossAlignment.center,
                                alignment: WrapAlignment.center,
                                children: venue.tags
                                    .map(
                                      tagWidget,
                                    )
                                    .toList(),
                              ),
                              const SizedBox(height: 20),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  _ActionButton(
                                    icon: CupertinoIcons.car_fill,
                                    onTap: () {
                                      showLocBottomSheet(
                                        venue.id,
                                        '${venue.name}, ${venue.address}',
                                        context,
                                      );
                                    },
                                  ),
                                  if (venue.phoneNumber != '')
                                    _ActionButton(
                                      icon: CupertinoIcons.phone_fill,
                                      onTap: () async {
                                        await showCupertinoModalPopup(
                                          context: context,
                                          builder: (context) =>
                                              CupertinoActionSheet(
                                            title:
                                                const Text('Contact Options'),
                                            message: Text(venue.phoneNumber),
                                            actions: [
                                              CupertinoActionSheetAction(
                                                onPressed: () async {
                                                  RouteUtils.pop(context);
                                                  final Uri phoneUri = Uri(
                                                    scheme: 'tel',
                                                    path: venue.phoneNumber,
                                                  );
                                                  if (await canLaunchUrl(
                                                    phoneUri,
                                                  )) {
                                                    await launchUrl(phoneUri);
                                                  }
                                                },
                                                child: Text(
                                                  'Call',
                                                  style: AppTextStyles.heading4,
                                                ),
                                              ),
                                              CupertinoActionSheetAction(
                                                onPressed: () async {
                                                  RouteUtils.pop(context);
                                                  final Uri smsUri = Uri(
                                                    scheme: 'sms',
                                                    path: venue.phoneNumber,
                                                  );
                                                  if (await canLaunchUrl(
                                                    smsUri,
                                                  )) {
                                                    await launchUrl(smsUri);
                                                  }
                                                },
                                                child: Text(
                                                  'Message',
                                                  style: AppTextStyles.heading4,
                                                ),
                                              ),
                                            ],
                                            cancelButton:
                                                CupertinoActionSheetAction(
                                              isDestructiveAction: true,
                                              onPressed: () =>
                                                  RouteUtils.pop(context),
                                              child: const Text('Cancel'),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  _ActionButton(
                                    icon: CupertinoIcons.star_fill,
                                    onTap: () {
                                      showRatingBottomSheet(context, venue.id);
                                    },
                                    size: 25,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SliverPersistentHeader(
                pinned: true,
                delegate: _SliverAppBarDelegate(
                  TabBar(
                    controller: _tabController,
                    indicatorColor: Colors.white,
                    dividerHeight: 0.1,
                    dividerColor: Colors.grey.withAlpha(128),
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.grey,
                    enableFeedback: true,
                    indicatorWeight: 3,
                    splashFactory: NoSplash.splashFactory,
                    overlayColor: WidgetStateColor.transparent,
                    tabs: [
                      const Tab(text: 'Vibes'),
                      if (venue.exclusive == true)
                        const GradientText(
                          'Offers',
                          gradient: LinearGradient(
                            colors: [
                              Color.fromARGB(255, 175, 129, 255),
                              Color.fromARGB(255, 255, 134, 241),
                            ],
                          ),
                        )
                      else
                        const Tab(text: 'Offers'),
                      const Tab(text: 'Info'),
                    ],
                  ),
                ),
              ),
            ],
            body: TabBarView(
              controller: _tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Feeds Tab
                VenueDescVibes(
                  venueID: venue.id,
                ),

                // Offers Tab
                PerksPage(
                  venueID: venue.id,
                  onAddToCart: (productWidget, productPosition) {
                    _runAddToCartAnimation(
                      context,
                      productWidget,
                      productPosition,
                    );
                  },
                ),

                // Info Tab
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Description',
                        style: AppTextStyles.headingTitle,
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        venue.description,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      // Add more info widgets
                    ],
                  ),
                ),
              ],
            ),
          ),
          if (_isAnimating &&
              _startPosition != null &&
              _endPosition != null &&
              _flyingWidget != null)
            AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                // Calculate current position with a curve
                final curvedValue =
                    Curves.easeInOut.transform(_controller.value);

                // Add a slight arc to the path
                final double arcX = _startPosition!.dx +
                    (_endPosition!.dx - _startPosition!.dx) * curvedValue;
                final double arcY = _startPosition!.dy +
                    (_endPosition!.dy - _startPosition!.dy) * curvedValue -
                    sin(curvedValue * 3.14) * 100; // Arc height

                // Scale down as it approaches the cart
                final scale = 1.0 - (curvedValue * 0.4);

                // Rotate slightly during flight
                final rotate = curvedValue * 0.5;

                return Positioned(
                  left: arcX - 20 * scale, // Adjust based on your widget size
                  top: arcY - 20 * scale, // Adjust based on your widget size
                  child: Transform.scale(
                    scale: scale,
                    child: Transform.rotate(
                      angle: rotate,
                      child: Opacity(
                        opacity: 1.0 - (curvedValue * 0.3),
                        child: ClipRRect(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                          child: SizedBox(
                            width: 40,
                            height: 40,
                            child: _flyingWidget,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
        ],
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback onTap;
  final double size;

  const _ActionButton({
    required this.icon,
    required this.onTap,
    this.size = 26,
  });

  @override
  Widget build(BuildContext context) {
    return HapticButton(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(15),
        margin: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.black.withAlpha(76),
          border: Border.all(
            color: Colors.white.withAlpha(51),
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.white.withAlpha(26),
              blurRadius: 15,
              spreadRadius: -5,
            ),
          ],
          backgroundBlendMode: BlendMode.overlay,
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 26,
        ),
      ),
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;
  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return ColoredBox(
      color: darkAppColors.backgroundColor,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}
