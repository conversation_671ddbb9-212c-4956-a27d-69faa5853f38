import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/logic/cubit/perks_cubit_cubit.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';

import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/promoters/promoter_model.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/views/venue/offers_screen.dart';
import 'package:vibeo/views/venue/promoters_screen.dart';

class PerksPage extends StatefulWidget {
  final String venueID;
  final void Function(Widget productWidget, Offset position)? onAddToCart;

  const PerksPage({
    required this.venueID,
    this.onAddToCart,
    super.key,
  });

  @override
  State<PerksPage> createState() => _PerksPageState();
}

class _PerksPageState extends State<PerksPage> {
  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() {
    final perksCubit = context.read<PerksCubit>();
    final String placeIdentifier = widget.venueID;

    // Fetch perks if not already loaded or if data is empty
    final currentPerks = perksCubit.state.perks;

    final bool hasPerksData = currentPerks.containsKey(placeIdentifier) &&
        ((currentPerks[placeIdentifier]?['offers'] as List?)?.isNotEmpty ==
                true ||
            (currentPerks[placeIdentifier]?['promoters'] as List?)
                    ?.isNotEmpty ==
                true);

    if (!hasPerksData) {
      final user = context.read<UserBloc>().state.user;
      perksCubit.fetchPerks(
        venueId: widget.venueID,
        userId: user!.uid,
        userEmail: user.email,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final String placeIdentifier = widget.venueID;

    return BlocBuilder<PerksCubit, PerksState>(
      builder: (context, state) {
        // Show loading state
        if (state.isLoading) {
          return _buildShimmerState();
        }

        // Check if we have data for this venue
        if (!state.perks.containsKey(placeIdentifier)) {
          return _buildEmptyState();
        }

        final venuePerks = state.perks[placeIdentifier]!;

        // Safer type casting with null checks
        final List<OfferModel> offers =
            (venuePerks['offers'] as List?)?.cast<OfferModel>() ?? [];

        final List<PromoterModel> promoters =
            (venuePerks['promoters'] as List?)?.cast<PromoterModel>() ?? [];

        final bool hasOffers = offers.isNotEmpty;
        final bool hasPromoters = promoters.isNotEmpty;

        // Show empty state if no perks available
        if (!hasOffers && !hasPromoters) {
          return _buildEmptyState();
        }

        // Show content with available data
        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (hasPromoters)
                PromoterSection(
                  venueID: widget.venueID,
                ),
              if (hasOffers)
                Padding(
                  padding: SizeUtils.horizontalPadding,
                  child: OffersSection(
                    venueID: widget.venueID,
                    onAddToCart: widget.onAddToCart,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

Widget _buildShimmerState() {
  return Shimmer.fromColors(
    baseColor: Colors.white12,
    highlightColor: Colors.white30,
    child: Column(
      children: List.generate(
        2,
        (index) => Container(
          height: 180,
          margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
          decoration: const BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.all(Radius.circular(20)),
          ),
        ),
      ),
    ),
  );
}

Widget _buildEmptyState() {
  return Center(
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 48),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withAlpha(26),
              border: Border.all(
                color: Colors.white.withAlpha(51),
              ),
            ),
            child: Icon(
              CupertinoIcons.globe,
              size: 50,
              color: Colors.white.withAlpha(204),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'No Perks Available',
            style: TextStyle(
              color: Colors.white.withAlpha(229),
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'Check back later for new offers and promotions',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withAlpha(178),
              fontSize: 16,
            ),
          ),
        ],
      ),
    ),
  );
}
