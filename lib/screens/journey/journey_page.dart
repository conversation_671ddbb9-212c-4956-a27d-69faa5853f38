import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/journey/question_stack.dart';
import 'package:vibeo/widgets/journey/initial_animation.dart';

class JourneyPlannerPage extends StatefulWidget {
  const JourneyPlannerPage({super.key});

  @override
  State<JourneyPlannerPage> createState() => _JourneyPlannerPageState();
}

class _JourneyPlannerPageState extends State<JourneyPlannerPage>
    with TickerProviderStateMixin {
  late AnimationController _initialAnimationController;
  late AnimationController _transitionController;

  bool _showInitialAnimation = true;
  bool _showQuestions = false;

  final Map<String, dynamic> _userResponses = {};

  @override
  void initState() {
    super.initState();

    _initialAnimationController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _transitionController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _startInitialAnimation();
  }

  Future<void> _startInitialAnimation() async {
    await _initialAnimationController.forward();
    await Future.delayed(const Duration(seconds: 1));

    if (mounted) {
      setState(() {
        _showInitialAnimation = false;
      });

      await _transitionController.forward();

      if (mounted) {
        setState(() {
          _showQuestions = true;
        });
      }
    }
  }

  void _onQuestionnaireComplete(Map<String, dynamic> responses) {
    setState(() {
      _userResponses.addAll(responses);
    });

    // Navigate to results or process responses
    _showResults();
  }

  void _showResults() {
    // TODO(journey): Implement results page navigation
    HapticFeedback.mediumImpact();
    Navigator.pop(context);
  }

  @override
  void dispose() {
    _initialAnimationController.dispose();
    _transitionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      body: SafeArea(
        child: Stack(
          children: [
            // Background gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    darkAppColors.backgroundColor,
                    darkAppColors.deepPurple.withAlpha(25),
                    darkAppColors.backgroundColor,
                  ],
                ),
              ),
            ),

            // Initial animation
            if (_showInitialAnimation)
              InitialAnimation(
                controller: _initialAnimationController,
              ),

            // Questions stack
            if (_showQuestions)
              FadeTransition(
                opacity: _transitionController,
                child: QuestionStack(
                  onComplete: _onQuestionnaireComplete,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
