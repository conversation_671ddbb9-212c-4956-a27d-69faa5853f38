import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:uuid/uuid.dart';
import 'package:vibeo/data/feed_details.dart';
import 'package:vibeo/logic/content/bloc/content_bloc.dart';
import 'package:video_compress/video_compress.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/previous_content.dart';
import 'package:vibeo/logic/feed/storage/feed_upload_rate_logic.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/routes/route_utils.dart';
import 'package:vibeo/themes/constant_theme.dart';

import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/views/content/location_bottom_sheet.dart';
import 'package:vibeo/widgets/content/add_button.dart';
import 'package:vibeo/widgets/content/choice_chips.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/widgets/content/infoUSP.dart';
import 'package:vibeo/widgets/content/infoVenueType.dart';
import 'package:vibeo/widgets/content/infoVibeScore.dart';

class AddContentPage extends StatefulWidget {
  final RouteSettings settings;

  const AddContentPage({required this.settings, super.key});

  @override
  State<AddContentPage> createState() => _AddContentPageState();
}

class _AddContentPageState extends State<AddContentPage> {
  final FeedRateLimiter rateLimiter = FeedRateLimiter();
  final FeedSecureStorage _secureStorage = FeedSecureStorage();
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _uspController = TextEditingController();
  bool isLiveMusic = false;
  String? selectedVenueName;
  String? selectedVenueType;
  String? selectedAddress;
  String? selectedPlaceID;
  String? selectedGoogleArea;
  String? selectedArea;

  late UserModel user;

  Map<String, dynamic> venueLocation = {};

  String? selectedCoverCharge;

  double vibeScore = 3;

  List<String>? areas = [
    'River North',
    'West Loop',
    'Wrigleyville',
    'Boystown',
    'Wicker Park',
    'Lincoln Park',
    'Downtown',
    'Old Town',
    'Pilsen',
  ];

  Map<String, dynamic>? limitRateInfo;

  @override
  void initState() {
    super.initState();
    fetchLimitRateInfo();
    fetchAreas();
    _loadPreviousArea();
    _setVideoPath();
  }

  void _setVideoPath() {
    if (widget.settings.arguments != null) {
      final mediaInfo = widget.settings.arguments as MediaInfo?;
      if (mediaInfo != null && mediaInfo.path != null) {
        final videoPath = mediaInfo.path!;
        AppLogger.info('Setting video path in ContentBloc: $videoPath');
        context.read<ContentBloc>().add(SetVideoPathEvent(videoPath));
      } else {
        AppLogger.error('MediaInfo or path is null');
      }
    } else {
      AppLogger.error('No arguments passed to AddContentPage');
    }
  }

  Future<void> fetchLimitRateInfo() async {
    user = context.read<UserBloc>().state.user!;

    if (user.isPartner) {
      limitRateInfo = await rateLimiter.getRateLimitInfo(user);
    }
  }

  void fetchAreas() {
    final FeedRepository feedRepository = FeedRepository();
    feedRepository.fetchAreas().then((value) {
      setState(() {
        areas = value;
      });
    });
  }

  Future<void> _loadPreviousContent() async {
    final previousContent = await _secureStorage.getPreviousContent();
    if (previousContent != null) {
      setState(() {
        selectedVenueType = previousContent.venueType;
        vibeScore = previousContent.vibeScore;
        _uspController.text = previousContent.usp ?? '';
        selectedCoverCharge = previousContent.coverCharge;
        selectedVenueName = previousContent.venueName;
        selectedAddress = previousContent.address;
        _searchController.text = selectedVenueName ?? '';
        isLiveMusic = previousContent.liveMusic;
        venueLocation = previousContent.venueLocation ?? {};
        selectedPlaceID = previousContent.selectedPlaceID;
        selectedGoogleArea = previousContent.address;
        selectedArea = previousContent.area;
      });
    }
  }

  Future<void> _loadPreviousArea() async {
    final previousContent = await _secureStorage.getPreviousContent();
    if (previousContent != null) {
      setState(() {
        selectedArea = previousContent.area;
      });
    }
  }

  Future<void> _savePreviousContent() async {
    final content = PreviousContent(
      venueName: selectedVenueName,
      address: selectedAddress,
      venueType: selectedVenueType,
      vibeScore: vibeScore,
      usp: _uspController.text,
      coverCharge: selectedCoverCharge,
      liveMusic: isLiveMusic,
      venueLocation: venueLocation,
      selectedPlaceID: selectedPlaceID,
      selectedGoogleArea: selectedGoogleArea,
      area: selectedArea,
    );
    await _secureStorage.savePreviousContent(content);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _uspController.dispose();
    super.dispose();
  }

  Future<void> uploadFeed() async {
    if (selectedVenueType == null ||
        selectedAddress == null ||
        selectedVenueName == null ||
        venueLocation.isEmpty ||
        selectedPlaceID == null ||
        selectedAddress!.trim().isEmpty) {
      showErrorSnackBar(context, 'Please select all required fields');
      return;
    }

    if (!user.isPartner || (limitRateInfo!['canUpload'] as bool? ?? true)) {
      final feedBloc = context.read<ContentBloc>();

      final details = {
        'id': const Uuid().v4(),
        'venueName': selectedVenueName,
        'address': selectedAddress,
        'latitude': venueLocation['latitude'],
        'longitude': venueLocation['longitude'],
        'coverCharge': selectedCoverCharge,
        'usp': _uspController.text,
        'liveMusic': isLiveMusic,
        'vibeScore': vibeScore,
        'venueType': selectedVenueType,
        'userID': user.uid,
        'emailID': user.email,
        'area': selectedArea,
        'googlePlaceID': selectedPlaceID,
        'googleArea': selectedGoogleArea,
      };

      await _savePreviousContent();
      AppLogger.info('Feed Details: $details');

      // First update the feed details
      feedBloc
        ..add(UpdateFeedDetailsEvent(details))
        // Then upload the feed
        ..add(UploadFeedEvent(isPartner: user.isPartner));

      if (mounted) {
        RouteUtils.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: SizeUtils.horizontalPadding,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ElevatedButton(
                    onPressed: () async {
                      await _loadPreviousContent();
                      await HapticFeedback.mediumImpact();
                    },
                    style: ElevatedButton.styleFrom(
                      foregroundColor: darkAppColors.primary,
                      shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(100),
                        ),
                      ),
                    ),
                    child: const Text('Use Previous'),
                  ),
                  if (context.read<ContentBloc>().isShazamError)
                    Container(
                      height: 20,
                      width: 20,
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                    ),
                  Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        context.read<ContentBloc>().add(OnFeedCanceled());
                        RouteUtils.pop(context);
                      },
                    ),
                  ),
                ],
              ),
              Expanded(
                child: buildScrollableWithFade(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.only(
                      bottom: 60,
                      top: 10,
                    ),
                    child: Column(
                      children: [
                        _buildSearchVenueSection(),
                        _buildMusicSection(),
                        _buildVenueTypeSection(),
                        _buildVibeScoreSection(),
                        _buildUSPSection(),
                        _buildCoverChargeSection(),
                      ],
                    ),
                  ),
                ),
              ),
              Column(
                children: [
                  if (user.isPartner && limitRateInfo != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 15),
                      child: Text(
                        '${limitRateInfo!['remainingUploads'] ?? 0} upload(s) left - expires in ${limitRateInfo!['timeUntilNextUpload']}',
                        textAlign: TextAlign.center,
                      ),
                    ),
                  addContentButton(
                    title: 'Upload',
                    onTap: uploadFeed,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Padding _buildSearchVenueSection() {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 24,
      ),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topRight,
            child: Container(
              margin: const EdgeInsets.only(
                bottom: 6,
              ),
              padding: const EdgeInsets.symmetric(
                vertical: 6,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                color: darkAppColors.deepPurple,
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  icon: const Icon(Icons.keyboard_arrow_down),
                  isDense: true,
                  hint: Text(
                    selectedArea ?? 'Select Area',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                  items: areas?.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(
                        value,
                        style: const TextStyle(fontSize: 14),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    // Handle selection
                    setState(() {
                      selectedArea = value;
                    });
                  },
                  dropdownColor: Colors.grey.shade900,
                  borderRadius: const BorderRadius.all(Radius.circular(12)),
                  elevation: 3,
                  isExpanded: false,
                ),
              ),
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(CupertinoIcons.location_fill),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Venue Location *',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Container(
                      height: 40,
                      margin: const EdgeInsets.only(
                        right: 16,
                      ),
                      decoration: const BoxDecoration(
                        color: Colors.white12,
                        borderRadius: BorderRadius.all(
                          Radius.circular(100),
                        ),
                      ),
                      child: TextField(
                        controller: _searchController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          fillColor: Colors.transparent,
                          contentPadding: EdgeInsets.only(
                            left: 24,
                            bottom: 5,
                          ),
                          hintText: 'Search venue',
                          focusedBorder: InputBorder.none,
                        ),
                        readOnly: true, // Make it read-only
                        onTap: () async {
                          await showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            backgroundColor: Colors.transparent,
                            builder: (context) => SizedBox(
                              height: MediaQuery.of(context).size.height * 0.8,
                              child: LocationBottomSheet(
                                selectedLoc: selectedVenueName ?? '',
                                onLocationSelected: (
                                  String venueName,
                                  String address,
                                  double lat,
                                  double lng,
                                  String placeID,
                                  String? googleArea,
                                ) {
                                  setState(() {
                                    selectedVenueName = venueName;
                                    selectedAddress = address;
                                    _searchController.text = venueName;
                                    venueLocation['latitude'] = lat;
                                    venueLocation['longitude'] = lng;
                                    selectedPlaceID = placeID;
                                    selectedGoogleArea = googleArea;
                                  });
                                },
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Padding _buildMusicSection() {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 24,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Icon(CupertinoIcons.double_music_note),
          const SizedBox(width: 12),
          BlocBuilder<ContentBloc, ContentState>(
            builder: (context, state) {
              return Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Is this live music?',
                          style: TextStyle(
                            fontSize: 17,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Row(
                          children: [
                            ChoiceChip(
                              label: const Text('Yes'),
                              labelStyle:
                                  const TextStyle(fontWeight: FontWeight.bold),
                              shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(100)),
                              ),
                              side: BorderSide.none,
                              selected: isLiveMusic,
                              selectedColor: darkAppColors.deepPurple,
                              showCheckmark: true,
                              checkmarkColor: Colors.white,
                              elevation: 0,
                              onSelected: (bool selected) {
                                HapticFeedback.mediumImpact();
                                setState(() {
                                  isLiveMusic = true;
                                });
                              },
                            ),
                            const SizedBox(width: 8),
                            ChoiceChip(
                              label: const Text('No'),
                              labelStyle:
                                  const TextStyle(fontWeight: FontWeight.bold),
                              shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(100)),
                              ),
                              side: BorderSide.none,
                              selected: isLiveMusic == false,
                              backgroundColor: Colors.transparent,
                              selectedColor: darkAppColors.deepPurple,
                              elevation: 0,
                              showCheckmark: true,
                              checkmarkColor: Colors.white,
                              onSelected: (bool selected) {
                                HapticFeedback.mediumImpact();
                                setState(() {
                                  isLiveMusic = false;
                                });
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Padding _buildVenueTypeSection() {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 24,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.celebration),
          const SizedBox(width: 12),
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'What type of venue is this? *',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    infoVenueType(context),
                  ],
                ),
                const SizedBox(height: 5),
                Wrap(
                  spacing: 5,
                  runSpacing: 10,
                  children: venueDefinitions.keys
                      .map(
                        (venue) => buildChoiceChip(
                          venue,
                          () {
                            setState(() {
                              selectedVenueType = venue;
                            });
                          },
                          isSelected: selectedVenueType == venue,
                        ),
                      )
                      .toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Padding _buildCoverChargeSection() {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 24,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.door_back_door_outlined),
          const SizedBox(width: 12),
          Flexible(
            // Wrap with Flexible
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Cover charge paid?',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                Wrap(
                  spacing: 5,
                  runSpacing: 5,
                  children: coverCharge
                      .map<Widget>(
                        (cover) => buildChoiceChip(
                          cover,
                          () {
                            setState(() {
                              selectedCoverCharge = cover;
                            });
                          },
                          isSelected: selectedCoverCharge == cover,
                        ),
                      )
                      .toList(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Padding _buildUSPSection() {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 24,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.sell_rounded),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      "Today's USP",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    infoUSP(context),
                  ],
                ),
                const SizedBox(
                  height: 5,
                ),
                Container(
                  height: 40,
                  margin: const EdgeInsets.only(
                    right: 16,
                  ),
                  decoration: const BoxDecoration(
                    color: Colors.white12,
                    borderRadius: BorderRadius.all(
                      Radius.circular(100),
                    ),
                  ),
                  child: TextField(
                    controller: _uspController,
                    style: const TextStyle(
                      color: Colors.white,
                    ),
                    decoration: const InputDecoration(
                      fillColor: Colors.transparent,
                      contentPadding: EdgeInsets.only(
                        left: 24,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.all(
                          Radius.circular(100),
                        ),
                      ),
                      hintText: 'Enter the USP',
                    ),
                    onTap: () {
                      // Scroll to the bottom with animation when TextField is tapped
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Padding _buildVibeScoreSection() {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: 24,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Row(
                children: [
                  Icon(Icons.people_alt_rounded, color: Colors.white),
                  SizedBox(width: 8),
                  Text(
                    'Vibe Score',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: Row(
                  children: [
                    Text(
                      vibeScoreDefinitions[vibeScore]!['name']!,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.normal,
                        fontSize: 15,
                      ),
                    ),
                    const SizedBox(width: 10),
                    infoVibeScore(context),
                  ],
                ),
              ),
            ],
          ),
          Padding(
            padding: SizeUtils.horizontalPadding.copyWith(left: 30),
            child: Stack(
              children: [
                // Background Track
                Container(
                  height: 30,
                  decoration: BoxDecoration(
                    color: darkAppColors.lightColor.withAlpha(26),
                    borderRadius: const BorderRadius.all(Radius.circular(100)),
                  ),
                ),
                // Active Track
                FractionallySizedBox(
                  widthFactor: vibeScore / 5,
                  child: Container(
                    height: 30,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          darkAppColors.lightColor,
                          darkAppColors.deepPurple,
                        ],
                      ),
                      borderRadius:
                          const BorderRadius.all(Radius.circular(100)),
                    ),
                  ),
                ),
                // Slider
                SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: Colors.transparent,
                    inactiveTrackColor: Colors.transparent,
                    thumbColor: Colors.white,
                    overlayColor: Colors.white.withAlpha(51),
                    thumbShape: const RoundSliderThumbShape(
                      elevation: 10,
                    ),
                    overlayShape: const RoundSliderOverlayShape(
                      overlayRadius: 15,
                    ),
                    trackHeight: 0,
                  ),
                  child: Slider(
                    min: 1,
                    max: 5,
                    divisions: 4,
                    value: vibeScore,
                    onChanged: (value) {
                      HapticFeedback.mediumImpact();
                      setState(() {
                        vibeScore = value;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  vibeScoreDefinitions[1]!['name']!,
                  style: TextStyle(
                    color: Colors.white.withAlpha(178),
                    fontSize: 12,
                  ),
                ),
                Text(
                  vibeScoreDefinitions[5]!['name']!,
                  style: TextStyle(
                    color: Colors.white.withAlpha(178),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
