import 'package:cached_video_player_plus/cached_video_player_plus.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/analytics/analytics.dart';

import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/logic/feed/controller/feed_controller.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/views/feed/vibe_playing_screen.dart';
import 'package:vibeo/views/shimmer/feed_playing_shimmer_page.dart';

class FeedPlayingPage extends StatefulWidget {
  final RouteSettings settings;

  const FeedPlayingPage({
    required this.settings,
    super.key,
  });

  @override
  State<FeedPlayingPage> createState() => _FeedPlayingPageState();
}

class _FeedPlayingPageState extends State<FeedPlayingPage>
    with TickerProviderStateMixin {
  CachedVideoPlayerPlusController? _controller;

  late AnimationController _fadeController;
  late AnimationController _animationController;
  int _currentIndex = 0;

  final ValueNotifier<bool> _likedNotifier = ValueNotifier(false);

  bool _isControllerInitialized = false;
  bool _isAnimationInitialized = false;
  bool _isDisposed = false;

  late List<FeedModel> feeds = [];
  late final int initalIndex;
  late final String title;
  bool _isLongPressed = false;
  double _scale = 1; // Scale of the page
  double _dragYOffset = 0; // Y-offset during drag
  late FeedController feedController;
  late String? genre;
  late bool fromVenuePage = false;

  late AnimationController _dragController;

  @override
  void initState() {
    super.initState();
    feedController = FeedController(context.read<UserBloc>().state.user!.uid);
    _dragController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    final routeData = widget.settings.arguments! as Map<String, dynamic>;
    if (routeData['genre'] == null) {
      _intializeFeeds(routeData);
    } else {
      genre = routeData['genre'] as String;
      final feedState = context.read<FeedBloc>().state;
      if (feedState.musicGenreFeeds.containsKey(genre) &&
          feedState.musicGenreFeeds[genre]!.isNotEmpty) {
        _initializeGenreFeeds(feedState);
      } else {
        context.read<FeedBloc>().add(FetchMusicGenreFeedsEvent(genre!));
      }
    }
  }

  void _intializeFeeds(Map<String, dynamic> routeData) {
    initalIndex = routeData['initialIndex'] as int;
    fromVenuePage = (routeData['isVenuePage'] as bool?) ?? false;
    feeds = routeData['feeds'] as List<FeedModel>;
    title = routeData['title']?.toString() ?? 'Vibes';
    _currentIndex = routeData['initialIndex'] as int;

    _initializeControllers();
  }

  void _initializeGenreFeeds(FeedState feedState) {
    feeds = feedState.musicGenreFeeds[genre]!;
    title = genre!;
    initalIndex = 0;
    _currentIndex = 0;
    _initializeControllers();
  }

  void _initializeControllers() {
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animationController = AnimationController(vsync: this);

    _setupAnimationController();
    _initializeVideo(feeds[_currentIndex].videoURL);
  }

  void _onDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragYOffset += details.primaryDelta!;
      _scale =
          1 - (_dragYOffset.abs() / MediaQuery.of(context).size.height * 0.3);
      if (_scale < 0.8) _scale = 0.8;
    });
  }

  void _onDragEnd(DragEndDetails details) {
    if (!_isLongPressed && _dragYOffset > 100) {
      RouteUtils.pop(context);
    } else {
      _dragController.reverse(from: 1).then((_) {
        setState(() {
          _scale = 1.0;
          _dragYOffset = 0.0;
        });
      });
    }
  }

  void _setupAnimationController() {
    bool wilPop = false;

    _animationController.addStatusListener((status) {
      if (!_isDisposed) {
        _isAnimationInitialized = status != AnimationStatus.dismissed;
      }
      if (status == AnimationStatus.completed) {
        _animationController
          ..stop()
          ..reset();
        setState(() {
          if (_currentIndex + 1 < feeds.length) {
            _currentIndex += 1;
            _initializeVideo(feeds[_currentIndex].videoURL);
          } else if (!wilPop) {
            wilPop = true;
            RouteUtils.pop(context);
          }
        });
      }
    });
  }

  Future<void> _initializeVideo(String url) async {
    if (_controller != null) {
      await _controller!.pause();
      await _controller!.dispose();
      _controller = null;
    }

    // Reset states
    if (!mounted) return;
    setState(() {
      _isControllerInitialized = false;
    });
    _controller = CachedVideoPlayerPlusController.networkUrl(
      Uri.parse(url),
      invalidateCacheIfOlderThan: const Duration(
        days: 7,
      ),
    );

    await _controller!.initialize().then((_) {
      if (!_isDisposed && mounted) {
        setState(() => _isControllerInitialized = true);
      }
    }).catchError((error) {
      if (!_isDisposed && mounted) {
        setState(() => _isControllerInitialized = false);
      }
    });
    await feedController.onFeedViewed(feeds[_currentIndex].id);
    final isLiked =
        await feedController.checkIfFeedLiked(feeds[_currentIndex].id);
    _likedNotifier.value = isLiked;

    if (mounted) {
      _fadeController.reset();
      _animationController
        ..reset()
        ..duration = _controller!.value.duration;
      await _fadeController.forward();
      await _controller!.play().then((_) async {
        await _animationController.forward();
      });

      _controller!.addListener(() {
        if (_controller!.value.position >= _controller!.value.duration) {
          _animationController.forward(from: 1);
        }
      });
      final AnalyticsService analytics = AnalyticsService.instance;
      await analytics.trackVideoView(
        feeds[_currentIndex].id,
      );
    }
  }

  Future<void> _handleFeedChange(int newIndex) async {
    if (!mounted) return;
    // Stop current playback
    await _controller?.pause();

    setState(() {
      _currentIndex = newIndex;
    });

    _fadeController.reset();
    _animationController.reset();
    await _initializeVideo(feeds[newIndex].videoURL);
  }

  @override
  void dispose() {
    _isDisposed = true;
    _animationController.stop();
    _fadeController.stop();
    _controller?.pause();
    _dragController.dispose();
    Future.microtask(() {
      _controller?.dispose();
      _fadeController.dispose();
      _animationController.dispose();
    });
    super.dispose();
  }

  void stopVideo() {
    if (!mounted || _isDisposed) return;

    setState(() => _isLongPressed = true);

    // Video control with safety wrapper
    try {
      if (_isControllerInitialized && _controller!.value.isPlaying) {
        _controller!.pause().catchError((_) {});
      }
    } catch (e) {
      _isControllerInitialized = false;
    }

    // Animation control with status check
    try {
      if (_isAnimationInitialized ||
          _animationController.status == AnimationStatus.forward) {
        _animationController.stop();
      }
    } catch (e) {
      _isAnimationInitialized = false;
    }
  }

  void startVideo() {
    if (!mounted || _isDisposed) return;

    setState(() => _isLongPressed = false);

    try {
      if (_isControllerInitialized && !_controller!.value.isPlaying) {
        _controller!.play().catchError((_) {});
      }
    } catch (e) {
      _isControllerInitialized = false;
    }

    try {
      if (_isAnimationInitialized ||
          _animationController.status != AnimationStatus.forward) {
        _animationController.forward();
      }
    } catch (e) {
      _isAnimationInitialized = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<FeedBloc, FeedState>(
      listenWhen: (previous, current) {
        return previous.byMusicLoading && current is FeedsLoaded;
      },
      listener: (context, state) {
        if (state is FeedsLoaded && state.musicGenreFeeds.containsKey(genre)) {
          setState(() {
            _initializeGenreFeeds(state);
          });
        }
      },
      builder: (context, state) {
        if (state.byMusicLoading || feeds.isEmpty) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: GestureDetector(
              onVerticalDragEnd: _onDragEnd,
              onVerticalDragUpdate: _onDragUpdate,
              child: AnimatedBuilder(
                animation: _dragController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scale,
                    child: Transform.translate(
                      offset: Offset(0, _dragYOffset),
                      child: const FeedPlayingShimmerPage(),
                    ),
                  );
                },
              ),
            ),
          );
        } else {
          final feed = feeds[_currentIndex];
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: GestureDetector(
              onLongPressStart: (_) {
                stopVideo();
              },
              onLongPressEnd: (_) {
                startVideo();
              },
              onTapUp: (details) {
                if (!_isLongPressed) {
                  final screenWidth = MediaQuery.of(context).size.width;
                  if (details.globalPosition.dx < screenWidth / 2) {
                    if (_currentIndex > 0) {
                      _handleFeedChange(_currentIndex - 1);
                    }
                  } else {
                    if (_currentIndex < feeds.length - 1) {
                      _handleFeedChange(_currentIndex + 1);
                    } else {
                      RouteUtils.pop(context);
                    }
                  }
                }
              },
              onVerticalDragEnd: _onDragEnd,
              onVerticalDragUpdate: _onDragUpdate,
              child: AnimatedBuilder(
                animation: _dragController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scale,
                    child: Transform.translate(
                      offset: Offset(0, _dragYOffset),
                      child: VibePlayingScreen(
                        animationController: _animationController,
                        controller: _controller,
                        feed: feed,
                        currentFeedIndex: _currentIndex,
                        fadeController: _fadeController,
                        feedsLength: feeds.length,
                        imageURL: feeds[_currentIndex].thumbnailURL,
                        startVideo: startVideo,
                        stopVideo: stopVideo,
                        title: title,
                        likedNotifier: _likedNotifier,
                        fromVenuePage: fromVenuePage,
                      ),
                    ),
                  );
                },
              ),
            ),
          );
        }
      },
    );
  }
}
