import 'package:cached_video_player_plus/cached_video_player_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_carousel_slider/carousel_slider.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/feed/controller/feed_controller.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/feed/vibe_feed_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/views/feed/vibe_playing_screen.dart';
import 'package:vibeo/views/shimmer/feed_playing_shimmer_page.dart';

class VibeFeedPlayingPage extends StatefulWidget {
  final RouteSettings settings;
  const VibeFeedPlayingPage({required this.settings, super.key});

  @override
  State<VibeFeedPlayingPage> createState() => _VibeFeedPlayingPageState();
}

class _VibeFeedPlayingPageState extends State<VibeFeedPlayingPage>
    with TickerProviderStateMixin {
  CachedVideoPlayerPlusController? _controller;

  late AnimationController _fadeController;
  late AnimationController _animationController;
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  late FeedController feedController;

  bool _isLongPressed = false;

  final ValueNotifier<bool> _likedNotifier = ValueNotifier(false);
  bool _isControllerInitialized = false;
  bool _isAnimationInitialized = false;
  bool _isDisposed = false;

  // Venue-specific variables
  late List<FeedModel> feeds;
  late final List<VibeFeedModel> venues;
  late int currentVenueIndex;
  late int currentFeedIndex;
  late final String title;
  double _scale = 1;
  double _dragYOffset = 0;

  late AnimationController _dragController;
  bool isSliding = false;

  @override
  void initState() {
    super.initState();
    feedController = FeedController(context.read<UserBloc>().state.user!.uid);
    _dragController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    final data = widget.settings.arguments! as Map<String, dynamic>;
    venues = data['venues'] as List<VibeFeedModel>;
    if (venues.last.isEndMarker) {
      venues.removeLast();
    }
    currentVenueIndex = data['initialVenueIndex'] as int;
    title = data['title']?.toString() ?? 'Vibes';
    currentFeedIndex = 0;

    feeds = venues[currentVenueIndex].feeds;

    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animationController = AnimationController(vsync: this);

    _setupAnimationController();
    _initializeVideo(feeds[currentFeedIndex].videoURL);
  }

  void _onDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragYOffset += details.primaryDelta!;
      _scale =
          1 - (_dragYOffset.abs() / MediaQuery.of(context).size.height * 0.3);
      if (_scale < 0.8) _scale = 0.8;
    });
  }

  void _onDragEnd(DragEndDetails details) {
    if (!_isLongPressed && _dragYOffset > 100) {
      RouteUtils.pop(context);
    } else {
      _dragController.reverse(from: 1).then((_) {
        setState(() {
          _scale = 1.0;
          _dragYOffset = 0.0;
        });
      });
    }
  }

  void _setupAnimationController() {
    bool willPop = false;
    _animationController.addStatusListener((status) {
      if (!_isDisposed) {
        _isAnimationInitialized = status != AnimationStatus.dismissed;
      }
      if (status == AnimationStatus.completed) {
        _animationController
          ..stop()
          ..reset();
        setState(() {
          if (currentFeedIndex + 1 < feeds.length) {
            currentFeedIndex += 1;
            _initializeVideo(feeds[currentFeedIndex].videoURL);
          } else if (currentVenueIndex + 1 < venues.length) {
            // Auto-slide to next venue
            currentVenueIndex++;
            feeds = venues[currentVenueIndex].feeds;
            currentFeedIndex = 0;
            _carouselController.nextPage(Durations.medium2);
            _initializeVideo(feeds[currentFeedIndex].videoURL);
          } else if (!willPop) {
            willPop = true;
            RouteUtils.pop(context);
          }
        });
      }
    });
  }

  Future<void> _initializeVideo(String url) async {
    if (_controller != null) {
      await _controller!.pause();
      await _controller!.dispose();
      _controller = null;
    }

    // Reset states
    if (!mounted) return;
    setState(() {
      _isControllerInitialized = false;
    });
    _controller = CachedVideoPlayerPlusController.networkUrl(
      Uri.parse(url),
      invalidateCacheIfOlderThan: const Duration(
        days: 7,
      ),
    );
    await _controller!.initialize().then((_) {
      if (!_isDisposed && mounted) {
        setState(() => _isControllerInitialized = true);
      }
    }).catchError((error) {
      if (!_isDisposed && mounted) {
        setState(() => _isControllerInitialized = false);
      }
    });
    await feedController.onFeedViewed(feeds[currentFeedIndex].id);
    final isLiked =
        await feedController.checkIfFeedLiked(feeds[currentFeedIndex].id);
    _likedNotifier.value = isLiked;

    if (mounted) {
      _fadeController.reset();
      _animationController
        ..reset()
        ..duration = _controller!.value.duration;
      await _fadeController.forward();
      await _controller!.play().then((_) async {
        await _animationController.forward();
      });

      _controller!.addListener(() {
        if (_controller!.value.position >= _controller!.value.duration) {
          _animationController.forward(from: 1);
        }
      });

      final AnalyticsService analytics = AnalyticsService.instance;
      await analytics.trackVideoView(
        feeds[currentFeedIndex].id,
      );
    }
  }

  Future<void> _handleFeedChange(int newIndex) async {
    if (!mounted) return;
    // Stop current playback
    await _controller?.pause();

    // Check if we're at the last feed of current venue
    if (newIndex >= feeds.length && currentVenueIndex < venues.length - 1) {
      // Move to next venue
      setState(() {
        currentVenueIndex++;
        feeds = venues[currentVenueIndex].feeds;
        currentFeedIndex = 0;
      });
      // Trigger the carousel slide animation
      _carouselController.nextPage(Durations.medium2);
      // Initialize video for the first feed of new venue
      await _initializeVideo(feeds[0].videoURL);
    } else {
      setState(() {
        currentFeedIndex = newIndex;
      });
      _fadeController.reset();
      _animationController.reset();
      await _initializeVideo(feeds[newIndex].videoURL);
    }
  }

  void stopVideo() {
    if (!mounted || _isDisposed) return;

    setState(() => _isLongPressed = true);

    // Video control with safety wrapper
    try {
      if (_isControllerInitialized && _controller!.value.isPlaying) {
        _controller!.pause().catchError((_) {});
      }
    } catch (e) {
      _isControllerInitialized = false;
    }

    // Animation control with status check
    try {
      if (_isAnimationInitialized ||
          _animationController.status == AnimationStatus.forward) {
        _animationController.stop();
      }
    } catch (e) {
      _isAnimationInitialized = false;
    }
  }

  void startVideo() {
    if (!mounted || _isDisposed) return;

    setState(() => _isLongPressed = false);

    try {
      if (_isControllerInitialized && !_controller!.value.isPlaying) {
        _controller!.play().catchError((_) {});
      }
    } catch (e) {
      _isControllerInitialized = false;
    }

    try {
      if (_isAnimationInitialized ||
          _animationController.status != AnimationStatus.forward) {
        _animationController.forward();
      }
    } catch (e) {
      _isAnimationInitialized = false;
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _animationController.stop();
    _fadeController.stop();
    _controller?.pause();
    _dragController.dispose();
    Future.microtask(() {
      _controller?.dispose();
      _fadeController.dispose();
      _animationController.dispose();
    });
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final feed = feeds[currentFeedIndex];

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: CarouselSlider.builder(
        itemCount: venues.length,
        controller: _carouselController,
        slideTransform: const CubeTransform(),
        initialPage: currentVenueIndex,
        onSlideStart: () {
          setState(() {
            isSliding = true;
          });
        },
        onSlideEnd: () => setState(() {
          isSliding = false;
        }),
        onSlideChanged: (int index) {
          setState(() {
            HapticFeedback.mediumImpact();
            currentFeedIndex = 0;
            currentVenueIndex = index;
            feeds = venues[currentVenueIndex].feeds;
            _handleFeedChange(currentFeedIndex);
          });
        },
        slideBuilder: (index) {
          if (isSliding && index != currentVenueIndex) {
            return const FeedPlayingShimmerPage();
          }
          return GestureDetector(
            onLongPressStart: (_) => stopVideo(),
            onLongPressEnd: (_) => startVideo(),
            onTapUp: (details) {
              if (!_isLongPressed) {
                final screenWidth = MediaQuery.of(context).size.width;
                if (details.globalPosition.dx < screenWidth / 2) {
                  // Left tap
                  if (currentFeedIndex > 0) {
                    _handleFeedChange(currentFeedIndex - 1);
                  } else if (currentVenueIndex > 0) {
                    // Move to previous venue's last feed

                    setState(() {
                      currentVenueIndex--;
                      feeds = venues[currentVenueIndex].feeds;
                      currentFeedIndex = feeds.length - 1;
                      _handleFeedChange(currentFeedIndex);
                    });
                    _carouselController.previousPage(Durations.medium2);
                  }
                } else {
                  // Right tap
                  if (currentFeedIndex < feeds.length - 1) {
                    _handleFeedChange(currentFeedIndex + 1);
                  } else if (currentVenueIndex < venues.length - 1) {
                    // Move to next venue's first feed
                    setState(() {
                      currentVenueIndex++;
                      feeds = venues[currentVenueIndex].feeds;
                      currentFeedIndex = 0;
                      _handleFeedChange(currentFeedIndex);
                    });
                    _carouselController.nextPage(Durations.medium2);
                  } else {
                    RouteUtils.pop(context);
                  }
                }
              }
            },
            onVerticalDragEnd: _onDragEnd,
            onVerticalDragUpdate: _onDragUpdate,
            child: AnimatedBuilder(
              animation: _dragController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scale,
                  child: Transform.translate(
                    offset: Offset(0, _dragYOffset),
                    child: VibePlayingScreen(
                      animationController: _animationController,
                      fadeController: _fadeController,
                      controller: _controller,
                      feed: feed,
                      currentFeedIndex: currentFeedIndex,
                      feedsLength: feeds.length,
                      imageURL: venues[currentVenueIndex]
                          .feeds[currentFeedIndex]
                          .thumbnailURL,
                      startVideo: startVideo,
                      stopVideo: stopVideo,
                      title: title,
                      likedNotifier: _likedNotifier,
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
