import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/logic/preload/bloc/preload_bloc.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/feed/vibe_feed_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/views/feed/vibe_playing_screen.dart';

class VideoPlayPage extends StatefulWidget {
  final RouteSettings settings;
  const VideoPlayPage({required this.settings, super.key});

  @override
  State<VideoPlayPage> createState() => _VideoPlayPageState();
}

class _VideoPlayPageState extends State<VideoPlayPage>
    with TickerProviderStateMixin {
  late List<FeedModel> feeds;
  late final List<VibeFeedModel> venues;
  late int currentVenueIndex;
  late int currentFeedIndex = 0;
  late final String title;
  late AnimationController _animationController;
  late AnimationController _fadeController;

  final ValueNotifier<bool> _likedNotifier = ValueNotifier(false);

  bool _isLongPressed = false;
  bool _isAnimationInitialized = false;

  double _scale = 1;
  double _dragYOffset = 0;
  late AnimationController _dragController;

  bool isSliding = false;

  @override
  void initState() {
    super.initState();

    _dragController = AnimationController(
      duration: const Duration(milliseconds: 200),
      animationBehavior: AnimationBehavior.preserve,
      upperBound: 1,
      lowerBound: 0,
      vsync: this,
    );

    _animationController = AnimationController(
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      lowerBound: 0,
      upperBound: 1,
      animationBehavior: AnimationBehavior.preserve,
      vsync: this,
    );

    // Initialize variables asynchronously
    _initializeVariables().then((_) {
      _setupAnimationController();
    });
  }

  void _setupAnimationController() {
    _animationController.addStatusListener((status) {
      if (!mounted) return;
      if (status != AnimationStatus.dismissed) {
        setState(() => _isAnimationInitialized = true);
      }

      if (status == AnimationStatus.completed) {
        _animationController
          ..stop()
          ..reset();

        if (currentFeedIndex + 1 < feeds.length) {
          setState(() {
            currentFeedIndex += 1;
          });
          _onIndexChanged(currentFeedIndex);
        } else {
          RouteUtils.pop(context);
        }
      }
    });
  }

  Future<void> _initializeVariables() async {
    try {
      final data = widget.settings.arguments! as Map<String, dynamic>;
      venues = data['venues'] as List<VibeFeedModel>;
      if (venues.last.isEndMarker) {
        venues.removeLast();
      }
      currentVenueIndex = data['initialVenueIndex'] as int;

      title = data['title']?.toString() ?? 'Vibes';
      currentFeedIndex = 0;

      feeds = venues[currentVenueIndex].feeds;

      if (feeds.isEmpty) {
        AppLogger.debug('No feeds available, popping page');
        RouteUtils.pop(context);
        return;
      }

      // Initialize videos
      context.read<PreloadBloc>().add(InitializeEvent(feeds, currentFeedIndex));
    } catch (e, stackTrace) {
      AppLogger.debug('Error in _initializeVariables: $e\n$stackTrace');
      if (mounted) {
        RouteUtils.pop(context);
      }
    }
  }

  Future<void> _onIndexChanged(int index) async {
    if (!mounted) return;

    AppLogger.debug('Changing to video index: $index');

    // Stop and reset animations first
    _animationController
      ..stop()
      ..reset();
    _fadeController
      ..stop()
      ..reset();

    // Update state first to show the correct thumbnail while loading
    setState(() {
      currentFeedIndex = index;
      _isAnimationInitialized = false;
    });

    // Notify the PreloadBloc to handle video controller changes
    context.read<PreloadBloc>().add(VideoIndexChangedEvent(index));

    // Wait a bit to ensure the PreloadBloc has time to update the controller
    await Future.delayed(const Duration(milliseconds: 50));

    if (!mounted) return;

    // Get the controller after the PreloadBloc has updated it
    final preloadState = context.read<PreloadBloc>().state;
    final controller = preloadState.controllers[index];
    final controllerStatus = preloadState.controllerStatus[index];

    if (controller == null) {
      AppLogger.debug('Controller is null for index: $index');
      return;
    }

    try {
      // Wait for the controller to be fully initialized by the PreloadBloc
      if (controllerStatus != ControllerStatus.initialized) {
        AppLogger.debug(
          'Waiting for controller to be initialized by PreloadBloc',
        );
        // We'll let the BlocBuilder in the UI handle showing the loading state
        return;
      }

      if (!mounted) return;

      // Set new duration and reset animation controller
      _animationController
        ..duration = controller.value.duration
        ..reset();

      // Wait for initialization to complete
      await controller.setLooping(false);

      // Reset fade controller and ensure it starts from zero
      _fadeController.value = 0.0;

      // Force a frame update to ensure video is visible
      if (mounted) setState(() {});

      // Use a small delay to ensure the video frame is ready before fading in
      await Future.delayed(const Duration(milliseconds: 100));
      await _fadeController.forward();

      // Only play video after fade in has started
      AppLogger.debug('Playing video');
      await controller.play();

      // Force another frame update after play to ensure video is visible
      if (mounted) setState(() {});

      AppLogger.debug('Video playing: ${controller.value.isPlaying}');

      if (controller.value.isPlaying) {
        await _animationController.forward();
      }
    } catch (e, stackTrace) {
      AppLogger.error('Error in _onIndexChanged: $e');
      AppLogger.debug('Stack trace: $stackTrace');
    }
  }

  void _onDragUpdate(DragUpdateDetails details) {
    setState(() {
      _dragYOffset += details.primaryDelta!;
      _scale =
          1 - (_dragYOffset.abs() / MediaQuery.of(context).size.height * 0.3);
      if (_scale < 0.8) _scale = 0.8;
    });
  }

  void _onDragEnd(DragEndDetails details) {
    if (!_isLongPressed && _dragYOffset > 100) {
      RouteUtils.pop(context);
    } else {
      _dragController.reverse(from: 1).then((_) {
        setState(() {
          _scale = 1.0;
          _dragYOffset = 0.0;
        });
      });
    }
  }

  void stopVideo() {
    setState(() => _isLongPressed = true);

    final controller =
        context.read<PreloadBloc>().state.controllers[currentFeedIndex];
    if (controller != null) {
      controller.pause();
    }

    if (_isAnimationInitialized) {
      _animationController.stop();
    }
  }

  void startVideo() {
    setState(() => _isLongPressed = false);

    final controller =
        context.read<PreloadBloc>().state.controllers[currentFeedIndex];
    if (controller != null) {
      controller.play();
      // Force a frame update to ensure video is visible
      if (mounted) setState(() {});
    }

    if (_isAnimationInitialized) {
      _animationController.forward();
    }
  }

  void _cleanupControllers() {
    try {
      context.read<PreloadBloc>().add(const DisposeControllersEvent());
    } catch (_) {
      // Ignore any errors during cleanup
    }
  }

  @override
  void dispose() {
    _cleanupControllers();
    _dragController.dispose();
    _animationController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  void deactivate() {
    _cleanupControllers();
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onLongPressStart: (_) => stopVideo(),
        onLongPressEnd: (_) => startVideo(),
        onTapUp: (details) {
          if (!_isLongPressed) {
            final screenWidth = MediaQuery.of(context).size.width;
            if (details.globalPosition.dx < screenWidth / 2) {
              if (currentFeedIndex > 0) {
                _onIndexChanged(currentFeedIndex - 1);
              } else {
                _onIndexChanged(0);
              }
            } else {
              if (currentFeedIndex < feeds.length - 1) {
                _onIndexChanged(currentFeedIndex + 1);
              } else {
                RouteUtils.pop(context);
              }
            }
          }
        },
        onVerticalDragEnd: _onDragEnd,
        onVerticalDragUpdate: _onDragUpdate,
        child: AnimatedBuilder(
          animation: _dragController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scale,
              child: Transform.translate(
                offset: Offset(0, _dragYOffset),
                child: BlocBuilder<PreloadBloc, PreloadState>(
                  builder: (context, state) {
                    final currentController =
                        state.controllers[currentFeedIndex];
                    return VibePlayingScreen(
                      controller: currentController,
                      imageURL: feeds[currentFeedIndex].thumbnailURL,
                      feed: feeds[currentFeedIndex],
                      currentFeedIndex: currentFeedIndex,
                      animationController: _animationController,
                      fadeController: _fadeController,
                      feedsLength: feeds.length,
                      title: title,
                      stopVideo: stopVideo,
                      startVideo: startVideo,
                      likedNotifier: _likedNotifier,
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
