import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/logic/artists/bloc/artists_bloc.dart';

import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/models/artists/artist_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';

import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/exceptions/try_wrapper.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/widgets/feed/feed_tile.dart';
import 'package:vibeo/widgets/global/no_content_page.dart';
import 'package:vibeo/widgets/global/tag_widget.dart';
import 'package:vibeo/widgets/video_tiles/vibe_shimmer_tiles.dart';

class ArtistsFeedPage extends StatefulWidget {
  final RouteSettings settings;
  const ArtistsFeedPage({required this.settings, super.key});

  @override
  State<ArtistsFeedPage> createState() => _ArtistsFeedPageState();
}

class _ArtistsFeedPageState extends State<ArtistsFeedPage> {
  final FeedRepository feedRepository = FeedRepository();
  late ArtistModel artistModel;
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();
    artistModel = widget.settings.arguments! as ArtistModel;
    _fetchArtistsFeeds();
  }

  Future<void> _fetchArtistsFeeds() async {
    setState(() {
      _isLoading = true;
    });
    await tryWrapperAsync(
      () async {
        if (artistModel.feeds == null) {
          final feeds = await feedRepository.fetchUserFeeds(artistModel.uid);
          if (mounted) {
            final ArtistModel updatedArtist = context
                .read<ArtistBloc>()
                .state
                .artists
                .firstWhere((a) => a.uid == artistModel.uid)
                .copyWith(feeds: feeds ?? []);
            context.read<ArtistBloc>().state.artists.forEach((artist) {
              if (artist.uid == artistModel.uid) {
                artist = updatedArtist;
              }
            });
            artistModel = artistModel.copyWith(feeds: feeds);
          }
        }
      },
      context,
    );
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            backgroundColor: Colors.transparent,
            expandedHeight: 200,
            floating: true,
            pinned: true,
            centerTitle: false,
            automaticallyImplyLeading: false,
            leading: Container(
              height: 65,
              width: 65,
              padding: const EdgeInsets.only(
                left: 12,
              ),
              child: GestureDetector(
                onTap: () => RouteUtils.pop(context),
                child: const Icon(
                  CupertinoIcons.chevron_back,
                ),
              ),
            ),
            flexibleSpace: ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        darkAppColors.backgroundColor
                            .withAlpha((255 * 0.7).toInt()),
                        darkAppColors.backgroundColor
                            .withAlpha((255 * 0.3).toInt()),
                      ],
                    ),
                    border: Border(
                      bottom: BorderSide(
                        color: Colors.white.withAlpha((255 * 0.2).toInt()),
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: FlexibleSpaceBar(
                    background: Padding(
                      padding: SizeUtils.horizontalPadding.copyWith(top: 100),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(100)),
                            child: CachedNetworkImage(
                              imageUrl: artistModel.photoURL,
                              width: 100,
                              height: 100,
                              maxHeightDiskCache: 200,
                              maxWidthDiskCache: 200,
                              fit: BoxFit.cover,
                            ),
                          ),
                          const SizedBox(width: 20),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '${artistModel.fullName}',
                                  style: AppTextStyles.heading2,
                                ),
                                const SizedBox(height: 8),
                                Padding(
                                  padding: const EdgeInsets.only(right: 10),
                                  child: Wrap(
                                    spacing: 6,
                                    runSpacing: 6,
                                    children: artistModel.tags
                                        .map(tagWidget)
                                        .toList(),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          if (_isLoading)
            SliverToBoxAdapter(
              child: buildShimmerGrid(
                top: 20,
              ),
            )
          else
            SliverPadding(
              padding: SizeUtils.horizontalPadding.copyWith(
                top: 20,
                bottom: 60,
              ),
              sliver: _buildFeedsGrid(),
            ),
        ],
      ),
    );
  }

  Widget _buildFeedsGrid() {
    final feeds = artistModel.feeds;
    if (feeds == null || feeds.isEmpty) {
      return SliverToBoxAdapter(
        child: SizedBox(
          height: MediaQuery.of(context).size.height * 0.5,
          child: const NoContentPage(
            icon: CupertinoIcons.photo_on_rectangle,
            title: 'No Posts Yet',
            description:
                'No posts here right now. Check back soon for more vibes!',
          ),
        ),
      );
    }

    return SliverGrid(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 5,
        crossAxisSpacing: 5,
        childAspectRatio: 1.2 / 2,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final feed = feeds[index];
          return LayoutBuilder(
            builder: (context, constraints) {
              return GestureDetector(
                onTap: () {
                  RouteUtils.pushNamed(
                    context,
                    RoutePaths.feedPlayingPage,
                    arguments: {
                      'initialIndex': index,
                      'feeds': feeds,
                      'title': vibeScoreDefinitions[feed.vibeScore]!['name'] ??
                          'Vibes',
                    },
                  );
                },
                child: FeedTile(feed: feed),
              );
            },
          );
        },
        childCount: feeds.length,
      ),
    );
  }
}
