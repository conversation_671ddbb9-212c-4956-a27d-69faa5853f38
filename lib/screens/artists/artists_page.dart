import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/logic/artists/bloc/artists_bloc.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/views/state/error_handler.dart';
import 'package:vibeo/views/state/something_went_wrong_screen.dart';

import 'package:vibeo/widgets/artists/artists_tile_shimmer.dart';
import 'package:vibeo/widgets/global/no_content_page.dart';
import 'package:vibeo/widgets/global/tag_widget.dart';

class ArtistsPage extends StatefulWidget {
  const ArtistsPage({super.key});

  @override
  State<ArtistsPage> createState() => _ArtistsPageState();
}

class _ArtistsPageState extends State<ArtistsPage> {
  @override
  void initState() {
    super.initState();
    fetchArtists();
  }

  void fetchArtists() {
    final state = context.read<ArtistBloc>().state;
    if (state.artists.isEmpty) {
      context.read<ArtistBloc>().add(FetchArtistsEvent(context));
    }
  }

  @override
  Widget build(BuildContext context) {
    return ErrorHandler(
      onRetry: fetchArtists,
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: BlocBuilder<ArtistBloc, ArtistState>(
          builder: (context, state) {
            if (state is ArtistsLoading && state.artists.isEmpty) {
              return GridView.builder(
                padding: SizeUtils.horizontalPadding.copyWith(
                  top: 28,
                  bottom: 100,
                ),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: 6, // Show 6 shimmer tiles
                itemBuilder: (context, index) => const ArtistTileShimmer(),
              );
            }

            if (state is ArtistsError && state.artists.isEmpty) {
              return Padding(
                padding: const EdgeInsets.only(top: 28),
                child: SomethingWentWrong(
                  onRetry: () {
                    context.read<ArtistBloc>().add(FetchArtistsEvent(context));
                  },
                ),
              );
            }

            if (state is ArtistsLoaded && state.artists.isEmpty) {
              return ListView(
                padding: const EdgeInsets.only(top: 28),
                physics: const AlwaysScrollableScrollPhysics(),
                children: const [
                  NoContentPage(
                    title: 'No Artists Found',
                    description:
                        'Oops, no artists here yet! Check back later for updates.',
                    icon: CupertinoIcons.person,
                  ),
                ],
              );
            }

            return GridView.builder(
              padding: SizeUtils.horizontalPadding.copyWith(
                top: 28,
                bottom: 100,
              ),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.75,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: state.artists.length,
              itemBuilder: (context, index) {
                final artist = state.artists[index];
                return GestureDetector(
                  onTap: () {
                    // Navigate to artist details page
                    RouteUtils.pushNamed(
                      context,
                      RoutePaths.artistsFeedsPage,
                      arguments: artist,
                    );
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.all(Radius.circular(16)),
                      boxShadow: AppShadowStyles.baseStyle,
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.all(Radius.circular(16)),
                      child: Stack(
                        children: [
                          // Image layer
                          Positioned.fill(
                            child: CachedNetworkImage(
                              imageUrl: artist.photoURL,
                              fit: BoxFit.cover,
                              alignment: Alignment.center,
                            ),
                          ),
                          // Gradient overlay
                          Positioned.fill(
                            child: Container(
                              decoration: const BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [Colors.transparent, Colors.black],
                                ),
                              ),
                            ),
                          ),
                          // Content
                          Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(12),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(
                                      artist.fullName,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 6),
                                    Wrap(
                                      spacing: 6,
                                      runSpacing: 6,
                                      children: artist.tags
                                          .getRange(0, 2)
                                          .map(tagWidget)
                                          .toList(),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
