import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/basket/cubit/basket_cubit.dart';
import 'package:vibeo/logic/cart/bloc/cart_bloc.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/screens/basket/earned_offers_page.dart';

import 'package:vibeo/services/basket/basket_storage_service.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/views/basket/redeem_offer_screen.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';

class BasketPage extends StatefulWidget {
  const BasketPage({super.key});

  @override
  State<BasketPage> createState() => _BasketPageState();
}

class _BasketPageState extends State<BasketPage>
    with SingleTickerProviderStateMixin {
  late TabController _controller;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  late BasketCubit _basketCubit;

  @override
  void initState() {
    super.initState();
    _controller = TabController(
      length: 2, // Updated to include redeemed offers tab
      vsync: this,
      initialIndex: 0,
    );

    // Add listener to update when tab changes
    _controller.addListener(_handleTabChange);

    // Load cart data
    context.read<CartBloc>().add(LoadCart());

    // Initialize BasketCubit
    final userID = context.read<UserBloc>().state.user!.uid;
    final basketStorageService = BasketStorageService(userID);
    _basketCubit = BasketCubit(basketStorageService: basketStorageService);
  }

  void _handleTabChange() {
    // Force rebuild when tab changes to ensure search query is passed
    if (_controller.indexIsChanging) {
      setState(() {});

      // If switching to the basket tab, refresh the basket items
      if (_controller.index == 0) {
        // Refresh the basket items using our persistent BasketCubit
        _basketCubit.loadBasketItems(forceRefresh: true);
      }
    }
  }

  @override
  void dispose() {
    _controller
      ..removeListener(_handleTabChange)
      ..dispose();
    _searchController.dispose();
    _basketCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SuperScaffold(
        appBar: SuperAppBar(
          backgroundColor:
              darkAppColors.secondaryBackgroundColor.withAlpha(100),
          border: Border(
            bottom: BorderSide(
              width: 0.1,
              color: Colors.white.withAlpha(120),
            ),
          ),
          bottom: SuperAppBarBottom(
            enabled: true,
            child: OffersTabPagesBar(controller: _controller),
          ),
          automaticallyImplyLeading: false,
          height: 20,
          title: const SizedBox(),
          largeTitle: SuperLargeTitle(
            enabled: true,
            largeTitle: 'Your Bag',
            textStyle: const TextStyle(
              fontFamily: 'PulpDisplay',
              fontSize: 30,
              color: Colors.white,
              inherit: false,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.none,
              letterSpacing: 0,
              wordSpacing: 0,
              height: 1.2,
            ),
          ),
          searchBar: SuperSearchBar(
            enabled: true,
            searchController: _searchController,
            placeholderText: 'Search venues or offers',
            onChanged: (query) {
              // Don't trim the query here as it might remove single-letter searches
              setState(() {
                _searchQuery = query;
              });
            },
            onSubmitted: (query) {
              // Don't trim the query here as it might remove single-letter searches
              setState(() {
                _searchQuery = query;
                // Clear focus when submitted
                FocusScope.of(context).unfocus();
              });
            },
            onFocused: (focused) {
              // When search bar loses focus, ensure UI updates
              if (!focused) {
                setState(() {});
              }
            },
            cancelTextStyle: TextStyle(
              color: darkAppColors.primary,
            ),
            resultBehavior: SearchBarResultBehavior.neverVisible,
          ),
        ),
        body: TabBarView(
          controller: _controller,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            BlocProvider<BasketCubit>.value(
              value: _basketCubit,
              child: RedeemOfferScreen(
                searchQuery: _searchQuery,
              ),
            ),
            EarnedOffersPage(
              searchQuery: _searchQuery,
            ),
          ],
        ),
      ),
    );
  }
}

class OffersTabPagesBar extends StatefulWidget implements PreferredSizeWidget {
  const OffersTabPagesBar({
    required this.controller,
    super.key,
  });

  final TabController controller;

  @override
  Size get preferredSize => const Size.fromHeight(kTextTabBarHeight);

  @override
  State<OffersTabPagesBar> createState() => _OffersTabPagesBarState();
}

class _OffersTabPagesBarState extends State<OffersTabPagesBar> {
  late BasketStorageService _basketStorageService;
  int _basketItemCount = 0;

  @override
  void initState() {
    super.initState();
    final userID = context.read<UserBloc>().state.user!.uid;
    _basketStorageService = BasketStorageService(userID);
    _loadBasketItemCount();

    // Listen for tab changes to refresh the basket count
    widget.controller.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_handleTabChange);
    super.dispose();
  }

  void _handleTabChange() {
    // Refresh basket count when tab changes
    if (widget.controller.index == 1) {
      // Basket tab
      _loadBasketItemCount();
    }
  }

  Future<void> _loadBasketItemCount() async {
    final count = await _basketStorageService.getBasketItemCount();
    if (mounted) {
      setState(() {
        _basketItemCount = count;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return TabBar(
      controller: widget.controller,
      indicatorColor: Colors.white,
      dividerHeight: 0.05,
      dividerColor: Colors.grey.withAlpha(128),
      labelColor: Colors.white,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      unselectedLabelColor: Colors.grey,
      padding: const EdgeInsets.only(left: 4),
      enableFeedback: true,
      indicatorWeight: 2,
      splashFactory: NoSplash.splashFactory,
      overlayColor: WidgetStateColor.transparent,
      physics: const ClampingScrollPhysics(),
      onTap: (index) {
        HapticFeedback.lightImpact();
      },
      labelPadding: kTabLabelPadding.copyWith(
        bottom: 10,
      ),
      tabs: [
        Tab(
          child: Row(
            children: [
              const Text('Bag'),
              if (_basketItemCount > 0)
                Container(
                  margin: const EdgeInsets.only(left: 4),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: darkAppColors.primary,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '$_basketItemCount',
                    style: const TextStyle(fontSize: 10),
                  ),
                ),
            ],
          ),
        ),
        const Tab(text: 'Redeemed'),
      ],
    );
  }
}

class EmptyCartView extends StatelessWidget {
  final String message;
  final String buttonText;

  const EmptyCartView({
    required this.message,
    required this.buttonText,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.shopping_cart,
            size: 64,
            color: Color.fromRGBO(255, 255, 255, 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: AppTextStyles.bodySmall.copyWith(
              color: const Color.fromRGBO(255, 255, 255, 0.7),
            ),
          ),
          const SizedBox(height: 24),
          HapticButton(
            onTap: () {
              RouteUtils.pushNamedAndRemoveUntil(
                context,
                RoutePaths.home,
                (route) => false,
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    darkAppColors.deepPurple,
                    darkAppColors.purpleShade,
                  ],
                ),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Text(
                buttonText,
                style: AppTextStyles.bodysmallBold.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
