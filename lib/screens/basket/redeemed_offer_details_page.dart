// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:intl/intl.dart';
// import 'package:vibeo/logic/user/bloc/user_bloc.dart';
// import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
// import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
// import 'package:vibeo/models/offer/offer_model.dart';
// import 'package:vibeo/routes/route.dart';
// import 'package:vibeo/services/redeemed_offers/redeemed_offers_service.dart';
// import 'package:vibeo/themes/constant_theme.dart';
// import 'package:vibeo/themes/text_theme.dart';
// import 'package:vibeo/utils/size_utils.dart';
// import 'package:vibeo/widgets/haptic_feedback.dart';
// import 'dart:async';

// class RedeemedOfferDetailsPage extends StatefulWidget {
//   final OfferModel offer;
//   final String venueID;

//   const RedeemedOfferDetailsPage({
//     required this.offer,
//     required this.venueID,
//     super.key,
//   });

//   @override
//   State<RedeemedOfferDetailsPage> createState() =>
//       _RedeemedOfferDetailsPageState();
// }

// class _RedeemedOfferDetailsPageState extends State<RedeemedOfferDetailsPage> {
//   late String _redemptionCode;
//   late String _redemptionDate;
//   late String _userId;
//   late RedeemedOffersService _redeemedOffersService;
//   final int _quantity = 1; // Default quantity
//   // VIBEPOINTS FEATURE COMMENTED OUT
//   // late int _vibePointsEarned; // Vibe points earned for this offer

//   @override
//   void initState() {
//     super.initState();
//     _userId = context.read<UserBloc>().state.user!.uid;
//     _redemptionCode = _generateRedemptionCode();
//     _redemptionDate =
//         DateFormat("MMMM d, yyyy 'at' h:mm a").format(DateTime.now());

//     // Initialize redeemed offers service
//     _redeemedOffersService = RedeemedOffersService(_userId);

//     // VIBEPOINTS FEATURE COMMENTED OUT
//     // Calculate vibe points earned
//     // _vibePointsEarned = _calculateVibePointsForOffer(widget.offer);

//     // Save this offer as redeemed
//     _saveRedeemedOffer();
//   }

//   // VIBEPOINTS FEATURE COMMENTED OUT
//   /*
//   int _calculateVibePointsForOffer(OfferModel offer) {
//     // Determine points based on offer category and type according to the points system
//     switch (offer.voucherCategory) {
//       case VoucherCategory.ONGOING:
//         // Base Campaign - Everyday Perk
//         return 75;
//       case VoucherCategory.TIMEBASED:
//         // Time Boost - Limited Hour Deal
//         return 100;
//       case VoucherCategory.DROPS:
//         // Flash/Mystery Drop
//         return 150;
//       default:
//         // Check if it's a platform unlockable (costs vibe points)
//         if (offer.redemptionType == OfferRedemptionType.vibePointsRedeem) {
//           // Platform Unlockable - Vibeo Exclusive (costs points)
//           return -offer.vibePointsCost; // Negative because user spends points
//         }

//         // Default to base campaign value if no specific category matches
//         return 75;
//     }
//   }
//   */

//   Future<void> _saveRedeemedOffer() async {
//     // Create a copy of the offer with isRedeemed set to true
//     final redeemedOffer = widget.offer.copyWith(isRedeemed: true);

//     // Save to storage
//     await _redeemedOffersService.saveRedeemedOffer(
//       redeemedOffer,
//       widget.venueID,
//       _redemptionCode,
//     );
//   }

//   String _generateRedemptionCode() {
//     final now = DateTime.now();
//     final timestamp = now.millisecondsSinceEpoch.toString().substring(7, 13);
//     final userIdLast4 = _userId.substring(_userId.length - 4);
//     return '$timestamp$userIdLast4'.toUpperCase();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: darkAppColors.backgroundColor,
//       appBar: const CupertinoNavigationBar(
//         backgroundColor: Colors.transparent,
//         middle: Text(
//           'Offer Details',
//           style: TextStyle(
//             color: Colors.white,
//           ),
//         ),
//         previousPageTitle: 'Back',
//       ),
//       floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
//       floatingActionButton: Padding(
//         padding: SizeUtils.horizontalPadding,
//         child: Row(
//           children: [
//             // View Venue button
//             Expanded(
//               child: HapticButton(
//                 onTap: () async {
//                   // Show loading indicator
//                   unawaited(
//                     showDialog(
//                       context: context,
//                       barrierDismissible: false,
//                       builder: (context) => const Center(
//                         child: CircularProgressIndicator(),
//                       ),
//                     ),
//                   );

//                   try {
//                     // Fetch venue details
//                     final venueRepository = VenueRepository();
//                     final venue = await venueRepository.fetchVenueByID(
//                       id: widget.venueID,
//                     );

//                     if (context.mounted) {
//                       // Close loading dialog
//                       Navigator.pop(context);

//                       // Add venue to bloc
//                       context.read<VenueBloc>().add(
//                             AddVenueEvent(venue),
//                           );

//                       // Navigate to venue page
//                       await RouteUtils.pushNamed(
//                         context,
//                         RoutePaths.venueDescPage,
//                         arguments: {
//                           'venue': venue,
//                           'showOffersDirectly': true,
//                         },
//                       );
//                     }
//                   } catch (e) {
//                     // Close loading dialog and show error
//                     if (context.mounted) {
//                       Navigator.pop(context);
//                       ScaffoldMessenger.of(context).showSnackBar(
//                         SnackBar(content: Text('Error loading venue: $e')),
//                       );
//                     }
//                   }
//                 },
//                 child: Container(
//                   height: 50,
//                   margin: const EdgeInsets.only(right: 8),
//                   decoration: BoxDecoration(
//                     color: Colors.black.withAlpha(102),
//                     borderRadius: BorderRadius.circular(12),
//                     border: Border.all(
//                       color: Colors.white.withAlpha(26),
//                       width: 1,
//                     ),
//                   ),
//                   child: Center(
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         const Icon(
//                           Icons.location_on,
//                           color: Colors.white,
//                           size: 18,
//                         ),
//                         const SizedBox(width: 8),
//                         Text(
//                           'View Venue',
//                           style: AppTextStyles.bodysmallBold.copyWith(
//                             color: Colors.white,
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),

//             // Done button
//             Expanded(
//               child: HapticButton(
//                 onTap: () {
//                   RouteUtils.pop(context);
//                 },
//                 child: Container(
//                   height: 50,
//                   margin: const EdgeInsets.only(left: 8),
//                   decoration: BoxDecoration(
//                     color: darkAppColors.deepPurple,
//                     borderRadius: BorderRadius.circular(12),
//                   ),
//                   child: Center(
//                     child: Text(
//                       'Done',
//                       style: AppTextStyles.bodysmallBold.copyWith(
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//       body: SafeArea(
//         child: SingleChildScrollView(
//           padding: SizeUtils.horizontalPadding.copyWith(
//             top: 12,
//             bottom: 100,
//           ),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.center,
//             children: [
//               // VIBEPOINTS FEATURE COMMENTED OUT
//               // Vibe points earned container removed

//               // const SizedBox(height: 16),

//               Text(
//                 'Redeemed on $_redemptionDate',
//                 style:
//                     AppTextStyles.bodySmaller.copyWith(color: Colors.white60),
//               ),

//               const Padding(
//                 padding: EdgeInsets.symmetric(vertical: 20),
//                 child: Divider(color: Colors.white24),
//               ),

//               // Offer description section
//               Align(
//                 alignment: Alignment.centerLeft,
//                 child: Text(
//                   'Offer Description',
//                   style: AppTextStyles.bodysmallBold.copyWith(fontSize: 18),
//                 ),
//               ),

//               const SizedBox(height: 12),

//               Container(
//                 width: double.infinity,
//                 padding: const EdgeInsets.all(16),
//                 decoration: BoxDecoration(
//                   color: darkAppColors.secondaryBackgroundColor.withAlpha(128),
//                   borderRadius: BorderRadius.circular(12),
//                   border: Border.all(
//                     color: Colors.white.withAlpha(25),
//                     width: 0.5,
//                   ),
//                 ),
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: [
//                     Text(
//                       widget.offer.title,
//                       style: AppTextStyles.bodysmallBold.copyWith(
//                         fontSize: 16,
//                       ),
//                     ),
//                     const SizedBox(height: 8),
//                     Text(
//                       widget.offer.description,
//                       style: AppTextStyles.bodySmall.copyWith(
//                         color: Colors.white.withAlpha(220),
//                       ),
//                     ),
//                     if (widget.offer.info != null) ...[
//                       const SizedBox(height: 12),
//                       Text(
//                         widget.offer.info!,
//                         style: AppTextStyles.bodySmaller.copyWith(
//                           color: Colors.white.withAlpha(179),
//                           fontStyle: FontStyle.italic,
//                         ),
//                       ),
//                     ],
//                   ],
//                 ),
//               ),

//               const Padding(
//                 padding: EdgeInsets.symmetric(vertical: 20),
//                 child: Divider(color: Colors.white24),
//               ),

//               // Order details section
//               Align(
//                 alignment: Alignment.centerLeft,
//                 child: Text(
//                   'Order Details',
//                   style: AppTextStyles.bodysmallBold.copyWith(fontSize: 18),
//                 ),
//               ),

//               const SizedBox(height: 16),

//               _buildDetailRow('Venue', widget.offer.venueName),
//               const Padding(
//                 padding: EdgeInsets.symmetric(vertical: 8),
//                 child: Divider(color: Colors.white12, height: 1),
//               ),

//               _buildDetailRow('Quantity', '$_quantity'),

//               const Padding(
//                 padding: EdgeInsets.symmetric(vertical: 20),
//                 child: Divider(color: Colors.white24),
//               ),

//               // Price section
//               Align(
//                 alignment: Alignment.centerLeft,
//                 child: Text(
//                   'Price Details',
//                   style: AppTextStyles.bodysmallBold.copyWith(fontSize: 18),
//                 ),
//               ),

//               const SizedBox(height: 16),

//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Text(
//                     'Original Price',
//                     style:
//                         AppTextStyles.bodySmall.copyWith(color: Colors.white70),
//                   ),
//                   if (widget.offer.discountedValue != null &&
//                       widget.offer.offerValue != 0)
//                     Text(
//                       '\$${widget.offer.offerValue}',
//                       style: AppTextStyles.bodySmall.copyWith(
//                         decoration: TextDecoration.lineThrough,
//                         color: Colors.white60,
//                       ),
//                     )
//                   else
//                     Text(
//                       '\$${widget.offer.offerValue}',
//                       style: AppTextStyles.bodySmall,
//                     ),
//                 ],
//               ),

//               const SizedBox(height: 8),

//               if (widget.offer.discountedValue != null &&
//                   widget.offer.offerValue != 0) ...[
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       'Discount',
//                       style: AppTextStyles.bodySmall
//                           .copyWith(color: Colors.white70),
//                     ),
//                     Text(
//                       '-\$${widget.offer.offerValue! - double.parse(widget.offer.discountedValue.toString())}',
//                       style:
//                           AppTextStyles.bodySmall.copyWith(color: Colors.green),
//                     ),
//                   ],
//                 ),
//                 const Padding(
//                   padding: EdgeInsets.symmetric(vertical: 8),
//                   child: Divider(color: Colors.white12, height: 1),
//                 ),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       'Final Price',
//                       style: AppTextStyles.bodysmallBold,
//                     ),
//                     Text(
//                       '${widget.offer.discountedValue}',
//                       style: AppTextStyles.bodysmallBold.copyWith(
//                         color: Colors.green,
//                         fontSize: 18,
//                       ),
//                     ),
//                   ],
//                 ),
//                 const SizedBox(height: 8),
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                   children: [
//                     Text(
//                       'Total (Qty: $_quantity)',
//                       style: AppTextStyles.bodysmallBold,
//                     ),
//                     Text(
//                       '${widget.offer.discountedValue}',
//                       style: AppTextStyles.bodysmallBold.copyWith(
//                         color: Colors.green,
//                         fontSize: 18,
//                       ),
//                     ),
//                   ],
//                 ),
//               ],
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildDetailRow(String label, String value) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(vertical: 4),
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Text(
//             label,
//             style: AppTextStyles.bodySmall.copyWith(color: Colors.white70),
//           ),
//           const SizedBox(width: 16),
//           Flexible(
//             child: Text(
//               value,
//               style: AppTextStyles.bodySmall,
//               textAlign: TextAlign.end,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
