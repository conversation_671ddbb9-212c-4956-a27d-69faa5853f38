import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_cupertino_navigation_bar/super_cupertino_navigation_bar.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/venue/venue_model.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/logic/offer/bloc/offer_repo_impl.dart';
import 'package:vibeo/services/basket/basket_storage_service.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/widgets/help/redemption_help_dialog.dart';
import 'package:vibeo/widgets/offer/offer_tile.dart';
import 'package:vibeo/components/component.dart';

class CategorizedOffersPage extends StatefulWidget {
  final VenueModel venue;

  const CategorizedOffersPage({
    required this.venue,
    super.key,
  });

  @override
  State<CategorizedOffersPage> createState() => _CategorizedOffersPageState();
}

class _CategorizedOffersPageState extends State<CategorizedOffersPage>
    with SingleTickerProviderStateMixin {
  // Lists to hold categorized offers
  final List<OfferModel> _drops = [];
  final List<OfferModel> _timeBasedOffers = [];
  final List<OfferModel> _redeemingOffers = [];
  final List<OfferModel> _expiringOffers = [];
  final List<OfferModel> _regularOffers = [];
  final List<OfferModel> _moreOffers = [];
  final List<OfferModel> _redeemedOffers = [];

  // State variables
  bool _isLoading = true;
  bool _hasError = false;
  late BasketStorageService _basketStorageService;
  late OfferRepository _offerRepository;

  // Store fetched offers separately from widget.venue
  List<OfferModel> _venueOffers = [];

  @override
  void initState() {
    super.initState();

    // Initialize repositories
    _offerRepository = OfferRepository();

    // Initialize storage service with user ID
    final userID = context.read<UserBloc>().state.user!.uid;
    _basketStorageService = BasketStorageService(userID);

    // Load offers for this venue
    _loadOffers();
  }

  /// Load offers for this venue from the basket storage
  Future<void> _loadOffers() async {
    setState(() {
      _isLoading = true;
      _hasError = false;

      // Clear fetched offers
      _venueOffers = [];
    });

    try {
      // Get offer IDs for this venue from basket storage
      final user = context.read<UserBloc>().state.user;
      final offerIDs =
          await _basketStorageService.getOfferIDsForVenue(widget.venue.id);

      if (offerIDs.isEmpty) {
        // If there are no offers in the basket for this venue, use what we have
        setState(() {
          _isLoading = false;
          _categorizeOffers();
        });
        return;
      }

      AppLogger.debug(
        'Found ${offerIDs.length} offers for venue ${widget.venue.id}',
      );

      // Fetch detailed offer information
      final offers = await _offerRepository.fetchOffersByList(
        offerIDs,
        user!.uid,
        user.email,
      );

      AppLogger.debug(
        'Fetched ${offers.length} offer details for venue ${widget.venue.id}',
      );

      if (mounted) {
        setState(() {
          // Store the fetched offers in a local variable to use for categorization
          // We don't need to update the widget.venue since it's final
          _venueOffers = offers;

          // Categorize the offers
          _categorizeOffers();

          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Error loading offers for venue ${widget.venue.id}: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  void _categorizeOffers() {
    // Use the fetched offers if available, otherwise fall back to widget.venue.offers
    final offers =
        _venueOffers.isNotEmpty ? _venueOffers : (widget.venue.offers ?? []);

    // Clear previous categorized offers
    _drops.clear();
    _timeBasedOffers.clear();
    _redeemingOffers.clear();
    _expiringOffers.clear();
    _regularOffers.clear();
    _moreOffers.clear();
    _redeemedOffers.clear();

    final newOffers = offers;

    for (final offer in newOffers) {
      // Collect redeemed offers in a separate list
      if (offer.isRedeemedToday) {
        _redeemedOffers.add(offer);
        continue;
      }

      // Check if it's a locked offer (for the "Locked Offers" section)
      if (offer.isLocked) {
        _moreOffers.add(offer);
        continue;
      }

      // Check if it's a drop (special time-limited offer)
      if (offer.voucherCategory == VoucherCategory.DROPS) {
        _drops.add(offer);
        continue;
      } else if (offer.voucherType == VoucherType.REGULAR) {
        _regularOffers.add(offer);
        continue;
      } else if (offer.voucherCategory == VoucherCategory.ONGOING) {
        _redeemingOffers.add(offer);
        continue;
      } else if (offer.voucherCategory == VoucherCategory.TIMEBASED) {
        _timeBasedOffers.add(offer);
        continue;
      } else if (offer.voucherCategory == VoucherCategory.LIMITED) {
        _expiringOffers.add(offer);
        continue;
      } else {
        _regularOffers.add(offer);
        continue;
      }
    }

    // Sort offers by priority

    _timeBasedOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _redeemingOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _regularOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _moreOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _redeemedOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SuperScaffold(
        appBar: SuperAppBar(
          backgroundColor:
              darkAppColors.secondaryBackgroundColor.withAlpha(100),
          border: Border(
            bottom: BorderSide(
              width: 0.1,
              color: Colors.white.withAlpha(120),
            ),
          ),
          height: 40,
          title: Text('Offers', style: AppTextStyles.title),
          largeTitle: SuperLargeTitle(enabled: false),
          actions: TextButton.icon(
            style: TextButton.styleFrom(alignment: Alignment.topCenter),
            onPressed: () {
              RedemptionHelpDialog.show(context);
            },
            label: Text(
              'Get Help',
              style: AppTextStyles.bodySmaller.copyWith(
                color: Colors.white70,
                fontSize: 13,
              ),
            ),
            icon: const Icon(
              CupertinoIcons.question_circle,
              color: Colors.white70,
              size: 14,
            ),
          ),
          searchBar: SuperSearchBar(enabled: false),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                darkAppColors.backgroundColor,
                darkAppColors.backgroundColor.withAlpha(240),
              ],
            ),
          ),
          child: _isLoading
              ? const Center(child: LoadingWidget())
              : _hasError
                  ? _buildErrorView()
                  : ListView(
                      padding: SizeUtils.horizontalPadding.copyWith(
                        top: 24,
                        bottom: 40,
                      ),
                      children: [
                        Text(
                          "${widget.venue.name}'s Offers",
                          style: AppTextStyles.body.copyWith(fontSize: 20),
                        ),
                        const SizedBox(height: 8),

                        // Time-based Offers section
                        if (_timeBasedOffers.isNotEmpty)
                          _buildSection(
                            'Limited-Time Perks ',
                            _timeBasedOffers,
                            CupertinoIcons.time,
                            [
                              Colors.orange.shade400,
                              Colors.deepOrange.shade600,
                            ],
                          ),

                        // Redeeming Offers section
                        if (_redeemingOffers.isNotEmpty)
                          _buildSection(
                            'Ongoing Deals',
                            _redeemingOffers,
                            CupertinoIcons.gift,
                            [Colors.green.shade400, Colors.teal.shade600],
                          ),

                        // Drops section
                        if (_drops.isNotEmpty)
                          _buildSection(
                            'Drops',
                            _drops,
                            Icons.flash_on,
                            [
                              Colors.purple.shade400,
                              Colors.deepPurple.shade600,
                            ],
                          ),

                        // Regular Offers section
                        if (_regularOffers.isNotEmpty)
                          _buildSection(
                            'Spotted Deals',
                            _regularOffers,
                            CupertinoIcons.tag,
                            [Colors.blue.shade400, Colors.indigo.shade600],
                          ),

                        if (_moreOffers.isNotEmpty)
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Recommended for you',
                                style: AppTextStyles.heading2,
                              ),
                              if (_moreOffers.isNotEmpty)
                                _buildSection(
                                  'Unlockable Offers',
                                  _moreOffers,
                                  CupertinoIcons.lock,
                                  [Colors.pink.shade400, Colors.red.shade600],
                                ),
                            ],
                          ),

                        // Redeemed Offers section
                        if (_redeemedOffers.isNotEmpty)
                          _buildSection(
                            'Redeemed',
                            _redeemedOffers,
                            CupertinoIcons.checkmark_seal,
                            [Colors.teal.shade400, Colors.cyan.shade600],
                          ),

                        // Show a message if no offers are available
                        if (_timeBasedOffers.isEmpty &&
                            _redeemingOffers.isEmpty &&
                            _drops.isEmpty &&
                            _regularOffers.isEmpty &&
                            _moreOffers.isEmpty &&
                            _redeemedOffers.isEmpty)
                          _buildEmptyOffersView(),
                      ],
                    ),
        ),
      ),
    );
  }

  /// Build error view with retry button
  Widget _buildErrorView() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        margin: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor.withAlpha(150),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withAlpha(25),
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.exclamationmark_circle,
              size: 64,
              color: Color.fromRGBO(255, 100, 100, 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load offers',
              style: AppTextStyles.body.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'There was a problem loading the offers for this venue. Please try again.',
              style: AppTextStyles.bodySmall.copyWith(
                color: const Color.fromRGBO(255, 255, 255, 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadOffers,
              style: ElevatedButton.styleFrom(
                backgroundColor: darkAppColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build empty offers view
  Widget _buildEmptyOffersView() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        margin: const EdgeInsets.symmetric(vertical: 32),
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor.withAlpha(100),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withAlpha(25),
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              CupertinoIcons.gift,
              size: 64,
              color: Color.fromRGBO(255, 255, 255, 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No offers available',
              style: AppTextStyles.body.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'There are no offers in your basket for this venue.',
              style: AppTextStyles.bodySmall.copyWith(
                color: const Color.fromRGBO(255, 255, 255, 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    List<OfferModel> offers,
    IconData icon,
    List<Color> gradientColors,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with glassmorphic design and animation
        Container(
          margin: const EdgeInsets.only(bottom: 16, top: 8),

          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(16),
          //   border: Border.all(
          //     color: gradientColors[0].withAlpha(76),
          //     width: 1,
          //   ),
          //   boxShadow: [
          //     BoxShadow(
          //       color: gradientColors[0].withAlpha(30),
          //       blurRadius: 8,
          //       offset: const Offset(0, 2),
          //     ),
          //   ],
          // ),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: gradientColors[1].withAlpha(40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              // Count badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: gradientColors[1].withAlpha(51),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: gradientColors[1].withAlpha(102),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${offers.length}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        ListView.builder(
          itemBuilder: (context, index) {
            final offer = offers[index];
            return Dismissible(
              key: Key('offer_${offer.id}'),
              direction: DismissDirection.endToStart,
              background: Container(
                alignment: Alignment.centerRight,
                padding: const EdgeInsets.only(right: 20),
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(179), // 0.7 opacity
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  CupertinoIcons.delete,
                  color: Colors.white,
                ),
              ),
              confirmDismiss: (direction) async {
                // Show confirmation dialog
                return showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      backgroundColor: darkAppColors.secondaryBackgroundColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                        side: BorderSide(
                          color: Colors.white.withAlpha(30),
                          width: 1,
                        ),
                      ),
                      title: Text(
                        'Remove Offer',
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      content: Text(
                        'Are you sure you want to remove this offer from your cart?',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                      actions: <Widget>[
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: Text(
                            'Cancel',
                            style: TextStyle(color: darkAppColors.lightColor),
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: const Text(
                            'Remove',
                            style: TextStyle(color: Colors.redAccent),
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
              onDismissed: (direction) async {
                // Remove the offer from the basket
                await _basketStorageService.removeOfferFromBasket(
                  widget.venue.id,
                  offer.id,
                );

                // Remove the offer from the list
                setState(() {
                  offers.removeAt(index);
                });

                // Show a snackbar
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '${offer.title} removed from cart',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: darkAppColors.secondaryBackgroundColor,
                      action: SnackBarAction(
                        label: 'Undo',
                        textColor: Colors.white,
                        onPressed: () async {
                          // Add the offer back to the basket
                          await _basketStorageService.addOfferToBasket(
                            widget.venue.id,
                            offer.id,
                          );

                          // Reload offers
                          await _loadOffers();
                        },
                      ),
                    ),
                  );
                }
              },
              child: OfferTile(
                offer: offer,
                canNavigate: true,
                canRedeem: true,
                canAddToCart: false,
              ),
            );
          },
          itemCount: offers.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(bottom: 16),
        ),
      ],
    );
  }
}
