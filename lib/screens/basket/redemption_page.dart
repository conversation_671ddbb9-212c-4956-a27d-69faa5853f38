import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/logic/redemption/cubit/redemption_cubit.dart';
import 'package:vibeo/logic/redemption/cubit/redemption_state.dart';

import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/offer/offer_model.dart';

import 'package:vibeo/screens/basket/redemption_summary_page.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/widgets/basket/get_help_button.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class RedemptionPage extends StatefulWidget {
  final OfferModel offer;
  final String venueID;

  const RedemptionPage({
    required this.offer,
    required this.venueID,
    super.key,
  });

  @override
  State<RedemptionPage> createState() => _RedemptionPageState();
}

class _RedemptionPageState extends State<RedemptionPage>
    with SingleTickerProviderStateMixin {
  final List<String> _pinValues = ['', '', '', '', ''];
  int _currentIndex = 0;

  bool _isError = false;
  String _errorMessage = '';
  bool _isOrderSummaryExpanded = false;
  bool _isProcessing = false;

  late AnimationController _animationController;
  late Animation<double> _shakeAnimation;

  @override
  void initState() {
    super.initState();
    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _shakeAnimation = Tween<double>(begin: 0, end: 10)
        .chain(CurveTween(curve: Curves.elasticIn))
        .animate(_animationController)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _animationController.reverse();
        }
      });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _addDigit(String digit) {
    if (_currentIndex < 5) {
      setState(() {
        _pinValues[_currentIndex] = digit;
        _currentIndex++;
        _isError = false;
        _errorMessage = '';
      });

      // Automatically verify when all digits are entered
      if (_currentIndex == 5) {
        _verifyPin();
      }
    }
  }

  void _removeDigit() {
    if (_currentIndex > 0) {
      setState(() {
        _currentIndex--;
        _pinValues[_currentIndex] = '';
        _isError = false;
        _errorMessage = '';
      });
    }
  }

  void _clearPin() {
    setState(() {
      for (int i = 0; i < 5; i++) {
        _pinValues[i] = '';
      }
      _currentIndex = 0;
      _isError = false;
      _errorMessage = '';
    });
  }

  Future<void> _verifyPin() async {
    setState(() {
      _isProcessing = true;
    });

    final pin = _pinValues.join();
    AppLogger.debug('Verifying PIN: $pin for offer: ${widget.offer.id}');

    try {
      // Step 1: Get the current user
      AppLogger.debug('Getting current user');
      final userState = context.read<UserBloc>().state;
      if (userState is! UserExists) {
        AppLogger.debug('User not logged in');
        setState(() {
          _isError = true;
          _errorMessage = 'User not logged in. Please log in and try again.';
          _isProcessing = false;
        });
        return;
      }

      final user = userState.user!;
      AppLogger.debug('User found: ${user.uid}, email: ${user.email}');

      // Get the RedemptionCubit from context
      AppLogger.debug('Getting RedemptionCubit from context');
      final redemptionCubit = context.read<RedemptionCubit>();
      AppLogger.debug('RedemptionCubit obtained successfully');

      // Step 2: Initiate redemption
      AppLogger.debug('Initiating redemption for offer: ${widget.offer.id}');
      await redemptionCubit.initiateRedemption(
        offerId: widget.offer.id,
        userId: user.uid,
        userEmail: user.email,
      );
      AppLogger.debug('Redemption initiated successfully');

      // Step 3: Confirm redemption with PIN
      AppLogger.debug('Checking redemption state');
      final redemptionState = redemptionCubit.state;
      AppLogger.debug(
        'Current redemption state: ${redemptionState.runtimeType}',
      );

      if (redemptionState is RedemptionInitiated) {
        final uniqueCode = redemptionState.response.uniqueCode;
        AppLogger.debug('Got unique code: $uniqueCode');

        // Confirm redemption with the PIN
        AppLogger.debug('Confirming redemption with PIN');
        await redemptionCubit.confirmRedemptionPin(
          uniqueCode: uniqueCode,
          pin: pin,
        );
        AppLogger.debug('Redemption confirmation request sent');

        // Step 4: Handle confirmation result
        AppLogger.debug('Checking confirmation state');
        final confirmationState = redemptionCubit.state;
        AppLogger.debug(
          'Current confirmation state: ${confirmationState.runtimeType}',
        );

        if (confirmationState is RedemptionConfirmed) {
          AppLogger.debug('Redemption confirmed successfully');

          // Navigate to the summary page
          if (mounted) {
            AppLogger.debug('Navigating to summary page');
            await Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => RedemptionSummaryPage(
                  offer: widget.offer,
                  venueID: widget.venueID,
                  confirmation: confirmationState.confirmation,
                ),
              ),
            );
          }
        } else if (confirmationState is RedemptionError) {
          // Handle error
          AppLogger.debug(
            'Redemption confirmation error: ${confirmationState.message}',
          );
          setState(() {
            _isError = true;
            _errorMessage = confirmationState.message;
            _pinValues.fillRange(0, 5, '');
            _currentIndex = 0;
            _isProcessing = false;
          });
          await _animationController.forward();
        } else {
          AppLogger.debug(
            'Unexpected confirmation state: ${confirmationState.runtimeType}',
          );
          setState(() {
            _isError = true;
            _errorMessage =
                'Unexpected state after confirmation. Please try again.';
            _pinValues.fillRange(0, 5, '');
            _currentIndex = 0;
            _isProcessing = false;
          });
          await _animationController.forward();
        }
      } else if (redemptionState is RedemptionError) {
        // Handle error
        AppLogger.debug(
          'Redemption initiation error: ${redemptionState.message}',
        );
        setState(() {
          _isError = true;
          _errorMessage = redemptionState.message;
          _pinValues.fillRange(0, 5, '');
          _currentIndex = 0;
          _isProcessing = false;
        });
        await _animationController.forward();
      } else {
        AppLogger.debug(
          'Unexpected redemption state: ${redemptionState.runtimeType}',
        );
        setState(() {
          _isError = true;
          _errorMessage =
              'Unexpected state after initiation. Please try again.';
          _pinValues.fillRange(0, 5, '');
          _currentIndex = 0;
          _isProcessing = false;
        });
        await _animationController.forward();
      }
    } catch (e) {
      // Handle unexpected errors with more detailed message
      AppLogger.debug(
        'Exception caught during redemption process',
      );
      setState(() {
        _isError = true;
        // Provide more specific error information
        if (e.toString().contains('network')) {
          _errorMessage =
              'Network error. Please check your connection and try again.';
        } else if (e.toString().contains('timeout')) {
          _errorMessage = 'Request timed out. Please try again.';
        } else if (e.toString().contains('401') ||
            e.toString().contains('unauthorized')) {
          _errorMessage = 'Authentication error. Please log in again.';
        } else if (e.toString().contains('404')) {
          _errorMessage = 'Offer not found or expired.';
        } else if (e.toString().contains('500')) {
          _errorMessage = 'Server error. Please try again later.';
        } else {
          _errorMessage =
              'Redemption error: Please check your PIN and try again.';
        }
        _pinValues.fillRange(0, 5, '');
        _currentIndex = 0;
        _isProcessing = false;
      });
      await _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) => Scaffold(
        backgroundColor: darkAppColors.backgroundColor,
        appBar: CupertinoNavigationBar(
          backgroundColor: Colors.transparent,
          middle: const Text(
            'Redeem Offer',
            style: TextStyle(
              color: Colors.white,
            ),
          ),
          previousPageTitle: 'Back',
          trailing: buildGetHelpButton(context),
        ),
        body: SafeArea(
          child: Stack(
            children: [
              // Main content
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),

                    // Header
                    Text(
                      'Enter Redemption PIN',
                      style: AppTextStyles.title,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Ask the venue staff for a 5-digit PIN to redeem your offer',
                      textAlign: TextAlign.center,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 40),

                    // PIN display or loading indicator
                    if (_isProcessing)
                      const Center(
                        child: Column(
                          children: [
                            LoadingWidget(),
                            SizedBox(height: 16),
                            Text(
                              'Processing redemption...',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      AnimatedBuilder(
                        animation: _shakeAnimation,
                        builder: (context, child) {
                          return Transform.translate(
                            offset: Offset(
                              _isError ? _shakeAnimation.value : 0,
                              0,
                            ),
                            child: child,
                          );
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(
                            5,
                            (index) => AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              margin: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: _pinValues[index].isNotEmpty
                                    ? Colors.deepPurpleAccent.withAlpha(80)
                                    : Colors.white12,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: _pinValues[index].isNotEmpty
                                      ? darkAppColors.deepPurple
                                      : Colors.transparent,
                                  width: 1,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  _pinValues[index],
                                  style: const TextStyle(
                                    fontSize: 28,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                    // Error message
                    if (_isError)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Text(
                          _errorMessage,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                          ),
                        ),
                      ),

                    const Spacer(),

                    // Custom keypad (disabled when processing)
                    if (_isProcessing)
                      const SizedBox(height: 240)
                    else
                      _buildKeypad(),

                    const SizedBox(height: 20),
                  ],
                ),
              ),

              // Order summary overlay (shown when expanded)
              if (_isOrderSummaryExpanded)
                Positioned.fill(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _isOrderSummaryExpanded = false;
                      });
                    },
                    behavior: HitTestBehavior.translucent,
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                      child: ColoredBox(
                        color: Colors.black.withOpacity(0.5),
                        child: Center(
                          child: AnimatedOpacity(
                            duration: const Duration(milliseconds: 200),
                            opacity: _isOrderSummaryExpanded ? 1.0 : 0.0,
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.85,
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    darkAppColors.secondaryBackgroundColor
                                        .withOpacity(0.9),
                                    darkAppColors.secondaryBackgroundColor
                                        .withOpacity(0.7),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 15,
                                    spreadRadius: 5,
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Title with close button
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Order Summary',
                                        style: AppTextStyles.headingTitle
                                            .copyWith(fontSize: 20),
                                      ),
                                      IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _isOrderSummaryExpanded = false;
                                          });
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.xmark_circle_fill,
                                          color: Colors.white70,
                                          size: 24,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Divider(
                                    color: Colors.white24,
                                    height: 24,
                                  ),

                                  // Offer details
                                  Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.circular(10),
                                        child: Image.network(
                                          widget.offer.imageLink,
                                          width: 60,
                                          height: 60,
                                          fit: BoxFit.cover,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                            width: 60,
                                            height: 60,
                                            color: Colors.grey[800],
                                            child: const Icon(
                                              Icons.image_not_supported,
                                              color: Colors.white54,
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              widget.offer.title,
                                              style:
                                                  AppTextStyles.bodysmallBold,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              widget.offer.venueName,
                                              style: AppTextStyles.bodySmaller
                                                  .copyWith(
                                                color: Colors.white70,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),

                                  // Price details
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Quantity:',
                                        style: AppTextStyles.bodySmall
                                            .copyWith(color: Colors.white70),
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: darkAppColors.deepPurple
                                              .withOpacity(0.3),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          'x1',
                                          style: AppTextStyles.bodysmallBold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 8),

                                  if (widget.offer.discountedValue != null) ...[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Original Price:',
                                          style:
                                              AppTextStyles.bodySmall.copyWith(
                                            color: Colors.white70,
                                          ),
                                        ),
                                        Text(
                                          '\$${widget.offer.offerValue}',
                                          style:
                                              AppTextStyles.bodySmall.copyWith(
                                            decoration:
                                                TextDecoration.lineThrough,
                                            color: Colors.white60,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'You Paid:',
                                          style:
                                              AppTextStyles.bodySmall.copyWith(
                                            color: Colors.white70,
                                          ),
                                        ),
                                        ShaderMask(
                                          blendMode: BlendMode.srcIn,
                                          shaderCallback: (bounds) =>
                                              const LinearGradient(
                                            colors: [
                                              Colors.greenAccent,
                                              Colors.lightGreenAccent,
                                            ],
                                          ).createShader(
                                            Rect.fromLTWH(
                                              0,
                                              0,
                                              bounds.width,
                                              bounds.height,
                                            ),
                                          ),
                                          child: Text(
                                            '${widget.offer.discountedValue}',
                                            style: AppTextStyles.bodysmallBold
                                                .copyWith(fontSize: 18),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ] else ...[
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Price:',
                                          style:
                                              AppTextStyles.bodySmall.copyWith(
                                            color: Colors.white70,
                                          ),
                                        ),
                                        Text(
                                          '\$${widget.offer.offerValue}',
                                          style: AppTextStyles.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildKeypad() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildKeypadButton('1'),
            _buildKeypadButton('2'),
            _buildKeypadButton('3'),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildKeypadButton('4'),
            _buildKeypadButton('5'),
            _buildKeypadButton('6'),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildKeypadButton('7'),
            _buildKeypadButton('8'),
            _buildKeypadButton('9'),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildControlButton(
              icon: CupertinoIcons.clear,
              onTap: _clearPin,
            ),
            _buildKeypadButton('0'),
            _buildControlButton(
              icon: CupertinoIcons.delete_left,
              onTap: _removeDigit,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildKeypadButton(String digit) {
    return HapticButton(
      onTap: () => _addDigit(digit),
      child: Container(
        width: 85,
        height: 85,
        margin: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              darkAppColors.secondaryBackgroundColor.withOpacity(0.5),
              darkAppColors.secondaryBackgroundColor.withOpacity(0.2),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            digit,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return HapticButton(
      onTap: onTap,
      child: Container(
        width: 85,
        height: 85,
        margin: const EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor.withOpacity(0.3),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
        child: Center(
          child: Icon(
            icon,
            color: Colors.white,
            size: 28,
          ),
        ),
      ),
    );
  }
}
