import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/location/location_permission.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/utils/size_utils.dart';

class PreferencesPage extends StatefulWidget {
  const PreferencesPage({super.key});

  @override
  State<PreferencesPage> createState() => _PreferencesPageState();
}

class _PreferencesPageState extends State<PreferencesPage> {
  bool notificationsEnabled = false;
  bool locationEnabled = false;
  final _storage = const FlutterSecureStorage();
  static const _notificationsKey = 'notifications_enabled';
  static const _locationKey = 'location_enabled';

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    try {
      // Load notification preference
      final notificationPref = await _storage.read(key: _notificationsKey);
      if (notificationPref != null) {
        setState(() {
          notificationsEnabled = notificationPref == 'true';
        });
      } else {
        // Check current notification permission status
        final permissionStatus = OneSignal.Notifications.permission;
        setState(() {
          notificationsEnabled = permissionStatus;
        });
        // Save the initial state
        await _storage.write(
          key: _notificationsKey,
          value: permissionStatus.toString(),
        );
      }

      // Load location preference
      final locationPref = await _storage.read(key: _locationKey);
      if (locationPref != null) {
        setState(() {
          locationEnabled = locationPref == 'true';
        });
      } else {
        // Check current location permission status
        final permissionGranted = await isLocationPermissionGranted();
        setState(() {
          locationEnabled = permissionGranted;
        });
        // Save the initial state
        await _storage.write(
          key: _locationKey,
          value: permissionGranted.toString(),
        );
      }
    } catch (e) {
      AppLogger.error('Error loading preferences: $e');
    }
  }

  Future<void> _toggleNotifications(bool value) async {
    try {
      if (value) {
        // Request notification permission
        final permissionStatus =
            await OneSignal.Notifications.requestPermission(true);
        if (permissionStatus) {
          setState(() {
            notificationsEnabled = true;
          });
          await _storage.write(key: _notificationsKey, value: 'true');
        } else {
          // Permission denied, keep toggle off
          setState(() {
            notificationsEnabled = false;
          });
          await _storage.write(key: _notificationsKey, value: 'false');

          // Show dialog explaining how to enable notifications
          if (mounted) {
            _showPermissionDeniedDialog(
              'Notifications',
              'Please enable notifications in your device settings to receive updates from Vibeo.',
            );
          }
        }
      } else {
        // User wants to disable notifications
        setState(() {
          notificationsEnabled = false;
        });
        await _storage.write(key: _notificationsKey, value: 'false');
      }
    } catch (e) {
      AppLogger.error('Error toggling notifications: $e');
    }
  }

  Future<void> _toggleLocation(bool value) async {
    try {
      if (value) {
        // Request location permission
        await handleLocationPermission();
        // Check if permission was granted
        final permissionGranted = await isLocationPermissionGranted();

        setState(() {
          locationEnabled = permissionGranted;
        });
        await _storage.write(
          key: _locationKey,
          value: permissionGranted.toString(),
        );

        // If permission was denied, show dialog
        if (!permissionGranted && mounted) {
          _showPermissionDeniedDialog(
            'Location Services',
            'Please enable location services in your device settings to use location features in Vibeo.',
          );
        }
      } else {
        // User wants to disable location
        setState(() {
          locationEnabled = false;
        });
        await _storage.write(key: _locationKey, value: 'false');
      }
    } catch (e) {
      AppLogger.error('Error toggling location: $e');
    }
  }

  void _showPermissionDeniedDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      appBar: CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: true,
        middle: Text(
          'Manage Preferences',
          style: AppTextStyles.heading2.copyWith(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        border: Border.all(color: Colors.transparent),
      ),
      body: SingleChildScrollView(
        padding: SizeUtils.horizontalPadding.copyWith(
          top: 16,
          bottom: 32,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildPreferencesSection(
              title: 'App Permissions',
              items: [
                _buildToggleItem(
                  icon: CupertinoIcons.bell,
                  title: 'Notifications',
                  subtitle: 'Receive updates about new offers and events',
                  value: notificationsEnabled,
                  onChanged: _toggleNotifications,
                  showDivider: true,
                ),
                _buildToggleItem(
                  icon: CupertinoIcons.location,
                  title: 'Location Services',
                  subtitle:
                      'Allow Vibeo to access your location for nearby venues',
                  value: locationEnabled,
                  onChanged: _toggleLocation,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection({
    required String title,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 12),
          child: Text(
            title,
            style: AppTextStyles.subtitle.copyWith(
              color: Colors.grey,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: darkAppColors.secondaryBackgroundColor.withAlpha(77),
            boxShadow: AppShadowStyles.baseStyle,
            border: Border.all(
              color: Colors.white.withAlpha(13),
              width: 0.5,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Column(
              children: items,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildToggleItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool showDivider = false,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: darkAppColors.deepPurple.withAlpha(38),
                ),
                child: Icon(
                  icon,
                  color: Colors.deepPurpleAccent,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTextStyles.heading4.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              CupertinoSwitch(
                value: value,
                onChanged: onChanged,
                activeTrackColor: darkAppColors.deepPurple,
              ),
            ],
          ),
        ),
        if (showDivider)
          Divider(
            color: Colors.white.withAlpha(13),
            height: 1,
            indent: 76,
            endIndent: 20,
          ),
      ],
    );
  }
}
