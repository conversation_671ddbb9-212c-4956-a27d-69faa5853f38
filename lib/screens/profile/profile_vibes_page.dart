import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/views/profile/download_feed_dialog.dart';
import 'package:vibeo/widgets/feed/feed_tile.dart';
import 'package:vibeo/widgets/global/no_content_page.dart';

import 'package:vibeo/widgets/video_tiles/vibe_shimmer_tiles.dart';

class ProfileVibesPage extends StatefulWidget {
  const ProfileVibesPage({super.key});

  @override
  State<ProfileVibesPage> createState() => _ProfileVibesPageState();
}

class _ProfileVibesPageState extends State<ProfileVibesPage> {
  @override
  void initState() {
    super.initState();
    final userState = context.read<UserBloc>().state;

    if (userState is UserExists && !userState.feedsFetched) {
      context.read<UserBloc>().add(
            FetchUserFeedsEvent(userState.user!.uid),
          );
    }
  }

  Future<bool> saveNetworkVideoFile(
    BuildContext context,
    String fileName,
    String networkURL,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return DownloadProgressDialog(
          fileName: fileName,
          networkURL: networkURL,
          watermarkText: 'Vibeo',
        );
      },
    );

    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        middle: Text(
          'Your Vibes',
          style: TextStyle(color: Colors.white),
        ),
        automaticallyImplyLeading: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator.adaptive(
              displacement: 100,
              edgeOffset: 100,
              onRefresh: () {
                context.read<UserBloc>().add(
                      RefreshFeeds(context.read<UserBloc>().state.user!.uid),
                    );
                return Future.delayed(Durations.medium2);
              },
              color: Colors.white,
              child: BlocBuilder<UserBloc, UserState>(
                builder: (context, state) {
                  if (state.isLoadingFeeds) {
                    return buildShimmerGrid();
                  }

                  final feeds = state.feeds;
                  if (feeds == null || feeds.isEmpty) {
                    return ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: const [
                        NoContentPage(
                          icon: CupertinoIcons.photo_on_rectangle,
                          title: 'Please Add Vibes',
                          description:
                              'Share the vibes! Upload your content to bring Vibeo to life.',
                        ),
                      ],
                    );
                  }

                  return GridView.builder(
                    shrinkWrap: true,
                    padding: SizeUtils.horizontalPadding.copyWith(
                      top: 120,
                      bottom: 60,
                    ),
                    physics: const AlwaysScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 5,
                      crossAxisSpacing: 5,
                      childAspectRatio: 1.2 / 2,
                    ),
                    itemCount: feeds.length,
                    itemBuilder: (context, index) {
                      final feed = feeds[index];
                      return CupertinoContextMenu.builder(
                        enableHapticFeedback: true,
                        actions: [
                          CupertinoContextMenuAction(
                            isDestructiveAction: true,
                            trailingIcon: CupertinoIcons.delete,
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) => CupertinoAlertDialog(
                                  title: const Text('Delete feed'),
                                  content: const Text(
                                    'Do you really want to delete this feed?',
                                  ),
                                  actions: [
                                    CupertinoDialogAction(
                                      isDestructiveAction: true,
                                      child: const Text(
                                        'Yes',
                                      ),
                                      onPressed: () {
                                        RouteUtils.pop(context);

                                        context.read<UserBloc>().add(
                                              DeleteUserFeed(
                                                userId: context
                                                    .read<UserBloc>()
                                                    .state
                                                    .user!
                                                    .uid,
                                                feedId: context
                                                    .read<UserBloc>()
                                                    .state
                                                    .feeds![index]
                                                    .id,
                                              ),
                                            );
                                        RouteUtils.pop(context);
                                      },
                                    ),
                                    CupertinoDialogAction(
                                      textStyle: const TextStyle(
                                        color: Colors.white54,
                                      ),
                                      child: const Text(
                                        'Cancel',
                                      ),
                                      onPressed: () {
                                        RouteUtils.pop(context);
                                      },
                                    ),
                                  ],
                                ),
                              );
                            },
                            child: const Text('Delete'),
                          ),
                          CupertinoContextMenuAction(
                            trailingIcon: CupertinoIcons.download_circle,
                            onPressed: () async {
                              final success = await saveNetworkVideoFile(
                                context,
                                'video_${DateTime.now().millisecondsSinceEpoch}',
                                context
                                    .read<UserBloc>()
                                    .state
                                    .feeds![index]
                                    .videoURL,
                              );
                              if (success) {
                                if (context.mounted) {
                                  RouteUtils.pop(context);
                                }
                              } else {
                                if (context.mounted) {
                                  RouteUtils.pop(context);
                                }
                                if (context.mounted) {
                                  RouteUtils.pop(context);
                                }
                              }
                            },
                            child: const Text('Save to Gallery'),
                          ),
                        ],
                        builder: (context, animation) => GestureDetector(
                          onTap: () {
                            RouteUtils.pushNamed(
                              context,
                              RoutePaths.feedPlayingPage,
                              arguments: {
                                'initialIndex': index,
                                'feeds': feeds,
                                'title': vibeScoreDefinitions[feed.vibeScore]![
                                        'name'] ??
                                    'Vibes',
                              },
                            );
                          },
                          child: FeedTile(feed: feed),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
