import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/logic/feed/controller/feed_controller.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';

import 'package:vibeo/routes/route.dart';

import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/feed/feed_tile.dart';
import 'package:vibeo/widgets/global/no_content_page.dart';

import 'package:vibeo/widgets/video_tiles/vibe_shimmer_tiles.dart';

class VibesLikedPage extends StatefulWidget {
  const VibesLikedPage({super.key});

  @override
  State<VibesLikedPage> createState() => _VibesLikedPageState();
}

class _VibesLikedPageState extends State<VibesLikedPage> {
  late FeedController feedController;
  @override
  void initState() {
    super.initState();
    final userState = context.read<UserBloc>().state;
    feedController = FeedController(userState.user!.uid);
    if (userState is UserExists && !userState.likedFeedsFetched) {
      () async {
        final List<String> feedIds = await feedController.listVibesLiked();
        if (mounted) {
          context.read<UserBloc>().add(
                FetchUserLikedFeedsEvent(feedIds),
              );
        }
      }();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        middle: Text(
          'Your Favorites',
          style: TextStyle(color: Colors.white),
        ),
        automaticallyImplyLeading: true,
      ),
      body: Column(
        children: [
          Expanded(
            child: RefreshIndicator.adaptive(
              displacement: 100,
              edgeOffset: 100,
              onRefresh: () async {
                final List<String> feedIds =
                    await feedController.listVibesLiked();
                if (context.mounted) {
                  context.read<UserBloc>().add(
                        RefreshLikedFeeds(feedIds),
                      );
                }

                return Future.delayed(Durations.medium2);
              },
              color: Colors.white,
              child: BlocBuilder<UserBloc, UserState>(
                builder: (context, state) {
                  if (state.isLikedLoadingFeeds) {
                    return buildShimmerGrid();
                  }

                  final feeds = state.likedFeeds;
                  if (feeds == null || feeds.isEmpty) {
                    return ListView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      children: const [
                        NoContentPage(
                          icon: CupertinoIcons.hand_thumbsup,
                          title: 'You Liked No Feeds Yet',
                          description:
                              'Your favorites are waiting! Start liking posts to save your top vibes.',
                        ),
                      ],
                    );
                  }

                  return GridView.builder(
                    shrinkWrap: true,
                    padding: SizeUtils.horizontalPadding.copyWith(
                      top: 120,
                      bottom: 60,
                    ),
                    physics: const AlwaysScrollableScrollPhysics(),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      mainAxisSpacing: 5,
                      crossAxisSpacing: 5,
                      childAspectRatio: 1.2 / 2,
                    ),
                    itemCount: feeds.length,
                    itemBuilder: (context, index) {
                      final feed = feeds[index];
                      return GestureDetector(
                        onTap: () {
                          RouteUtils.pushNamed(
                            context,
                            RoutePaths.feedPlayingPage,
                            arguments: {
                              'initialIndex': index,
                              'feeds': feeds,
                              'title': vibeScoreDefinitions[
                                      feeds[index].vibeScore]!['name'] ??
                                  'Vibes',
                            },
                          );
                        },
                        child: FeedTile(feed: feed),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
