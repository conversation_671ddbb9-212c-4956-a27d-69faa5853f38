import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/loading_widget.dart';

import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/controller/venue_controller.dart';

import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/views/shimmer/venue_shimmer_tile.dart';
import 'package:vibeo/views/state/something_went_wrong_screen.dart';
import 'package:vibeo/widgets/global/no_content_page.dart';
import 'package:vibeo/widgets/venue/venue_tile.dart';

class SavedVenuesPage extends StatefulWidget {
  const SavedVenuesPage({super.key});

  @override
  State<SavedVenuesPage> createState() => _SavedVenuesPageState();
}

class _SavedVenuesPageState extends State<SavedVenuesPage> {
  late VenueController venueController;
  @override
  void initState() {
    super.initState();
    venueController = VenueController(context.read<UserBloc>().state.user!.uid);
    final userState = context.read<UserBloc>().state;

    if (userState is UserExists) {
      () async {
        final List<String> venueIds = await venueController.venuesSavedList();

        if (mounted) {
          context.read<UserBloc>().add(
                FetchUserSavedVenuesEvent(venueIds),
              );
        }
      }();
    }
  }

  Future<void> onRefresh() async {
    final userState = context.read<UserBloc>().state;

    if (userState is UserExists) {
      final List<String> venueIds = await venueController.venuesSavedList();

      if (mounted) {
        context.read<UserBloc>().add(
              FetchUserSavedVenuesEvent(venueIds),
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: const CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        middle: Text(
          'Your Venues',
          style: TextStyle(color: Colors.white),
        ),
        automaticallyImplyLeading: true,
      ),
      body: Padding(
        padding: SizeUtils.horizontalPadding,
        child: RefreshIndicator.adaptive(
          displacement: 100,
          edgeOffset: 100,
          onRefresh: onRefresh,
          color: Colors.white,
          child: BlocBuilder<UserBloc, UserState>(
            builder: (context, state) {
              if (state is UserError) {
                return SomethingWentWrong(
                  onRetry: onRefresh,
                );
              }
              if (state.isLoadingSavedVenues) {
                return ListView.builder(
                  padding: const EdgeInsets.only(
                    top: 120,
                    bottom: 60,
                  ),
                  itemCount: 10,
                  itemBuilder: (ctx, index) => const VenueTileShimmer(),
                );
              }
              final venues = state.savedVenues;
              if (state.savedVenuesFetched &&
                  (venues == null || venues.isEmpty)) {
                return ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: const [
                    NoContentPage(
                      icon: CupertinoIcons.heart_slash,
                      title: 'Your Venues',
                      description:
                          'Haven’t saved any hotspots yet? Find your favorites and lock them in.',
                    ),
                  ],
                );
              }
              if (venues != null && venues.isNotEmpty) {
                return ListView.builder(
                  padding: const EdgeInsets.only(
                    top: 120,
                    bottom: 60,
                  ),
                  itemCount: venues.length,
                  itemBuilder: (context, index) {
                    if (index >= venues.length) {
                      return const VenueTileShimmer();
                    }
                    return VenueTile(
                      index: index,
                      venue: venues[index],
                      addVenueExplicitly: true,
                    );
                  },
                );
              }

              return const Center(child: LoadingWidget());
            },
          ),
        ),
      ),
    );
  }
}
