import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/helper/open_web_link.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/user/user_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/auth/dialogs/generalinfo_dialog.dart';
import 'package:vibeo/widgets/tutorial/tutorial_manager.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late final UserModel? user;

  @override
  void initState() {
    user = context.read<UserBloc>().state.user;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      appBar: CupertinoNavigationBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: true,
        middle: Text(
          'Settings',
          style: AppTextStyles.heading2.copyWith(
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        border: Border.all(color: Colors.transparent),
      ),
      body: SingleChildScrollView(
        padding: SizeUtils.horizontalPadding.copyWith(
          top: 16,
          bottom: 32,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileSection(),
            const SizedBox(height: 24),
            _buildSettingsSection(
              title: 'App Settings',
              items: [
                SettingsItem(
                  icon: CupertinoIcons.settings,
                  title: 'Manage Preferences',
                  onTap: () {
                    RouteUtils.pushNamed(
                      context,
                      RoutePaths.preferencesPage,
                    );
                  },
                  showDivider: true,
                ),
                SettingsItem(
                  icon: CupertinoIcons.info_circle,
                  title: 'Reset Tutorials',
                  onTap: _showResetTutorialsConfirmation,
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              title: 'Help & Support',
              items: [
                SettingsItem(
                  icon: CupertinoIcons.question_circle,
                  title: 'FAQ',
                  onTap: () {
                    GeneralInfoDialog.show(context, 'assets/files/faq.md');
                  },
                  showDivider: true,
                ),
                const SettingsItem(
                  icon: CupertinoIcons.mail,
                  title: 'Contact Us',
                  onTap: openVibeoWebLink,
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              title: 'Legal',
              items: [
                SettingsItem(
                  icon: CupertinoIcons.doc_text,
                  title: 'Privacy Policy',
                  onTap: () {
                    GeneralInfoDialog.show(context, 'assets/files/privacy.md');
                  },
                  showDivider: true,
                ),
                SettingsItem(
                  icon: CupertinoIcons.doc_plaintext,
                  title: 'Terms & Conditions',
                  onTap: () {
                    GeneralInfoDialog.show(context, 'assets/files/terms.md');
                  },
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSettingsSection(
              title: 'Danger Zone',
              items: [
                SettingsItem(
                  icon: CupertinoIcons.arrow_right_square,
                  title: 'Logout',
                  onTap: _showLogoutConfirmation,
                  iconColor: Colors.redAccent,
                  showDivider: true,
                ),
                SettingsItem(
                  icon: CupertinoIcons.delete,
                  title: 'Delete Account',
                  onTap: _showDeleteAccountConfirmation,
                  iconColor: Colors.redAccent,
                ),
              ],
            ),
            const SizedBox(height: 32),
            Center(
              child: Text(
                'Version 1.0.0',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Row(
      children: [
        Container(
          height: 70,
          width: 70,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                darkAppColors.secondaryBackgroundColor,
                const Color.fromARGB(255, 64, 64, 64),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Text(
            user!.fullName.trim().contains(' ')
                ? '${user!.fullName.trim().split(" ").first[0].toUpperCase()}'
                    '${user!.fullName.trim().split(" ").last[0].toUpperCase()}'
                : '${user!.fullName.trim()[0].toUpperCase()}'
                    '${user!.fullName.trim().length > 1 ? user!.fullName.trim()[user!.fullName.trim().length - 1].toUpperCase() : ""}',
            style: AppTextStyles.heading1.copyWith(
              fontSize: 24,
            ),
          ),
        ),
        const SizedBox(width: 24),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${user!.fullName}',
                style: AppTextStyles.heading2.copyWith(
                  fontSize: 26,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required List<Widget> items,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, bottom: 12),
          child: Text(
            title,
            style: AppTextStyles.subtitle.copyWith(
              color: Colors.grey,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: darkAppColors.secondaryBackgroundColor.withAlpha(77),
            boxShadow: AppShadowStyles.baseStyle,
            border: Border.all(
              color: Colors.white.withAlpha(13),
              width: 0.5,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: Column(
              children: items,
            ),
          ),
        ),
      ],
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Sign out'),
        content: const Text('Do you really want to sign out?'),
        actions: [
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              RouteUtils.pop(context);
              context.read<AuthBloc>().add(const SignOut());
              RouteUtils.pushNamedAndRemoveUntil(
                context,
                RoutePaths.onboardPage,
                (route) => false,
              );
            },
            child: const Text('Yes'),
          ),
          CupertinoDialogAction(
            textStyle: const TextStyle(color: Colors.white54),
            onPressed: () => RouteUtils.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountConfirmation() {
    showDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Do you really want to permanently delete your account?',
        ),
        actions: [
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () {
              RouteUtils.pop(context);
              context.read<UserBloc>().add(
                    DeleteUser(
                      uid: context.read<UserBloc>().state.user!.uid,
                    ),
                  );
              context.read<AuthBloc>().add(const SignOut());
              RouteUtils.pushNamedAndRemoveUntil(
                context,
                RoutePaths.onboardPage,
                (route) => false,
              );
            },
            child: const Text('Yes'),
          ),
          CupertinoDialogAction(
            textStyle: const TextStyle(color: Colors.white54),
            onPressed: () => RouteUtils.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showResetTutorialsConfirmation() {
    showDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Reset Tutorials'),
        content: const Text(
          'This will reset all tutorial progress and show tutorials again when you visit different sections of the app.',
        ),
        actions: [
          CupertinoDialogAction(
            onPressed: () async {
              RouteUtils.pop(context);
              await TutorialManager.instance.resetAllTutorials();

              // Show success message
              if (context.mounted) {
                await showDialog(
                  context: context,
                  builder: (context) => CupertinoAlertDialog(
                    title: const Text('Tutorials Reset'),
                    content: const Text(
                      'All tutorials have been reset. You will see them again when you navigate through the app.',
                    ),
                    actions: [
                      CupertinoDialogAction(
                        onPressed: () => RouteUtils.pop(context),
                        child: const Text('OK'),
                      ),
                    ],
                  ),
                );
              }
            },
            child: const Text('Reset'),
          ),
          CupertinoDialogAction(
            textStyle: const TextStyle(color: Colors.white54),
            onPressed: () => RouteUtils.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class SettingsItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final bool showDivider;
  final Color? iconColor;

  const SettingsItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.showDivider = false,
    this.iconColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color:
                        (iconColor ?? darkAppColors.deepPurple).withAlpha(38),
                  ),
                  child: Icon(
                    icon,
                    color: iconColor ?? Colors.deepPurpleAccent,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: AppTextStyles.heading4.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: darkAppColors.lightColor.withAlpha(128),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
        if (showDivider)
          Divider(
            color: Colors.white.withAlpha(13),
            height: 1,
            indent: 76,
            endIndent: 20,
          ),
      ],
    );
  }
}

class SettingsToggleItem extends StatelessWidget {
  final IconData icon;
  final String title;
  final bool value;
  final ValueChanged<bool> onChanged;
  final bool showDivider;
  final Color? iconColor;

  const SettingsToggleItem({
    required this.icon,
    required this.title,
    required this.value,
    required this.onChanged,
    this.showDivider = false,
    this.iconColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: (iconColor ?? darkAppColors.deepPurple).withAlpha(38),
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? Colors.deepPurpleAccent,
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.heading4.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              CupertinoSwitch(
                value: value,
                onChanged: onChanged,
                activeTrackColor: darkAppColors.deepPurple,
              ),
            ],
          ),
        ),
        if (showDivider)
          Divider(
            color: Colors.white.withAlpha(13),
            height: 1,
            indent: 76,
            endIndent: 20,
          ),
      ],
    );
  }
}
