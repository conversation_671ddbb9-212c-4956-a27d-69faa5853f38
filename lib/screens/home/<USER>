import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/screens/basket/basket_page.dart';
import 'package:vibeo/screens/screen.dart';
import 'package:vibeo/widgets/navbar/navbar.dart';
import 'package:vibeo/widgets/tutorial/tutorial_manager.dart';

class HomePage extends StatefulWidget {
  final int initialIndex;

  const HomePage({this.initialIndex = 0, super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  late final List<Widget> _pages;
  bool _hasShownInitialTutorial = false;

  // Global keys for tutorial targeting
  final GlobalKey _bottomNavigationKey = GlobalKey();
  final GlobalKey _forYouTabKey = GlobalKey();
  final GlobalKey _beoTabKey = GlobalKey();
  final GlobalKey _bagTabKey = GlobalKey();
  final GlobalKey _profileTabKey = GlobalKey();

  Future<void> _onItemTapped(int index) async {
    final AnalyticsService analytics = AnalyticsService.instance;
    await analytics.logHomepageTab(
      interactionType: index == 0
          ? 'explore'
          : index == 1
              ? 'beo'
              : 'profile',
    );

    final previousIndex = _selectedIndex;

    if (_selectedIndex != index) {
      setState(() {
        _selectedIndex = index;
      });

      // Show page-specific tutorials when switching tabs
      await _showPageTutorialIfNeeded(index, previousIndex);
    }
    if (_selectedIndex == 1) {}
  }

  /// Show tutorial for specific page if it hasn't been shown before
  Future<void> _showPageTutorialIfNeeded(
    int newIndex,
    int previousIndex,
  ) async {
    // Only show tutorials after initial tutorial is complete
    if (!_hasShownInitialTutorial) return;

    // Small delay to ensure page transition is complete
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    switch (newIndex) {
      case 0: // For You page
        if (await TutorialManager.instance.shouldShowTutorial('for_you_page')) {
          if (mounted) {
            await TutorialManager.instance.startForYouTutorial(context);
          }
        }
      case 1: // Beo page
        if (await TutorialManager.instance.shouldShowTutorial('beo_page')) {
          if (mounted) {
            await TutorialManager.instance.startBeoTutorial(context);
          }
        }
      case 2: // Bag page
        if (await TutorialManager.instance.shouldShowTutorial('bag_page')) {
          if (mounted) {
            await TutorialManager.instance.startBagTutorial(context);
          }
        }
      case 3: // Profile page - no specific tutorial for now
    }
  }

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
    _pages = [
      const ExplorePage(),
      BeoAIPage(
        onExploreMoreTap: () => _onItemTapped(0),
      ),
      const BasketPage(),
      const ProfilePage(),
    ];

    // Initialize tutorial system after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeTutorialSystem();
    });
  }

  /// Initialize the tutorial system and show initial tutorial if needed
  Future<void> _initializeTutorialSystem() async {
    if (!mounted) return;

    // Get user ID from UserBloc
    final userState = context.read<UserBloc>().state;
    if (userState is! UserExists || userState.user == null) {
      return;
    }

    // Initialize tutorial manager
    TutorialManager.instance.initialize(userState.user!.uid);

    // Register target keys for navigation tutorial
    TutorialManager.instance
        .registerTargetKey('bottom_navigation', _bottomNavigationKey);
    TutorialManager.instance.registerTargetKey('for_you_tab', _forYouTabKey);
    TutorialManager.instance.registerTargetKey('beo_tab', _beoTabKey);
    TutorialManager.instance.registerTargetKey('bag_tab', _bagTabKey);
    TutorialManager.instance.registerTargetKey('profile_tab', _profileTabKey);

    // Check if initial tutorial should be shown
    if (await TutorialManager.instance
        .shouldShowTutorial('initial_app_tutorial')) {
      // Small delay to ensure UI is fully loaded
      await Future.delayed(const Duration(milliseconds: 1000));
      if (mounted) {
        await TutorialManager.instance.startInitialAppTutorial(context);
        _hasShownInitialTutorial = true;
      }
    } else {
      _hasShownInitialTutorial = true;
      // If initial tutorial is done, check for page-specific tutorial
      await _showPageTutorialIfNeeded(_selectedIndex, -1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: NavBar(
        key: _bottomNavigationKey,
        selectedIndex: _selectedIndex,
        onItemTapped: _onItemTapped,
        tabKeys: [
          _forYouTabKey,
          _beoTabKey,
          _bagTabKey,
          _profileTabKey,
        ],
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        child: RepaintBoundary(
          child: _pages[_selectedIndex],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Unregister tutorial keys
    TutorialManager.instance.unregisterTargetKey('bottom_navigation');
    TutorialManager.instance.unregisterTargetKey('for_you_tab');
    TutorialManager.instance.unregisterTargetKey('beo_tab');
    TutorialManager.instance.unregisterTargetKey('bag_tab');
    TutorialManager.instance.unregisterTargetKey('profile_tab');
    super.dispose();
  }
}
