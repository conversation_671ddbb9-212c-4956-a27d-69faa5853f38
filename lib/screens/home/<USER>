import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/data/autocompletions_data.dart';
import 'package:vibeo/logic/search/search_bloc.dart';

import 'package:vibeo/models/feed/feed_model.dart';

import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/views/state/not_found_screen.dart';
import 'package:vibeo/widgets/beo/beo_banner.dart';
import 'package:vibeo/widgets/venue/venue_tile.dart';
import 'package:vibeo/widgets/feed/build_feed_item.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class SearchPage extends StatefulWidget {
  final Animation<double> animation;
  const SearchPage({required this.animation, super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  late List<String> suggestions;

  @override
  void initState() {
    super.initState();
    suggestions = popularVenues + popularTags;
    context.read<SearchBloc>().add(SearchInitial());
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: widget.animation,
      child: Scaffold(
        backgroundColor: darkAppColors.backgroundColor.withAlpha(120),
        body: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
          child: Container(
            decoration: BoxDecoration(
              color: darkAppColors.backgroundColor.withAlpha(120),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 10,
                  spreadRadius: 2,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              children: [
                const SizedBox(
                  height: kToolbarHeight,
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    left: 10,
                    right: 5,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: SizedBox(
                          height: 45,
                          child: BlocConsumer<SearchBloc, SearchState>(
                            listener: (context, state) {
                              if (state is SearchInitialState) {
                                suggestions = state.autocompleteSuggestions;
                              }
                              if (state is SearchLoadedState) {
                                _focusNode.unfocus();
                              }
                            },
                            builder: (context, state) {
                              return Autocomplete<String>(
                                optionsBuilder:
                                    (TextEditingValue textEditingValue) {
                                  // Return empty list if no text
                                  if (textEditingValue.text.isEmpty) {
                                    return const [];
                                  }

                                  // Filter suggestions based on input
                                  return suggestions.where(
                                    (suggestion) => suggestion
                                        .toLowerCase()
                                        .contains(
                                          textEditingValue.text.toLowerCase(),
                                        ),
                                  );
                                },
                                onSelected: (String selection) {
                                  _searchController.text = selection;
                                  _focusNode.unfocus();
                                  FocusManager.instance.primaryFocus?.unfocus();

                                  context
                                      .read<SearchBloc>()
                                      .add(Searched(selection.trim()));
                                },
                                fieldViewBuilder: (
                                  BuildContext context,
                                  TextEditingController controller,
                                  FocusNode focusNode,
                                  VoidCallback onFieldSubmitted,
                                ) {
                                  _searchController.text = controller.text;
                                  controller.addListener(() {
                                    if (_searchController.text !=
                                        controller.text) {
                                      _searchController.text = controller.text;
                                    }
                                  });
                                  _searchController.addListener(() {
                                    if (controller.text !=
                                        _searchController.text) {
                                      controller.text = _searchController.text;
                                    }
                                  });
                                  return TextField(
                                    controller: _searchController,
                                    focusNode: focusNode,
                                    cursorColor: Colors.white,
                                    decoration: InputDecoration(
                                      contentPadding:
                                          const EdgeInsets.only(left: 20),
                                      hintText: 'Search Vibes',
                                      hintStyle: const TextStyle(
                                        color: Colors.white60,
                                        fontSize: 15,
                                      ),
                                      border: const OutlineInputBorder(
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(100),
                                        ),
                                      ),
                                      enabledBorder: const OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.white30,
                                          width: 0.1,
                                        ),
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(100),
                                        ),
                                      ),
                                      focusedBorder: const OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.white,
                                          width: 0.1,
                                        ),
                                        borderRadius: BorderRadius.all(
                                          Radius.circular(100),
                                        ),
                                      ),
                                      suffixIcon: _searchController.text
                                              .isNotEmpty // Show clear button only when text is not empty
                                          ? IconButton(
                                              icon: const Icon(
                                                Icons.clear,
                                                color: Colors
                                                    .white60, // Adjust the color to match your design
                                              ),
                                              onPressed: () {
                                                _searchController
                                                    .clear(); // Clear the text
                                                // Optionally, trigger any search-related logic here
                                                context.read<SearchBloc>().add(
                                                      ClearSearch(),
                                                    ); // Example: Add a ClearSearch event
                                              },
                                            )
                                          : null,
                                    ),
                                    onSubmitted: (String value) async {
                                      _focusNode.unfocus();

                                      if (value.trim().isNotEmpty) {
                                        context
                                            .read<SearchBloc>()
                                            .add(Searched(value.trim()));
                                        final AnalyticsService analytics =
                                            AnalyticsService.instance;
                                        await analytics.logSearch(
                                          queryText: value.trim(),
                                        );
                                      }
                                    },
                                    onChanged: (_) {
                                      setState(() {});
                                    },
                                  );
                                },
                                optionsViewBuilder: (
                                  BuildContext context,
                                  AutocompleteOnSelected<String> onSelected,
                                  Iterable<String> options,
                                ) {
                                  return Align(
                                    alignment: Alignment.topLeft,
                                    child: Container(
                                      margin: EdgeInsets.only(
                                        top: 10,
                                        right:
                                            SizeUtils.horizontalPadding.right,
                                      ),
                                      decoration: BoxDecoration(
                                        color: darkAppColors
                                            .secondaryBackgroundColor
                                            .withAlpha(
                                          (255 * 0.3).toInt(),
                                        ), // Semi-transparent background
                                        borderRadius: const BorderRadius.all(
                                          Radius.circular(20),
                                        ),
                                        border: Border.all(
                                          color: Colors.white.withAlpha(
                                            (255 * 0.2).toInt(),
                                          ), // Light border for glass effect
                                          width: 1,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withAlpha(
                                              25,
                                            ), // Subtle shadow
                                            blurRadius: 10,
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(
                                          20,
                                        ), // Match the container's border radius
                                        child: BackdropFilter(
                                          filter: ImageFilter.blur(
                                            sigmaX: 50,
                                            sigmaY: 50,
                                          ), // Blur effect
                                          child: ConstrainedBox(
                                            constraints: const BoxConstraints(
                                              maxHeight: 200,
                                            ),
                                            child: ListView.builder(
                                              padding: EdgeInsets.zero,
                                              shrinkWrap: true,
                                              itemCount: options.length,
                                              itemBuilder: (
                                                BuildContext context,
                                                int index,
                                              ) {
                                                final String option =
                                                    options.elementAt(index);
                                                return InkWell(
                                                  onTap: () {
                                                    _focusNode.unfocus();

                                                    onSelected(option);
                                                  },
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.all(
                                                      16,
                                                    ),
                                                    child: Text(
                                                      option,
                                                      style: const TextStyle(
                                                        color: Colors.white,
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      IconButton(
                        onPressed: () async {
                          _focusNode.unfocus();

                          if (_searchController.text.trim().isNotEmpty) {
                            context
                                .read<SearchBloc>()
                                .add(Searched(_searchController.text.trim()));
                            final AnalyticsService analytics =
                                AnalyticsService.instance;
                            await analytics.logSearch(
                              queryText: _searchController.text.trim(),
                            );
                          }
                        },
                        icon: const Icon(
                          CupertinoIcons.search,
                          color: Colors.white,
                        ),
                      ),
                      IconButton(
                        onPressed: () async {
                          _focusNode.unfocus();

                          RouteUtils.pop(context);
                          // context.read<SearchBloc>().add(SearchInitial());
                        },
                        icon: const Icon(
                          CupertinoIcons.chevron_down,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                // Content below search bar
                Expanded(
                  child: SizedBox(
                    width: double.infinity,
                    child: buildScrollableWithFade(
                      child: SingleChildScrollView(
                        padding: SizeUtils.horizontalPadding.copyWith(
                          top: 20,
                        ),
                        child: BlocBuilder<SearchBloc, SearchState>(
                          builder: (context, state) {
                            return state is SearchInitialState
                                ? Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Popular Venues',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Wrap(
                                        spacing: 10,
                                        runSpacing: 0,
                                        children: popularVenues
                                            .map(
                                              (venue) => SuggestionChip(
                                                label: venue,
                                                onTap: (String selected) async {
                                                  _focusNode.unfocus();

                                                  setState(() {
                                                    _searchController.text =
                                                        selected;
                                                  });
                                                  context
                                                      .read<SearchBloc>()
                                                      .add(
                                                        Searched(
                                                          selected,
                                                        ),
                                                      );
                                                  final AnalyticsService
                                                      analytics =
                                                      AnalyticsService.instance;
                                                  await analytics.logSearch(
                                                    queryText: _searchController
                                                        .text
                                                        .trim(),
                                                    isTag: true,
                                                  );
                                                },
                                              ),
                                            )
                                            .toList(),
                                      ),
                                      const SizedBox(height: 24),
                                      const Text(
                                        'Popular Tags',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      Wrap(
                                        spacing: 10,
                                        runSpacing: 0,
                                        children: popularTags
                                            .map(
                                              (tag) => SuggestionChip(
                                                label: tag,
                                                onTap: (String selected) async {
                                                  _focusNode.unfocus();

                                                  setState(() {
                                                    _searchController.text =
                                                        selected;
                                                  });

                                                  context
                                                      .read<SearchBloc>()
                                                      .add(
                                                        Searched(
                                                          selected,
                                                        ),
                                                      );
                                                  final AnalyticsService
                                                      analytics =
                                                      AnalyticsService.instance;
                                                  await analytics.logSearch(
                                                    queryText: _searchController
                                                        .text
                                                        .trim(),
                                                    isTag: true,
                                                  );
                                                },
                                              ),
                                            )
                                            .toList(),
                                      ),
                                      const Padding(
                                        padding: EdgeInsets.only(top: 16),
                                        child: BeoBanner(),
                                      ),
                                    ],
                                  )
                                : state is SearchLoadingState
                                    ? const Center(
                                        child: LoadingWidget(),
                                      )
                                    : state is SearchLoadedState
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              if (state.venues.isNotEmpty)
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'Venues',
                                                      style:
                                                          AppTextStyles.title,
                                                    ),
                                                    SizedBox(
                                                      height:
                                                          state.feeds.isEmpty
                                                              ? SizeUtils
                                                                  .screenHeight
                                                              : 200,
                                                      child:
                                                          buildScrollableWithFade(
                                                        child: ListView.builder(
                                                          padding: state
                                                                  .feeds.isEmpty
                                                              ? const EdgeInsets
                                                                  .only(
                                                                  bottom: 200,
                                                                )
                                                              : EdgeInsets.zero,
                                                          itemCount: state
                                                              .venues.length,
                                                          clipBehavior: state
                                                                  .feeds.isEmpty
                                                              ? Clip.hardEdge
                                                              : Clip.none,
                                                          scrollDirection: state
                                                                  .feeds.isEmpty
                                                              ? Axis.vertical
                                                              : Axis.horizontal,
                                                          itemBuilder:
                                                              (context, index) {
                                                            return Container(
                                                              margin: state
                                                                      .feeds
                                                                      .isNotEmpty
                                                                  ? const EdgeInsets
                                                                      .only(
                                                                      top: 0,
                                                                      right: 10,
                                                                    )
                                                                  : null,
                                                              width: 300,
                                                              child: VenueTile(
                                                                index: index,
                                                                venue: state
                                                                        .venues[
                                                                    index],
                                                                addVenueExplicitly:
                                                                    true,
                                                              ),
                                                            );
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                    const SizedBox(height: 20),
                                                  ],
                                                ),
                                              if (state.feeds.isNotEmpty)
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'Vibes',
                                                      style:
                                                          AppTextStyles.title,
                                                    ),
                                                    const SizedBox(height: 10),
                                                    _buildFeedsGrid(
                                                      state.feeds,
                                                    ),
                                                  ],
                                                ),
                                            ],
                                          )
                                        : NoResultsFoundScreen(
                                            icon: CupertinoIcons.search,
                                            title:
                                                'No Search results found for "${_searchController.text.trim()}"',
                                            description:
                                                'Please try something else.',
                                          );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeedsGrid(List<FeedModel> feeds) {
    return MasonryGridView.builder(
      gridDelegate: const SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
      ),
      padding: const EdgeInsets.only(
        bottom: 60,
      ),
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: feeds.length,
      itemBuilder: (context, index) => buildFeedItem(context, index, feeds),
    );
  }
}

class SuggestionChip extends StatelessWidget {
  final String label;
  final Function onTap;

  const SuggestionChip({
    required this.label,
    required this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return HapticButton(
      onTap: () {
        onTap(label);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 4,
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.all(Radius.circular(100)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withAlpha((255 * 0.2).toInt()),
                    Colors.white.withAlpha((255 * 0.05).toInt()),
                  ],
                ),
                borderRadius: const BorderRadius.all(Radius.circular(100)),
                border: Border.all(
                  color: Colors.white.withAlpha((255 * 0.2).toInt()),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha((255 * 0.1).toInt()),
                    blurRadius: 15,
                    spreadRadius: -5,
                  ),
                ],
              ),
              child: Text(
                label,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
