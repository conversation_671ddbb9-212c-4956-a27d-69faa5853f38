import 'package:flutter/material.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/screens/basket/basket_page.dart';
import 'package:vibeo/screens/screen.dart';
import 'package:vibeo/widgets/navbar/navbar.dart';

class HomePage extends StatefulWidget {
  final int initialIndex;

  const HomePage({this.initialIndex = 0, super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  late final List<Widget> _pages;

  Future<void> _onItemTapped(int index) async {
    final AnalyticsService analytics = AnalyticsService.instance;
    await analytics.logHomepageTab(
      interactionType: index == 0
          ? 'explore'
          : index == 1
              ? 'beo'
              : 'profile',
    );
    if (_selectedIndex != index) {
      setState(() {
        _selectedIndex = index;
      });
    }
    if (_selectedIndex == 1) {}
  }

  @override
  void initState() {
    _selectedIndex = widget.initialIndex;
    _pages = [
      const ExplorePage(),
      BeoAIPage(
        onExploreMoreTap: () => _onItemTapped(0),
      ),
      const BasketPage(),
      const ProfilePage(),
    ];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: NavBar(
        selectedIndex: _selectedIndex,
        onItemTapped: _onItemTapped,
      ),
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        child: RepaintBoundary(
          child: _pages[_selectedIndex],
        ),
      ),
    );
  }
}
