import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/components/buildScrollableWithFade.dart';

import 'package:vibeo/logic/beo/bloc/beo_bloc.dart';
import 'package:vibeo/logic/beo/bloc/beo_repo_impl.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/beo/beo.model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/beo/beo_prompt_textfield.dart';
import 'package:vibeo/widgets/feed/feed_tile.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:vibeo/widgets/text/gradient_text.dart';

enum MessageType {
  user,
  beo;

  String toJson() => name;
}

class BeoAIPage extends StatefulWidget {
  final VoidCallback onExploreMoreTap;
  const BeoAIPage({required this.onExploreMoreTap, super.key});

  @override
  State<BeoAIPage> createState() => _BeoAIPageState();
}

class _BeoAIPageState extends State<BeoAIPage>
    with SingleTickerProviderStateMixin {
  bool _isChatMode = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  final List<Map<String, dynamic>> messages = [];
  final ScrollController _scrollController =
      ScrollController(); // Add ScrollController

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _fadeAnimation = Tween<double>(begin: 1, end: 0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _scaleAnimation = Tween<double>(begin: 1, end: 0.8).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // Scroll to bottom after the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  @override
  void dispose() {
    if (messages.isNotEmpty) {
      // Use Future.delayed to ensure the operation completes
      Future.delayed(Duration.zero, () async {
        final BeoRepository beoRepository = BeoRepository();
        if (mounted) {
          final String uid = context.read<UserBloc>().state.user!.uid;
          await beoRepository.saveChatMessages(
            uid,
            messages,
          );
        }
      });
    }

    _animationController.dispose();
    _scrollController.dispose(); // Dispose the ScrollController
    super.dispose();
  }

  void _toggleChatMode(bool isChatMode) {
    setState(() {
      _isChatMode = isChatMode;
      if (_isChatMode) {
        _animationController.forward(); // Start scaling down and fading out
      } else {
        _animationController.reverse(); // Reverse to original size and fade in
      }
    });
  }

  // Method to scroll to the bottom
  void _scrollToBottom() {
    Future.delayed(const Duration(milliseconds: 50), () {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          0, // Scroll to top since the list is reversed
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      resizeToAvoidBottomInset: true,
      body: BlocProvider(
        create: (context) =>
            BeoRecommendationsBloc(repository: BeoRepository()),
        child: BlocConsumer<BeoRecommendationsBloc, BeoRecommendationsState>(
          listener: (context, state) {
            if (state is BeoRecommendationsLoading) {
              _toggleChatMode(true); // Enter chat mode with animation
            }
            if (state is BeoRecommendationsLoaded) {
              setState(() {
                messages.add({
                  'type': MessageType.beo,
                  'message': state.response.error ??
                      (state.response.data!.overallSummary.trim().isEmpty
                          ? ''
                          : state.response.data!.overallSummary),
                  'data': state.response.data?.feeds,
                  'set_homepage': state.response.data?.setHomePage ?? false,
                });
                _scrollToBottom(); // Scroll to bottom after adding a message
              });
            }
            if (state is BeoRecommendationsError) {
              setState(() {
                messages.add({
                  'type': MessageType.beo,
                  'message': "Sorry I didn't get that. Try again.",
                });
                _scrollToBottom(); // Scroll to bottom after adding a message
              });
            }
          },
          builder: (context, state) {
            return SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 100),
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    alignment: Alignment.topCenter,
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _isChatMode ? _scaleAnimation.value * 1.3 : 1,
                          alignment: Alignment.topCenter,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              GradientText(
                                'beo',
                                gradient: LinearGradient(
                                  colors: [
                                    const Color.fromARGB(255, 131, 65, 223),
                                    // darkAppColors.deepPurple,
                                    darkAppColors.lightColor,
                                  ],
                                  stops: const [0.4, 0.8],
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                ),
                                style: TextStyle(
                                  fontSize: _isChatMode ? 36 : 48,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (!_isChatMode)
                                FadeTransition(
                                  opacity: _fadeAnimation,
                                  child: const Text(
                                    'Your AI for going out',
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.white70,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  // Chat or Onboarding Section
                  Expanded(
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _isChatMode
                          ? _buildChatSection(state)
                          : _buildOnboardingSection(),
                    ),
                  ),

                  // Input Section
                  BeoPromptTextField(
                    onPromptSubmitted: (val) {
                      if (val.isNotEmpty) {
                        setState(() {
                          messages
                              .add({'type': MessageType.user, 'message': val});
                          _scrollToBottom(); // Scroll to bottom after adding a message
                        });
                        context.read<BeoRecommendationsBloc>().add(
                              FetchBeoRecommendations(val.trim()),
                            );
                      }
                    },
                    isChatMode: _isChatMode,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildOnboardingSection() {
    return Center(
      child: Image.asset(
        'assets/images/beo_onboard.png',
        width: SizeUtils.screenWidth * 0.8,
        cacheHeight: 600,
        cacheWidth: (SizeUtils.screenWidth * 0.8).toInt(),
      ),
    );
  }

  Widget _buildChatSection(BeoRecommendationsState state) {
    return buildScrollableWithFade(
      child: ListView.builder(
        controller: _scrollController,
        reverse: true,
        padding: const EdgeInsets.symmetric(vertical: 24),
        itemCount:
            messages.length + (state is BeoRecommendationsLoading ? 1 : 0),
        itemBuilder: (context, index) {
          if (state is BeoRecommendationsLoading && index == 0) {
            return _buildShimmerLoading();
          }

          final message = messages.reversed
              .toList()[state is BeoRecommendationsLoading ? index - 1 : index];
          final bool setHomePage = (message['set_homepage'] as bool?) ?? false;
          return _buildMessageBubble(message, setHomePage);
        },
      ),
    );
  }

  Widget _buildShimmerLoading() {
    final DateTime startTime = DateTime.now();
    return StatefulBuilder(
      builder: (context, setState) {
        return Align(
          alignment:
              Alignment.bottomLeft, // Align to bottom since list is reversed
          child: Container(
            margin: const EdgeInsets.only(
              left: 10,
              right: 100,
              top: 10,
            ), // Changed bottom to top
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: Colors.transparent,
            ),
            child: Shimmer.fromColors(
              baseColor: Colors.white.withAlpha(200),
              highlightColor: Colors.white.withAlpha(128),
              child: Text(
                DateTime.now().difference(startTime).inSeconds >= 4
                    ? 'Curating...'
                    : 'Thinking...',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageBubble(
    Map<String, dynamic> message,
    bool setHomePage,
  ) {
    final isUser = message['type'] == MessageType.user;
    final isFeedsAvailable = message['data'] != null &&
        message['data'] is List<Feed?> &&
        (message['data'] as List<Feed?>).isNotEmpty;
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isFeedsAvailable)
            Container(
              height: 320,
              margin: const EdgeInsets.only(bottom: 10),
              child: ListView(
                padding: const EdgeInsets.only(
                  left: 12,
                  right: 12,
                  bottom: 6,
                  top: 16,
                ),
                scrollDirection: Axis.horizontal,
                children: [
                  for (final entry
                      in (message['data'] as List<Feed?>).asMap().entries)
                    if (entry.value != null)
                      SizedBox(
                        width: 220,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 12),
                          child: HapticButton(
                            onTap: () {
                              RouteUtils.pushNamed(
                                context,
                                RoutePaths.feedPlayingPage,
                                arguments: {
                                  'initialIndex': entry.key,
                                  'feeds': (message['data'] as List<Feed?>)
                                      .map((feed) => feed!.feedModel)
                                      .toList(),
                                  'title': 'Beo AI Recommended',
                                },
                              );
                            },
                            child: FeedTile(
                              feed: entry.value!.feedModel,
                              description: entry.value!.summary,
                            ),
                          ),
                        ),
                      ),
                ],
              ),
            ),
          Container(
            margin: EdgeInsets.only(
              left: isUser ? 100 : 10,
              right: isUser ? 10 : 70,
              bottom: 12,
            ),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(16)),
              gradient: LinearGradient(
                colors: isUser
                    ? [
                        const Color.fromARGB(255, 41, 41, 41),
                        const Color.fromARGB(255, 22, 22, 22),
                      ]
                    : [
                        const Color.fromARGB(255, 22, 22, 22),
                        const Color.fromARGB(255, 41, 41, 41),
                      ],
                begin: isUser ? Alignment.topLeft : Alignment.topRight,
                end: isUser ? Alignment.bottomRight : Alignment.bottomLeft,
              ),
              boxShadow: isUser
                  ? AppShadowStyles.baseStyle
                  : [
                      BoxShadow(
                        color: darkAppColors.deepPurple.withAlpha(51),
                        blurRadius: 5,
                        spreadRadius: 1,
                      ),
                    ],
              border: Border.all(
                color: isUser
                    ? Colors.white.withAlpha(25) // User bor
                    : Colors.deepPurpleAccent,
                width: isUser ? 1 : 0.2,
              ),
            ),
            child: ClipRRect(
              borderRadius: const BorderRadius.all(Radius.circular(24)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 18,
                      vertical: 16,
                    ),
                    child: Text(
                      message['message'].toString(),
                      style: TextStyle(
                        color: Colors.white.withAlpha((255 * 0.9).toInt()),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (!isFeedsAvailable && !isUser && setHomePage)
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: ElevatedButton(
                onPressed: widget.onExploreMoreTap,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(100),
                  ),
                  foregroundColor: Colors.black,
                ),
                child: const Text('Explore Home'),
              ),
            ),
        ],
      ),
    );
  }
}
