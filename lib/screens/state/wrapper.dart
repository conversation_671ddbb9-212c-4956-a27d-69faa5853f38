import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/screens/auth/onboard_page.dart';
import 'package:vibeo/screens/home/<USER>';
import 'package:vibeo/screens/state/server_error_page.dart';
import 'package:vibeo/screens/state/splash_screen.dart';

class WrapperPage extends StatelessWidget {
  const WrapperPage({super.key});

  void listener(BuildContext context, UserState state) {
    final FirebaseAuth firebaseAuth = FirebaseAuth.instance;
    if (state is UserExists &&
        state.user != null &&
        firebaseAuth.currentUser != null) {
      OneSignal.login(state.user!.uid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, authState) {
        return MultiBlocListener(
          listeners: [
            BlocListener<AuthBloc, AuthState>(
              listener: (context, state) {
                if (state is Authenticated) {
                  // Check user existence when authenticated
                  context.read<UserBloc>().add(
                        CheckUserExists(state.userModel),
                      );
                }
              },
            ),
            BlocListener<UserBloc, UserState>(
              listener: (context, userState) {
                if (authState is Authenticated) {
                  listener(context, userState);

                  if (userState is UserNotFound &&
                      authState.hasAttemptedSignIn) {
                    RouteUtils.pushNamed(
                      context,
                      RoutePaths.phoneNumberPage,
                    );
                  }
                }
              },
            ),
          ],
          child: BlocBuilder<UserBloc, UserState>(
            builder: (context, userState) {
              // Show splash screen while loading or registering
              if (userState is UserLoading || userState is UserRegistering) {
                return const SplashScreen();
              }

              // Show home page if authenticated and user exists
              if (authState is Authenticated && userState is UserExists) {
                return const HomePage();
              }

              // Show onboard page if unauthenticated or user not found
              if (authState is Unauthenticated ||
                  userState is UserNotFound ||
                  userState is UserInitial ||
                  (authState is Authenticated && userState is UserNotFound)) {
                return const OnboardPage();
              }

              // Handle error states
              if (authState is AuthError || userState is UserError) {
                return const ServerErrorPage();
              }

              // Default to splash screen
              return const SplashScreen();
            },
          ),
        );
      },
    );
  }
}
