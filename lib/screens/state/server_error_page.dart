import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';

class ServerErrorPage extends StatelessWidget {
  const ServerErrorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: CupertinoNavigationBar(
        automaticallyImplyLeading: false,
        border: Border.all(color: Colors.transparent),
        backgroundColor: Colors.transparent,
        middle: const Text('Vibeo'),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black,
              darkAppColors.deepPurple,
            ],
          ),
        ),
        child: Safe<PERSON>rea(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon Container
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withAlpha(26),
                      border: Border.all(
                        color: Colors.white.withAlpha(51),
                      ),
                    ),
                    child: Icon(
                      Icons.cloud_off_rounded, // Changed icon
                      size: 80,
                      color: Colors.white.withAlpha(204),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Oops! The Beat Dropped Out! 🎵',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white.withAlpha(229),
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    "We're experiencing some technical difficulties. Please wait a few minutes and try again.",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white.withAlpha(178),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
