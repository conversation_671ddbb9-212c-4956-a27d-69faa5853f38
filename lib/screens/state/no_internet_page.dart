// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:vibeo/logic/internet/bloc/internet_bloc.dart';
// import 'package:vibeo/themes/constant_theme.dart';

// class NoInternetPage extends StatelessWidget {
//   const NoInternetPage({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: Colors.black,
//       body: Container(
//         decoration: BoxDecoration(
//           gradient: LinearGradient(
//             begin: Alignment.topCenter,
//             end: Alignment.bottomCenter,
//             colors: [
//               Colors.black,
//               darkAppColors.redShade,
//             ],
//           ),
//         ),
//         child: SafeArea(
//           child: Center(
//             child: Padding(
//               padding: const EdgeInsets.symmetric(horizontal: 24),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.center,
//                 children: [
//                   // Icon Container
//                   Container(
//                     padding: const EdgeInsets.all(24),
//                     decoration: BoxDecoration(
//                       shape: BoxShape.circle,
//                       color: Colors.white.withAlpha(26),
//                       border: Border.all(
//                         color: Colors.white.withAlpha(51),
//                       ),
//                     ),
//                     child: Icon(
//                       Icons.wifi_off_rounded,
//                       size: 80,
//                       color: Colors.white.withAlpha(204),
//                     ),
//                   ),
//                   const SizedBox(height: 40),
//                   Text(
//                     'No Internet Connection',
//                     style: TextStyle(
//                       color: Colors.white.withAlpha(229),
//                       fontSize: 24,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   ),
//                   const SizedBox(height: 16),
//                   Text(
//                     'Please check your internet connection and try again',
//                     textAlign: TextAlign.center,
//                     style: TextStyle(
//                       color: Colors.white.withAlpha(178),
//                       fontSize: 16,
//                     ),
//                   ),
//                   const SizedBox(height: 40),
//                   // Retry Button
//                   GestureDetector(
//                     onTap: () {
//                       context
//                           .read<InternetBloc>()
//                           .add(CheckInternetConnection());
//                     },
//                     child: Container(
//                       padding: const EdgeInsets.symmetric(
//                         horizontal: 32,
//                         vertical: 16,
//                       ),
//                       decoration: BoxDecoration(
//                         color: Colors.white.withAlpha(26),
//                         borderRadius: BorderRadius.all(Radius.circular(30),
//                         border: Border.all(
//                           color: Colors.white.withAlpha(51),
//                         ),
//                       ),
//                       child: Row(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Icon(
//                             Icons.refresh_rounded,
//                             color: Colors.white.withAlpha(229),
//                           ),
//                           const SizedBox(width: 8),
//                           Text(
//                             'Try Again',
//                             style: TextStyle(
//                               color: Colors.white.withAlpha(229),
//                               fontSize: 16,
//                               fontWeight: FontWeight.w600,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }
