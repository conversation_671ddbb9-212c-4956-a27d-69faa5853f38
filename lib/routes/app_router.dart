import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/models/venue/venue_model.dart';

import 'package:vibeo/routes/route.dart';
import 'package:vibeo/screens/feed/video_play_page.dart';
import 'package:vibeo/screens/profile/preferences_page.dart';
import 'package:vibeo/screens/screen.dart';

import 'package:vibeo/views/state/dismissible_wrapper.dart';

class AppRouter {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    if (settings.name?.startsWith('/link') == true &&
        settings.name?.contains('recaptchaToken') == true) {
      return null;
    }

    // Regular route handling
    switch (settings.name) {
      case RoutePaths.wrapper:
        return _buildRoute(
          settings: settings,
          page: const PopScope(
            canPop: false, // Prevents back navigation
            child: WrapperPage(),
          ),
        );
      case RoutePaths.home:
        return _buildRoute(
          settings: settings,
          page: const PopScope(
            canPop: false, // Prevents back navigation
            child: HomePage(),
          ),
        );
      case RoutePaths.explore:
        return _buildRoute(
          settings: settings,
          page: const ExplorePage(),
        );

      case RoutePaths.onboardPage:
        return _buildRoute(
          settings: settings,
          page: const PopScope(
            canPop: false, // Prevents back navigation
            child: OnboardPage(),
          ),
        );
      case RoutePaths.phoneNumberPage:
        return _buildRoute(
          settings: settings,
          page: const PhoneVerificationPage(),
        );
      case RoutePaths.otpPage:
        return _buildRoute(
          settings: settings,
          page: const OtpPage(),
        );
      case RoutePaths.fullNamePage:
        return _buildRoute(
          settings: settings,
          page: const FullNamePage(),
        );
      case RoutePaths.agePage:
        return _buildRoute(
          settings: settings,
          page: const AgePage(),
        );
      case RoutePaths.genrePage:
        return _buildRoute(
          settings: settings,
          page: const GenrePage(),
        );
      case RoutePaths.genderPage:
        return _buildRoute(
          settings: settings,
          page: const GenderPage(),
        );
      case RoutePaths.addContentPage:
        return MaterialPageRoute(
          settings: settings,
          builder: (context) => BlocProvider.value(
            value: BlocProvider.of<FeedBloc>(context),
            child: AddContentPage(settings: settings),
          ),
        );

      case RoutePaths.venueDescPage:
        return _buildRoute(
          settings: settings,
          page: VenueDescPage(settings: settings),
        );
      case RoutePaths.profileVibesPage:
        return _buildRoute(
          settings: settings,
          page: const ProfileVibesPage(),
        );
      case RoutePaths.vibesLikedPage:
        return _buildRoute(
          settings: settings,
          page: const VibesLikedPage(),
        );
      case RoutePaths.feedPlayingPage:
        return _buildRoute(
          settings: settings,
          shouldFade: true,
          page: FeedPlayingPage(settings: settings),
        );

      case RoutePaths.vibeFeedPlayingPage:
        return _buildRoute(
          settings: settings,
          shouldFade: true,
          page: VibeFeedPlayingPage(settings: settings),
        );
      case RoutePaths.videoPlayPage:
        return _buildRoute(
          settings: settings,
          shouldFade: true,
          page: VideoPlayPage(settings: settings),
        );

      case RoutePaths.settingsPage:
        return _buildRoute(
          settings: settings,
          page: const SettingsPage(),
        );
      case RoutePaths.preferencesPage:
        return _buildRoute(
          settings: settings,
          page: const PreferencesPage(),
        );
      case RoutePaths.savedVenuesPage:
        return _buildRoute(
          settings: settings,
          page: const SavedVenuesPage(),
        );

      case RoutePaths.categorizedOffersPage:
        if (settings.arguments is Map<String, dynamic>) {
          final args = settings.arguments! as Map<String, dynamic>;
          final venue = args['venue'] as VenueModel;

          return _buildRoute(
            settings: settings,
            page: CategorizedOffersPage(
              venue: venue,
            ),
          );
        }

      case RoutePaths.artistsFeedsPage:
        return _buildRoute(
          settings: settings,
          page: ArtistsFeedPage(
            settings: settings,
          ),
        );
      case RoutePaths.journeyPlannerPage:
        return _buildRoute(
          settings: settings,
          shouldFade: true,
          page: const JourneyPlannerPage(),
        );
      default:
        return _buildRoute(page: const SplashScreen(), settings: settings);
    }
    return null;
  }

  static Route<dynamic> _buildRoute({
    required Widget page,
    required RouteSettings settings,
    bool shouldFade = false,
  }) {
    final wrappedPage = DismissibleWrapper(child: page);
    if (Platform.isIOS && !shouldFade) {
      return CupertinoPageRoute(
        builder: (context) => wrappedPage,
        settings: settings,
      );
    }
    return SlideRouteBuilder(
      page: wrappedPage,
      settings: settings,
    );
  }

  // static Widget _guardedRoute(
  //   Widget page,
  //   RouteSettings settings, {
  //   BuildContext? context,
  // }) {
  //   if (!_isAuthenticated(context!)) {
  //     // Your authentication logic here
  //     return page;
  //   }
  //   return page;
  // }

  // Authentication check
  // static bool _isAuthenticated(BuildContext context) {
  //   // Replace with your actual auth check
  //   return context.read<UserBloc>().state.user != null;
  // }
}
