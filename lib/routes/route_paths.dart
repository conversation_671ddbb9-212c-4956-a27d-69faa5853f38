class RoutePaths {
  RoutePaths._();
  static const String home = '/home';
  static const String explore = '/explore';
  static const String onboardPage = '/onBoardPage';
  static const String phoneNumberPage = '/phoneNumber';
  static const String otpPage = '/otpPage';
  static const String fullNamePage = '/fullNamePage';
  static const String agePage = '/agePage';
  static const String genrePage = '/genrePage';
  static const String genderPage = '/genderPage';
  static const String videoRecordingPage = '/videoRecordingPage';
  static const String videoPreviewPage = '/videoPreviewPage';
  static const String addContentPage = '/addContentPage';
  static const String venueDescPage = '/venueDescPage';
  static const String profileVibesPage = '/profileVibesPage';
  static const String accountPage = '/accountPage';
  static const String settingsPage = '/settingsPage';
  static const String preferencesPage = '/preferencesPage';
  static const String vibesLikedPage = '/vibesLikedPage';
  static const String savedOffersPage = '/savedOffersPage';
  static const String savedVenuesPage = '/savedVenuesPage';
  static const String artistsFeedsPage = '/artistsFeedsPage';
  static const String feedPlayingPage = '/feedPlayingPage';
  static const String shrinkAndDragPage = '/shrinkAndDragPage';
  static const String vibeFeedPlayingPage = '/vibeFeedPlayingPage';
  static const String videoPlayPage = '/videoPlayPage';
  static const String basketDemoPage = '/basketDemoPage';
  static const String categorizedOffersPage = '/categorizedOffersPage';
  static const String journeyPlannerPage = '/journeyPlannerPage';
  static const String wrapper = '/wrapper';
}
