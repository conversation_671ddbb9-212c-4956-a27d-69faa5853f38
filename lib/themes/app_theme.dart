import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';

class AppTheme {
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    fontFamily: 'PulpDisplay',
    colorScheme: ColorScheme.fromSeed(
      seedColor: darkAppColors.deepPurple,
      brightness: Brightness.dark,
      primary: darkAppColors.primary,
      secondary: darkAppColors.secondary,
      surface: darkAppColors.backgroundColor,
      onSurface: Colors.white,
    ),
    primaryColor: darkAppColors.primary,
    scaffoldBackgroundColor: darkAppColors.backgroundColor,
    appBarTheme: AppBarTheme(
      backgroundColor: darkAppColors.backgroundColor,
      elevation: 0,
      iconTheme: IconThemeData(color: darkAppColors.primary),
      titleTextStyle: const TextStyle(
        fontFamily: 'PulpDisplay',
        fontSize: 17,
        fontWeight: FontWeight.w600,
        color: Colors.white,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: darkAppColors.backgroundColor.withAlpha(128),
      border: const OutlineInputBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        borderSide: BorderSide.none,
      ),
      enabledBorder: const OutlineInputBorder(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: const BorderRadius.all(Radius.circular(8)),
        borderSide: BorderSide(color: darkAppColors.primary),
      ),
    ),
    extensions: [darkAppColors],
  );
}

class AppColors extends ThemeExtension<AppColors> {
  AppColors({
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.backgroundColor,
    required this.secondaryBackgroundColor,
    required this.deepPurple,
    required this.purpleShade,
    required this.redShade,
    required this.lightColor,
    required this.errorColor,
  });
  final Color primary;
  final Color secondary;
  final Color accent;
  final Color backgroundColor;
  final Color secondaryBackgroundColor;
  final Color lightColor;
  final Color deepPurple;
  final Color purpleShade;
  final Color redShade;
  final Color errorColor;

  @override
  ThemeExtension<AppColors> copyWith({
    Color? primary,
    Color? secondary,
    Color? accent,
    Color? backgroundColor,
    Color? secondaryBackgroundColor,
    Color? deepPurple,
    Color? purpleShade,
    Color? redShade,
    Color? lightColor,
    Color? errorColor,
  }) {
    return AppColors(
      primary: primary ?? this.primary,
      secondary: secondary ?? this.secondary,
      accent: accent ?? this.accent,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      secondaryBackgroundColor:
          secondaryBackgroundColor ?? this.secondaryBackgroundColor,
      lightColor: lightColor ?? this.lightColor,
      deepPurple: deepPurple ?? this.deepPurple,
      purpleShade: purpleShade ?? this.purpleShade,
      redShade: redShade ?? this.redShade,
      errorColor: errorColor ?? this.errorColor,
    );
  }

  @override
  ThemeExtension<AppColors> lerp(ThemeExtension<AppColors>? other, double t) {
    if (other is! AppColors) return this;

    return AppColors(
      primary: Color.lerp(primary, other.primary, t)!,
      secondary: Color.lerp(secondary, other.secondary, t)!,
      accent: Color.lerp(accent, other.accent, t)!,
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t)!,
      secondaryBackgroundColor: Color.lerp(
        secondaryBackgroundColor,
        other.secondaryBackgroundColor,
        t,
      )!,
      deepPurple: Color.lerp(deepPurple, other.deepPurple, t)!,
      lightColor: Color.lerp(lightColor, other.lightColor, t)!,
      purpleShade: Color.lerp(purpleShade, other.purpleShade, t)!,
      redShade: Color.lerp(redShade, other.redShade, t)!,
      errorColor: Color.lerp(errorColor, other.errorColor, t)!,
    );
  }
}
