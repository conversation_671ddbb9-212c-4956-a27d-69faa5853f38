// lib/utils/text_utils.dart
import 'package:flutter/material.dart';
import 'package:vibeo/themes/constant_theme.dart';

class AppTextStyles {
  AppTextStyles._();

  // Base styles
  static TextStyle get _baseStyle => const TextStyle(
        color: Colors.white,
        fontFamily: 'PulpDisplay',
      );

  // Heading styles
  static TextStyle get heading1 => _baseStyle.copyWith(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        height: 1,
      );

  static TextStyle get heading2 => _baseStyle.copyWith(
        fontSize: 24,
        fontWeight: FontWeight.bold,
      );

  // The specific style you mentioned
  static TextStyle get headingTitle => _baseStyle.copyWith(
        fontSize: 22,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get title => _baseStyle.copyWith(
        fontSize: 20,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get subtitle => _baseStyle.copyWith(
        fontSize: 18,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get heading4 => _baseStyle.copyWith(
        fontSize: 16,
      );

  // Variations of the title style
  static TextStyle get titleLight => title.copyWith(
        fontWeight: FontWeight.w300,
      );

  static TextStyle get titleMedium => title.copyWith(
        fontWeight: FontWeight.w500,
      );

  static TextStyle get titleWithOpacity => title.copyWith(
        color: darkAppColors.lightColor.withAlpha(204),
      );

  // Body text styles
  static TextStyle get body => _baseStyle.copyWith(
        fontSize: 16,
        fontWeight: FontWeight.normal,
        height: 1.5,
      );

  static TextStyle get bodyBold => _baseStyle.copyWith(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        height: 1.5,
      );

  static TextStyle get bodySmall => _baseStyle.copyWith(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        height: 1.5,
      );

  static TextStyle get bodysmallBold => _baseStyle.copyWith(
        fontWeight: FontWeight.bold,
        height: 1.5,
        fontSize: 14,
      );

  static TextStyle get bodySmaller => _baseStyle.copyWith(
        fontSize: 12,
        fontWeight: FontWeight.normal,
        height: 1.5,
      );
}

// Usage extension for easier access
extension TextStyleExtension on TextStyle {
  TextStyle withOpacity(double opacity) {
    return copyWith(
      color: color?.withAlpha((opacity * 255).round()),
    );
  }
}
