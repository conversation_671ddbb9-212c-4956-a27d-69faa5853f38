import 'package:flutter/material.dart';

class AppShadowStyles {
  AppShadowStyles._();

  // Base styles
  static List<BoxShadow> get baseStyle => [
        // Outer shadow for depth
        BoxShadow(
          color: Colors.black.withAlpha(89),
          blurRadius: 20,
          spreadRadius: -2,
          offset: const Offset(0, 12),
        ),
        // Inner shadow for depth
        BoxShadow(
          color: Colors.black.withAlpha(64),
          blurRadius: 10,
          spreadRadius: -5,
          offset: const Offset(0, 5),
        ),
        // Subtle highlight at the top
        BoxShadow(
          color: Colors.white.withAlpha(26),
          blurRadius: 1,
          spreadRadius: 0,
          offset: const Offset(0, -1),
        ),
      ];

  static List<BoxShadow> get textFeildShadow => [
        BoxShadow(
          color: Colors.black.withAlpha(89),
          blurRadius: 5,
          spreadRadius: -2,
          offset: const Offset(0, 5),
        ),
      ];
}
