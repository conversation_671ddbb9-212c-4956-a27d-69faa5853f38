import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:vibeo/helper/open_web_link.dart';

import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/utils/utils.dart';

class NotificationHandlerWidget extends StatefulWidget {
  final GlobalKey<NavigatorState>? navigatorKey;
  final Widget child;

  const NotificationHandlerWidget({
    required this.child,
    required this.navigatorKey,
    super.key,
  });

  @override
  State<NotificationHandlerWidget> createState() =>
      _NotificationHandlerWidgetState();
}

class _NotificationHandlerWidgetState extends State<NotificationHandlerWidget> {
  @override
  void initState() {
    super.initState();

    setupNotificationHandlers();
    setupInAppMessageHandlers();
  }

  String? _lastProcessedNotificationID;
  String? _lastProcessedInAppID;
  bool _isNavigating = false;

  void setupNotificationHandlers() {
    OneSignal.Notifications.addClickListener((event) async {
      // Prevent duplicate processing
      if (_lastProcessedNotificationID == event.notification.notificationId ||
          _isNavigating) {
        return;
      }
      _lastProcessedNotificationID = event.notification.notificationId;
      _isNavigating = true;

      try {
        AppLogger.debug('Notification opened: ${event.notification.body}');

        final String? venueID =
            event.notification.additionalData?['venueID'] as String?;
        if (venueID == null) {
          AppLogger.debug('Venue ID is null');
          return;
        }

        final VenueRepository venueRepository = VenueRepository();
        final venue = await venueRepository.fetchVenueByID(id: venueID);

        if (mounted) {
          context.read<VenueBloc>().add(AddVenueEvent(venue));

          // Use a short delay to ensure state update
          await Future.delayed(const Duration(milliseconds: 50));

          if (widget.navigatorKey?.currentState != null) {
            await widget.navigatorKey!.currentState!.pushNamed(
              RoutePaths.venueDescPage,
              arguments: {
                'venue': venue,
                'isFromNotification': true,
              },
            );
          }
        }
      } catch (e) {
        AppLogger.debug('Error handling notification: $e');
      } finally {
        _isNavigating = false;
      }
    });
  }

  void setupInAppMessageHandlers() {
    OneSignal.InAppMessages.addClickListener((event) async {
      AppLogger.info('In-App Message clicked: ${event.result.actionId}');
      // Handle in-app message click
      if (event.result.actionId == 'claim_offer_external') {
        // Handle the claim offer button click
        AppLogger.info('Claim offer button clicked');

        await openEventLink(event.result.url!);
      } else if (event.result.actionId!.contains('claim_offer_internal')) {
        if (_lastProcessedInAppID == event.result.actionId || _isNavigating) {
          return;
        }
        _lastProcessedInAppID = event.result.actionId;
        _isNavigating = true;

        try {
          AppLogger.debug('Notification opened: ${event.result.actionId}');

          final String? venueID = event.result.actionId?.split('/').last;
          if (venueID == null) {
            AppLogger.debug('Venue ID is null');
            return;
          }

          final VenueRepository venueRepository = VenueRepository();
          final venue = await venueRepository.fetchVenueByID(id: venueID);

          if (mounted) {
            context.read<VenueBloc>().add(AddVenueEvent(venue));

            // Use a short delay to ensure state update
            await Future.delayed(const Duration(milliseconds: 50));

            if (widget.navigatorKey?.currentState != null) {
              await widget.navigatorKey!.currentState!.pushNamed(
                RoutePaths.venueDescPage,
                arguments: {
                  'venue': venue,
                  'isFromNotification': true,
                  'showOffersDirectly':
                      event.result.actionId?.split('/').first ==
                          'claim_offer_internal_offers',
                },
              );
            }
          }
        } catch (e) {
          AppLogger.debug('Error handling notification: $e');
        } finally {
          _isNavigating = false;
        }
      }
    });

    OneSignal.InAppMessages.addWillDisplayListener((event) {
      AppLogger.info('In-App Message will display: ${event.message.messageId}');
      // Handle in-app message display
    });

    OneSignal.InAppMessages.addDidDisplayListener((event) {
      AppLogger.info('In-App Message did display: ${event.message.messageId}');
      // Handle in-app message displayed
    });

    OneSignal.InAppMessages.addWillDismissListener((event) {
      AppLogger.info('In-App Message will dismiss: ${event.message.messageId}');
      // Handle in-app message dismissal
    });

    OneSignal.InAppMessages.addDidDismissListener((event) {
      AppLogger.info('In-App Message did dismiss: ${event.message.messageId}');
      // Handle in-app message dismissed
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
