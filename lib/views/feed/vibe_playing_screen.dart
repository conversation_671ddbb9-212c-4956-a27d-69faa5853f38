import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:cached_video_player_plus/cached_video_player_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/helper/helper.dart';
import 'package:vibeo/logic/feed/controller/feed_controller.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/views/view.dart';
import 'package:vibeo/widgets/feed/action_buttons.dart';

import 'package:vibeo/widgets/feed/bounce_button_animation.dart';
import 'package:vibeo/widgets/feed/live_widget.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class VibePlayingScreen extends StatefulWidget {
  final CachedVideoPlayerPlusController? controller;
  final String imageURL;
  final FeedModel feed;
  final int currentFeedIndex;
  final AnimationController animationController;
  final AnimationController fadeController;
  final int feedsLength;
  final String title;
  final VoidCallback stopVideo;
  final VoidCallback startVideo;
  final ValueNotifier<bool> likedNotifier;
  final bool fromVenuePage;
  const VibePlayingScreen({
    required this.controller,
    required this.imageURL,
    required this.feed,
    required this.currentFeedIndex,
    required this.animationController,
    required this.fadeController,
    required this.feedsLength,
    required this.title,
    required this.stopVideo,
    required this.startVideo,
    required this.likedNotifier,
    this.fromVenuePage = false,
    super.key,
  });

  @override
  State<VibePlayingScreen> createState() => _VibePlayingScreenState();
}

class _VibePlayingScreenState extends State<VibePlayingScreen> {
  late FeedController feedController;

  @override
  void initState() {
    super.initState();
    feedController = FeedController(context.read<UserBloc>().state.user!.uid);
  }

  Future<void> shareFeed() async {
    widget.stopVideo();
    final AnalyticsService analytics = AnalyticsService.instance;
    await analytics.trackVideoShare(widget.feed.id);

    // Create the query string dynamically
    final videoUrl = Uri.encodeComponent(widget.feed.videoURL);
    final venueName = Uri.encodeComponent(widget.feed.venueName);
    final tags = [
      widget.feed.venueType,
      vibeScoreDefinitions[widget.feed.vibeScore]!['name'],
    ]
        .whereType<String>()
        .map(
          Uri.encodeComponent,
        )
        .join('&param=');

    // Construct the full URL
    final shareUrl =
        'https://vibeocontent123.web.app/?video=$videoUrl&venue=$venueName&param=$tags';

    // Share the URL
    final result = await Share.share(
      'Check out this venue at Vibeo: $shareUrl',
    );

    if (result.status == ShareResultStatus.success) {
      widget.startVideo();
      AppLogger.debug(
        'Thank you for sharing my website!',
      );
    } else {
      widget.startVideo();
    }
  }

  void openBottomSheet() {
    widget.stopVideo();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: ColoredBox(
            color: const Color.fromRGBO(
              0,
              0,
              0,
              0.001,
            ),
            child: BounceBottomSheet(
              feed: widget.feed,
            ),
          ),
        );
      },
    ).then((_) {
      widget.startVideo();
    });
  }

  Widget _buildVideo() {
    if (widget.controller == null) {
      return ColoredBox(
        color: Colors.black,
        child: CachedNetworkImage(
          imageUrl: widget.imageURL,
          height: SizeUtils.screenHeight,
          fit: BoxFit.cover,
        ),
      );
    }

    return ColoredBox(
      color: Colors.black,
      child: Center(
        child: FadeTransition(
          opacity: widget.fadeController,
          child: ClipRect(
            child: SizedBox(
              height: SizeUtils.screenHeight,
              width: SizeUtils.screenWidth,
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: widget.controller!.value.size.width,
                  height: widget.controller!.value.size.height,
                  child: CachedVideoPlayerPlus(widget.controller!),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          foregroundDecoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.black38,
                Colors.transparent,
                Colors.black12,
                Colors.black,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: [0, 0.5, 0.6, 1],
            ),
          ),
          child: Center(child: _buildVideo()),
        ),
        buildProgressBars(
          widget.feedsLength,
          widget.animationController,
          widget.currentFeedIndex,
        ),
        SafeArea(
          child: Padding(
            padding: SizeUtils.horizontalPadding.copyWith(
              top: 10,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      widget.title,
                      style: AppTextStyles.bodySmall,
                    ),
                    Row(
                      children: [
                        if (isLiveTag(widget.feed.dateTime!))
                          const LiveWidget(),
                        const SizedBox(
                          width: 10,
                        ),
                        GestureDetector(
                          onTap: () {
                            RouteUtils.pop(context);
                          },
                          child: const Icon(
                            CupertinoIcons.chevron_down,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                // const SizedBox(
                //   height: 10,
                // ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: HapticButton(
                        onTap: () async {
                          if (widget.feed.venueID == null) {
                            return;
                          }
                          widget.stopVideo();

                          final VenueRepository venueRepository =
                              VenueRepository();
                          final venue = await venueRepository.fetchVenueByID(
                            id: widget.feed.venueID!,
                          );
                          if (context.mounted) {
                            context.read<VenueBloc>().add(AddVenueEvent(venue));

                            await RouteUtils.pushNamed(
                              context,
                              RoutePaths.venueDescPage,
                              arguments: {'venue': venue},
                            ).then((_) {
                              widget.startVideo();
                            });
                          }
                        },
                        child: Text(
                          widget.feed.venueName,
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.w900,
                          ),
                          softWrap: true,
                          overflow: TextOverflow.visible,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: SizeUtils.screenHeight * 0.025,
          left: 0,
          right: 0,
          child: Padding(
            padding: SizeUtils.horizontalPadding.copyWith(
              top: 20,
              bottom: 20,
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Left side info
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Time section

                        Text(
                          formatTimeAgo(widget.feed.dateTime!),
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                          ),
                        ),

                        // Music section
                        if (widget.feed.genre != null &&
                            widget.feed.genre != 'Discoveries')
                          Column(
                            children: [
                              const SizedBox(height: 10),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Icon(
                                    CupertinoIcons.music_note,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  ConstrainedBox(
                                    constraints: BoxConstraints(
                                      maxWidth:
                                          MediaQuery.of(context).size.width *
                                              0.6,
                                    ),
                                    child: widget.feed.liveMusic
                                        ? const Text(
                                            'Live Music',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          )
                                        : widget.feed.genre != 'Discoveries'
                                            ? Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Text(
                                                    widget.feed.genre!,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  ),
                                                  if (widget.feed.genre !=
                                                      'Discoveries') ...[
                                                    Text(
                                                      widget.feed.music!,
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: const TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                    Text(
                                                      widget.feed.artists!,
                                                      maxLines: 1,
                                                      overflow:
                                                          TextOverflow.ellipsis,
                                                      style: const TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ],
                                              )
                                            : const SizedBox(),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        // Venue section
                        if (widget.feed.coverCharge != null &&
                            widget.feed.coverCharge != 'null')
                          Column(
                            children: [
                              const SizedBox(height: 20),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.door_back_door_outlined,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    widget.feed.coverCharge!,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                      ],
                    ),
                    // Right side buttons
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ValueListenableBuilder<bool>(
                          valueListenable: widget.likedNotifier,
                          builder: (context, isLiked, child) {
                            return buildLikeButton(
                              label: '24.5K',
                              onTap: ({bool isLiked = false}) async {
                                widget.likedNotifier.value = isLiked;
                                await feedController
                                    .onFeedLikePressed(widget.feed.id);
                              },
                              liked: isLiked,
                            );
                          },
                        ),
                        buildShareButton(
                          icon: CupertinoIcons.paperplane,
                          label: 'Share',
                          onTap: shareFeed,
                        ),
                      ],
                    ),
                  ],
                ),
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(top: 28),
                  child: HapticButton(
                    onTap: openBottomSheet,
                    child: Column(
                      children: [
                        AnimatedGradientBorderContainer(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                          animate: widget.feed.perksType != PerksType.none &&
                              !widget.fromVenuePage,
                          child: ClipRRect(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(100)),
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  color:
                                      darkAppColors.deepPurple.withAlpha(120),
                                  borderRadius: BorderRadius.circular(100),
                                ),
                                child: Center(
                                  child: Text(
                                    widget.fromVenuePage
                                        ? 'Explore More'
                                        : perksText(
                                            widget.feed.perksType,
                                            widget.feed.title,
                                          ),
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String perksText(PerksType? perksType, String? title) {
    switch (perksType) {
      case PerksType.offer:
        return title != null ? 'Claim $title' : 'Explore Offers';
      case PerksType.promoter:
        return 'Get Entry';
      case PerksType.both:
        return 'Explore Perks';
      case PerksType.exclusive:
        return 'Explore Exclusive Offers';
      default:
        return 'Explore More';
    }
  }
}
