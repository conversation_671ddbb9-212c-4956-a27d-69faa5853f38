import 'dart:math';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/logic/cubit/perks_cubit_cubit.dart';

import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/promoters/promoter_model.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/screens/home/<USER>';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/widgets/dialogs/location_dialog.dart';
import 'package:vibeo/widgets/feed/energy_widget.dart';

import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:vibeo/widgets/offer/offer_tile.dart';
import 'package:vibeo/widgets/venue/promoter_tile.dart';
import 'package:vibeo/widgets/video_tiles/vibe_tags.dart';

class BounceBottomSheet extends StatefulWidget {
  final FeedModel feed;
  const BounceBottomSheet({required this.feed, super.key});

  @override
  State<BounceBottomSheet> createState() => _BounceBottomSheetState();
}

class _BounceBottomSheetState extends State<BounceBottomSheet>
    with SingleTickerProviderStateMixin {
  late FeedModel _feed;
  bool _isLoading = false;

  late AnimationController _controller;
  // Animation status
  bool _isAnimating = false;

  // Store the starting position of the product
  Offset? _startPosition;

  // Store the target position (cart position)
  Offset? _endPosition;

  // The widget that will fly to the cart
  Widget? _flyingWidget;

  // Cart item count
  int _cartItemCount = 0;

  // Key to get cart position
  final GlobalKey _cartKey = GlobalKey();

  // Lists to hold categorized offers
  final List<OfferModel> _drops = [];
  final List<OfferModel> _timeBasedOffers = [];
  final List<OfferModel> _expiringOffers = [];
  final List<OfferModel> _redeemingOffers = [];
  final List<OfferModel> _regularOffers = [];
  final List<OfferModel> _lockedOffers = [];

  final List<String> _categories = [
    'All',
    'Promoters',
    'Drops',
    'Limited-Time Perks',
    'Ongoing Deals',
    'Spotted Deals',
    'Unlockable Offers',
  ];

  int selectedCategoryIndex = 0;

  @override
  void initState() {
    super.initState();
    _feed = widget.feed;

    // Initialize the controller in initState to avoid LateInitializationError
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _isAnimating = false;
          _cartItemCount++;
        });
      }
    });

    _fetchPerks();
    addAnalytics();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _runAddToCartAnimation(
    BuildContext context,
    Widget productWidget,
    Offset productPosition,
  ) {
    // Get the cart position
    final RenderBox? cartBox =
        _cartKey.currentContext?.findRenderObject() as RenderBox?;
    if (cartBox == null) return;

    final Offset cartPosition = cartBox.localToGlobal(Offset.zero);

    // Center of the cart icon
    final double cartCenterX = cartPosition.dx;
    final double cartCenterY = cartPosition.dy;

    setState(() {
      _startPosition = productPosition;
      _endPosition = Offset(cartCenterX, cartCenterY);
      _flyingWidget = productWidget;
      _isAnimating = true;
      _controller
        ..reset()
        ..forward();
    });
  }

  Future<void> addAnalytics() async {
    final AnalyticsService analytics = AnalyticsService.instance;
    await analytics.trackMoreButtonClick(_feed.id);
  }

  void addDummyOffers() {
    final newOffers = _feed.offers;

    // Controller is now initialized in initState
    for (final offer in newOffers) {
      // Skip redeemed offers
      if (offer.isRedeemedToday) {
        continue;
      }

      // Check if it's a locked offer (for the "Locked Offers" section)
      if (offer.isLocked) {
        _lockedOffers.add(offer);
        continue;
      }

      // Check if it's a drop (special time-limited offer)
      if (offer.voucherCategory == VoucherCategory.DROPS) {
        _drops.add(offer);
        continue;
      } else if (offer.voucherType == VoucherType.REGULAR) {
        _regularOffers.add(offer);
        continue;
      } else if (offer.voucherCategory == VoucherCategory.ONGOING) {
        _redeemingOffers.add(offer);
        continue;
      } else if (offer.voucherCategory == VoucherCategory.TIMEBASED) {
        _timeBasedOffers.add(offer);
        continue;
      } else if (offer.voucherCategory == VoucherCategory.LIMITED) {
        _expiringOffers.add(offer);
        continue;
      } else {
        _regularOffers.add(offer);
        continue;
      }
    }

    // Sort offers by priority
    _timeBasedOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _redeemingOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _regularOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _drops
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _expiringOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));
    _lockedOffers
      ..sort((a, b) => b.weekdays.length.compareTo(a.weekdays.length))
      ..sort((a, b) => a.priority.compareTo(b.priority));

    setState(() {
      if (_feed.promoters.isEmpty) {
        _categories.remove('Promoters');
      }
      if (_drops.isEmpty && _categories.contains('Drops')) {
        _categories.remove('Drops');
      }
      if (_timeBasedOffers.isEmpty &&
          _categories.contains('Limited-Time Perks')) {
        _categories.remove('Limited-Time Perks');
      }
      if (_redeemingOffers.isEmpty && _categories.contains('Ongoing Deals')) {
        _categories.remove('Ongoing Deals');
      }
      if (_regularOffers.isEmpty && _categories.contains('Spotted Deals')) {
        _categories.remove('Spotted Deals');
      }
      if (_lockedOffers.isEmpty && _categories.contains('Unlockable Offers')) {
        _categories.remove('Unlockable Offers');
      }
    });
  }

  Future<void> _fetchPerks() async {
    if (_feed.venueID == null) {
      return;
    }

    final perksCubit = context.read<PerksCubit>();
    final currentPerks = perksCubit.state.perks;
    final String placeIdentifier = _feed.venueID!;

    if (currentPerks.containsKey(placeIdentifier)) {
      setState(() {
        _feed = _feed.copyWith(
          offers: currentPerks[placeIdentifier]!['offers'] as List<OfferModel>,
          promoters: currentPerks[placeIdentifier]!['promoters']
              as List<PromoterModel>,
        );
      });
      addDummyOffers();
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });
      final user = context.read<UserBloc>().state.user;
      await perksCubit.fetchPerks(
        venueId: _feed.venueID!,
        userId: user!.uid,
        userEmail: user.email,
      );

      final updatedPerks = perksCubit.state.perks;

      setState(() {
        _feed = _feed.copyWith(
          offers: updatedPerks[placeIdentifier]!['offers'] as List<OfferModel>,
          promoters: updatedPerks[placeIdentifier]!['promoters']
              as List<PromoterModel>,
        );
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      AppLogger.error('Failed to fetch perks: $e');
    } finally {
      addDummyOffers();
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildOfferSection(
    String title,
    List<OfferModel> offers,
    IconData icon,
    List<Color> gradientColors,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with gradient background
        Container(
          margin: const EdgeInsets.only(bottom: 16, top: 8),

          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(16),
          //   border: Border.all(
          //     color: gradientColors[0].withAlpha(76),
          //     width: 1,
          //   ),
          //   boxShadow: [
          //     BoxShadow(
          //       color: gradientColors[0].withAlpha(30),
          //       blurRadius: 8,
          //       offset: const Offset(0, 2),
          //     ),
          //   ],
          // ),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: gradientColors[1].withAlpha(40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              // Count badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: gradientColors[1].withAlpha(51),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: gradientColors[1].withAlpha(102),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${offers.length}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Offers list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 4, bottom: 20),
          itemCount: offers.length,
          itemBuilder: (ctx, index) => OfferTile(
            offer: offers[index],
            canNavigate: true,
            canRedeem: false,
            canAddToCart: !offers[index].isLocked,
            onAddToCart: (productWidget, productPosition) =>
                _runAddToCartAnimation(
              context,
              productWidget,
              productPosition,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        DraggableScrollableSheet(
          initialChildSize: 0.9,
          minChildSize: 0.9,
          maxChildSize: _isLoading
              ? 0.9
              : (_feed.promoters.isNotEmpty || _feed.offers.isNotEmpty)
                  ? 0.9
                  : 0.9,
          builder: (_, controller) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(
                    25,
                  ),
                ),
                border: Border(
                  top: BorderSide(
                    color: darkAppColors.lightColor.withAlpha(51),
                    width: 1,
                  ),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withAlpha((255 * 0.1).toInt()),
                          Colors.white.withAlpha((255 * 0.05).toInt()),
                        ],
                      ),
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(25),
                      ),
                      border: Border.all(
                        color: Colors.white.withAlpha((255 * 0.2).toInt()),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Center(
                          child: Icon(
                            Icons.remove,
                            color: Colors.white54,
                            size: 30,
                          ),
                        ),
                        Expanded(
                          child: Padding(
                            padding: SizeUtils.horizontalPadding.copyWith(
                              top: 8,
                            ),
                            child: SingleChildScrollView(
                              physics: const NeverScrollableScrollPhysics(),
                              clipBehavior: Clip.none,
                              controller: controller,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Flexible(
                                        child: Text(
                                          widget.feed.venueName,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          style: AppTextStyles.heading2,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        '${widget.feed.venueType ?? ''}${(widget.feed.venueType != null && widget.feed.coverCharge != null && widget.feed.coverCharge != 'null') ? ' . ' : ''}${widget.feed.coverCharge ?? ''}',
                                        style: AppTextStyles.body.copyWith(
                                          color: Colors.white70,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 8,
                                      ),
                                      EnergyWidget(
                                        vibeScore: widget.feed.vibeScore,
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 16,
                                  ),
                                  SizedBox(
                                    height: 40,
                                    child: ListView(
                                      clipBehavior: Clip.none,
                                      scrollDirection: Axis.horizontal,
                                      children: [
                                        if (widget.feed.venueID != null)
                                          Row(
                                            children: [
                                              HapticButton(
                                                onTap: () async {
                                                  final VenueRepository
                                                      venueRepository =
                                                      VenueRepository();
                                                  final venue =
                                                      await venueRepository
                                                          .fetchVenueByID(
                                                    id: widget.feed.venueID!,
                                                  );
                                                  if (context.mounted) {
                                                    context
                                                        .read<VenueBloc>()
                                                        .add(
                                                          AddVenueEvent(venue),
                                                        );

                                                    await RouteUtils.pushNamed(
                                                      context,
                                                      RoutePaths.venueDescPage,
                                                      arguments: {
                                                        'venue': venue,
                                                      },
                                                    );
                                                  }
                                                },
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius:
                                                        const BorderRadius.all(
                                                      Radius.circular(100),
                                                    ),
                                                    gradient: LinearGradient(
                                                      colors: [
                                                        Colors.purple.withAlpha(
                                                          (255 * 0.3).toInt(),
                                                        ),
                                                        Colors.blue.withAlpha(
                                                          (255 * 0.3).toInt(),
                                                        ),
                                                      ],
                                                    ),
                                                    boxShadow: [
                                                      BoxShadow(
                                                        color: Colors.purple
                                                            .withAlpha(
                                                          (255 * 0.2).toInt(),
                                                        ),
                                                        blurRadius: 15,
                                                        spreadRadius: 1,
                                                      ),
                                                    ],
                                                  ),
                                                  child: Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                      horizontal: 16,
                                                      vertical: 8,
                                                    ),
                                                    decoration: BoxDecoration(
                                                      border: Border.all(
                                                        color: Colors.white
                                                            .withAlpha(
                                                          (255 * 0.2).toInt(),
                                                        ),
                                                        width: 1,
                                                      ),
                                                      borderRadius:
                                                          const BorderRadius
                                                              .all(
                                                        Radius.circular(100),
                                                      ),
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          CupertinoIcons
                                                              .building_2_fill,
                                                          color: Colors.white
                                                              .withAlpha(
                                                            (255 * 0.9).toInt(),
                                                          ),
                                                          size: 20,
                                                        ),
                                                        const SizedBox(
                                                          width: 8,
                                                        ),
                                                        Text(
                                                          'Venue',
                                                          style: TextStyle(
                                                            color: Colors.white
                                                                .withAlpha(
                                                              (255 * 0.9)
                                                                  .toInt(),
                                                            ),
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              const SizedBox(width: 12),
                                            ],
                                          ),
                                        HapticButton(
                                          onTap: () {
                                            showLocBottomSheet(
                                              widget.feed.id,
                                              '${widget.feed.venueName}, ${widget.feed.address}',
                                              context,
                                              isGlassmorphism: false,
                                            );
                                          },
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  const BorderRadius.all(
                                                Radius.circular(100),
                                              ),
                                              gradient: LinearGradient(
                                                colors: [
                                                  Colors.blue.withAlpha(
                                                    (255 * 0.3).toInt(),
                                                  ),
                                                  Colors.teal.withAlpha(
                                                    (255 * 0.3).toInt(),
                                                  ),
                                                ],
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.blue.withAlpha(
                                                    (255 * 0.2).toInt(),
                                                  ),
                                                  blurRadius: 15,
                                                  spreadRadius: 1,
                                                ),
                                              ],
                                            ),
                                            child: Container(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 16,
                                                vertical: 8,
                                              ),
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                  color: Colors.white.withAlpha(
                                                    (255 * 0.2).toInt(),
                                                  ),
                                                  width: 1,
                                                ),
                                                borderRadius:
                                                    const BorderRadius.all(
                                                  Radius.circular(100),
                                                ),
                                              ),
                                              child: Row(
                                                children: [
                                                  Icon(
                                                    Icons.directions,
                                                    color:
                                                        Colors.white.withAlpha(
                                                      (255 * 0.9).toInt(),
                                                    ),
                                                    size: 20,
                                                  ),
                                                  const SizedBox(width: 8),
                                                  Text(
                                                    'Get there',
                                                    style: TextStyle(
                                                      color: Colors.white
                                                          .withAlpha(
                                                        (255 * 0.9).toInt(),
                                                      ),
                                                      fontWeight:
                                                          FontWeight.w600,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        if (_feed.offers.isNotEmpty)
                                          HapticButton(
                                            onTap: () {
                                              Navigator.of(context)
                                                  .pushAndRemoveUntil(
                                                MaterialPageRoute(
                                                  builder: (context) {
                                                    return const HomePage(
                                                      initialIndex: 2,
                                                    );
                                                  },
                                                ),
                                                (route) => false,
                                              );
                                            },
                                            child: Container(
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    const BorderRadius.all(
                                                  Radius.circular(100),
                                                ),
                                                gradient: LinearGradient(
                                                  colors: [
                                                    Colors.white.withAlpha(
                                                      (255 * 0.3).toInt(),
                                                    ),
                                                    Colors.white.withAlpha(
                                                      (255 * 0.3).toInt(),
                                                    ),
                                                  ],
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color:
                                                        Colors.white.withAlpha(
                                                      (255 * 0.2).toInt(),
                                                    ),
                                                    blurRadius: 15,
                                                    spreadRadius: 1,
                                                  ),
                                                ],
                                              ),
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                  horizontal: 16,
                                                  vertical: 8,
                                                ),
                                                decoration: BoxDecoration(
                                                  border: Border.all(
                                                    color:
                                                        Colors.white.withAlpha(
                                                      (255 * 0.2).toInt(),
                                                    ),
                                                    width: 1,
                                                  ),
                                                  borderRadius:
                                                      const BorderRadius.all(
                                                    Radius.circular(100),
                                                  ),
                                                ),
                                                child: Row(
                                                  children: [
                                                    SizedBox(
                                                      child: Stack(
                                                        alignment:
                                                            Alignment.center,
                                                        children: [
                                                          Icon(
                                                            Icons
                                                                .shopping_bag_rounded,
                                                            key: _cartKey,
                                                            color: Colors.white
                                                                .withAlpha(
                                                              (255 * 0.9)
                                                                  .toInt(),
                                                            ),
                                                            size: 20,
                                                          ),
                                                          if (_cartItemCount >
                                                              0)
                                                            Positioned(
                                                              right: 1,
                                                              top: 1,
                                                              child: Container(
                                                                padding:
                                                                    const EdgeInsets
                                                                        .all(2),
                                                                decoration:
                                                                    BoxDecoration(
                                                                  color: darkAppColors
                                                                      .deepPurple,
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                    10,
                                                                  ),
                                                                ),
                                                                constraints:
                                                                    const BoxConstraints(
                                                                  minWidth: 12,
                                                                  minHeight: 12,
                                                                ),
                                                                child: Text(
                                                                  '$_cartItemCount',
                                                                  style:
                                                                      const TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                    fontSize: 8,
                                                                  ),
                                                                  textAlign:
                                                                      TextAlign
                                                                          .center,
                                                                ),
                                                              ),
                                                            ),
                                                        ],
                                                      ),
                                                    ),
                                                    const SizedBox(width: 8),
                                                    Text(
                                                      'Bag',
                                                      style: TextStyle(
                                                        color: Colors.white
                                                            .withAlpha(
                                                          (255 * 0.9).toInt(),
                                                        ),
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        const SizedBox(width: 12),
                                        if (context
                                                .read<UserBloc>()
                                                .state
                                                .user!
                                                .contentCreator &&
                                            widget.feed.description != null)
                                          ElevatedButton(
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.white,
                                              foregroundColor: Colors.black,
                                              shape:
                                                  const RoundedRectangleBorder(
                                                borderRadius: BorderRadius.all(
                                                  Radius.circular(100),
                                                ),
                                              ),
                                            ),
                                            onPressed: () {
                                              if (widget.feed.description ==
                                                  null) {
                                                return;
                                              }
                                              showDialog(
                                                context: context,
                                                builder: (context) => Dialog(
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  child: Container(
                                                    decoration: BoxDecoration(
                                                      color: Colors.black
                                                          .withAlpha(
                                                        (255 * 0.7).toInt(),
                                                      ),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                        20,
                                                      ),
                                                      border: Border.all(
                                                        color: Colors.white
                                                            .withAlpha(
                                                          (255 * 0.2).toInt(),
                                                        ),
                                                      ),
                                                    ),
                                                    padding:
                                                        const EdgeInsets.all(
                                                      20,
                                                    ),
                                                    child:
                                                        SingleChildScrollView(
                                                      child: Column(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          const Text(
                                                            'Vision Insights',
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 24,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            height: 20,
                                                          ),
                                                          Text(
                                                            widget.feed
                                                                .description!,
                                                            style:
                                                                const TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 16,
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            height: 20,
                                                          ),
                                                          Row(
                                                            mainAxisAlignment:
                                                                MainAxisAlignment
                                                                    .end,
                                                            children: [
                                                              TextButton(
                                                                onPressed: () =>
                                                                    Navigator
                                                                        .pop(
                                                                  context,
                                                                ),
                                                                child:
                                                                    const Text(
                                                                  'Close',
                                                                  style:
                                                                      TextStyle(
                                                                    color: Colors
                                                                        .white,
                                                                  ),
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                            child: const Text('Vision'),
                                          ),
                                      ],
                                    ),
                                  ),
                                  if (_isLoading)
                                    const Center(
                                      child: LoadingWidget(),
                                    )
                                  else if (_feed.offers.isNotEmpty ||
                                      _feed.promoters.isNotEmpty)
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        if (_feed.promoters.isNotEmpty &&
                                            _feed.offers.isEmpty)
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.all(26),
                                                child: Divider(
                                                  color: Colors.white.withAlpha(
                                                    (255 * 0.2).toInt(),
                                                  ),
                                                  height: 0.5,
                                                ),
                                              ),
                                              Text(
                                                'Promoter',
                                                style: AppTextStyles.title,
                                              ),
                                              const SizedBox(
                                                height: 8,
                                              ),
                                              SizedBox(
                                                height: 180,
                                                child: PageView.builder(
                                                  padEnds: false,
                                                  itemCount:
                                                      _feed.promoters.length,
                                                  pageSnapping: true,
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  clipBehavior: Clip.none,
                                                  itemBuilder: (ctx, index) {
                                                    final promoter =
                                                        _feed.promoters[index];
                                                    return PromoterTile(
                                                      promoter: promoter,
                                                      venueID:
                                                          widget.feed.venueID!,
                                                    );
                                                  },
                                                ),
                                              ),
                                            ],
                                          ),
                                        if (_feed.offers.isNotEmpty)
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Padding(
                                                padding:
                                                    const EdgeInsets.all(26),
                                                child: Divider(
                                                  color: Colors.white.withAlpha(
                                                    (255 * 0.2).toInt(),
                                                  ),
                                                  height: 0.5,
                                                ),
                                              ),
                                              Text(
                                                'Perks',
                                                style: AppTextStyles.title,
                                              ),
                                              SizedBox(
                                                height: 48,
                                                child: ListView.builder(
                                                  scrollDirection:
                                                      Axis.horizontal,
                                                  itemCount: _categories.length,
                                                  clipBehavior: Clip.none,
                                                  itemBuilder: (ctx, index) =>
                                                      _isLoading
                                                          ? buildShimmerTag()
                                                          : buildVibeTag(
                                                              isSelected:
                                                                  selectedCategoryIndex ==
                                                                      index,
                                                              onTap: () {
                                                                setState(() {
                                                                  selectedCategoryIndex =
                                                                      index;
                                                                });
                                                              },
                                                              title:
                                                                  _categories[
                                                                      index],
                                                              isLive: false,
                                                            ),
                                                ),
                                              ),
                                              const SizedBox(
                                                height: 8,
                                              ),
                                              SizedBox(
                                                height: 600,
                                                child: buildScrollableWithFade(
                                                  child: SingleChildScrollView(
                                                    padding:
                                                        const EdgeInsets.only(
                                                      top: 6,
                                                      bottom: 140,
                                                    ),
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        if (_feed.promoters
                                                                .isNotEmpty &&
                                                            _categories[
                                                                    selectedCategoryIndex] ==
                                                                'Promoters')
                                                          SizedBox(
                                                            height: 180,
                                                            child: PageView
                                                                .builder(
                                                              padEnds: false,
                                                              itemCount: _feed
                                                                  .promoters
                                                                  .length,
                                                              pageSnapping:
                                                                  true,
                                                              scrollDirection:
                                                                  Axis.horizontal,
                                                              clipBehavior:
                                                                  Clip.none,
                                                              itemBuilder:
                                                                  (ctx, index) {
                                                                final promoter =
                                                                    _feed.promoters[
                                                                        index];
                                                                return PromoterTile(
                                                                  promoter:
                                                                      promoter,
                                                                  venueID: widget
                                                                      .feed
                                                                      .venueID!,
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                        // Drops section
                                                        if (_drops.isNotEmpty &&
                                                            (_categories[
                                                                        selectedCategoryIndex] ==
                                                                    'Drops' ||
                                                                selectedCategoryIndex ==
                                                                    0))
                                                          _buildOfferSection(
                                                            'Drops',
                                                            _drops,
                                                            Icons.flash_on,
                                                            [
                                                              Colors.purple
                                                                  .shade400,
                                                              Colors.deepPurple
                                                                  .shade600,
                                                            ],
                                                          ),

                                                        // Time-based Offers section
                                                        if (_timeBasedOffers
                                                                .isNotEmpty &&
                                                            (_categories[
                                                                        selectedCategoryIndex] ==
                                                                    'Limited-Time Perks' ||
                                                                selectedCategoryIndex ==
                                                                    0))
                                                          _buildOfferSection(
                                                            'Limited-Time Perks',
                                                            _timeBasedOffers,
                                                            CupertinoIcons.time,
                                                            [
                                                              Colors.orange
                                                                  .shade400,
                                                              Colors.deepOrange
                                                                  .shade600,
                                                            ],
                                                          ),

                                                        // Redeeming Offers section
                                                        if (_redeemingOffers
                                                                .isNotEmpty &&
                                                            (_categories[
                                                                        selectedCategoryIndex] ==
                                                                    'Ongoing Deals' ||
                                                                selectedCategoryIndex ==
                                                                    0))
                                                          _buildOfferSection(
                                                            'Ongoing Deals',
                                                            _redeemingOffers,
                                                            CupertinoIcons.gift,
                                                            [
                                                              Colors.green
                                                                  .shade400,
                                                              Colors.teal
                                                                  .shade600,
                                                            ],
                                                          ),

                                                        // Regular Offers section
                                                        if (_regularOffers
                                                                .isNotEmpty &&
                                                            (_categories[
                                                                        selectedCategoryIndex] ==
                                                                    'Spotted Deals' ||
                                                                selectedCategoryIndex ==
                                                                    0))
                                                          _buildOfferSection(
                                                            'Spotted Deals',
                                                            _regularOffers,
                                                            CupertinoIcons.tag,
                                                            [
                                                              Colors.blue
                                                                  .shade400,
                                                              Colors.indigo
                                                                  .shade600,
                                                            ],
                                                          ),

                                                        // Locked Offers section
                                                        if (_lockedOffers
                                                                .isNotEmpty &&
                                                            (_categories[
                                                                        selectedCategoryIndex] ==
                                                                    'Unlockable Offers' ||
                                                                selectedCategoryIndex ==
                                                                    0))
                                                          _buildOfferSection(
                                                            'Unlockable Offers',
                                                            _lockedOffers,
                                                            CupertinoIcons.lock,
                                                            [
                                                              Colors.pink
                                                                  .shade400,
                                                              Colors
                                                                  .red.shade600,
                                                            ],
                                                          ),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                      ],
                                    )
                                  else
                                    SizedBox(
                                      height: 500,
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Image.asset(
                                              'assets/filters/Offers.png',
                                              width: 140,
                                              height: 140,
                                            ),
                                            const SizedBox(height: 16),
                                            Text(
                                              'No offers available',
                                              style: AppTextStyles.heading1,
                                            ),
                                            const SizedBox(height: 8),
                                            Text(
                                              'Check back later for more offers.',
                                              style: AppTextStyles.body,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
        if (_isAnimating &&
            _startPosition != null &&
            _endPosition != null &&
            _flyingWidget != null)
          AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              // Calculate current position with a curve
              final curvedValue = Curves.easeInOut.transform(_controller.value);

              // Add a slight arc to the path
              final double arcX = _startPosition!.dx +
                  (_endPosition!.dx - _startPosition!.dx) * curvedValue;
              final double arcY = _startPosition!.dy +
                  (_endPosition!.dy - _startPosition!.dy) * curvedValue -
                  sin(curvedValue * 3.14) * 100; // Arc height

              // Scale down as it approaches the cart
              final scale = 1.0 - (curvedValue * 0.4);

              // Rotate slightly during flight
              final rotate = curvedValue * 0.5;

              return Positioned(
                left: arcX - 20 * scale, // Adjust based on your widget size
                top: arcY - 20 * scale, // Adjust based on your widget size
                child: Transform.scale(
                  scale: scale,
                  child: Transform.rotate(
                    angle: rotate,
                    child: Opacity(
                      opacity: 1.0 - (curvedValue * 0.3),
                      child: ClipRRect(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(100)),
                        child: SizedBox(
                          width: 40,
                          height: 40,
                          child: _flyingWidget,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }
}
