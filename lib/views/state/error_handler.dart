import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/exception/exception_bloc.dart';
import 'package:vibeo/views/state/something_went_wrong_screen.dart';

class ErrorHandler extends StatelessWidget {
  final Widget child;
  final VoidCallback onRetry;

  const ErrorHandler({
    required this.child,
    required this.onRetry,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<GlobalErrorBloc, GlobalErrorState>(
      builder: (context, state) {
        if (state is GlobalErrorOccurred) {
          // Show the error UI inline
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 48),
            child: SomethingWentWrong(
              onRetry: onRetry,
            ),
          );
        } else {
          // Show the normal content
          return child;
        }
      },
    );
  }
}
