import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:vibeo/utils/size_utils.dart';

class SomethingWentWrong extends StatelessWidget {
  final VoidCallback onRetry;
  const SomethingWentWrong({required this.onRetry, super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        SizedBox(
          height: SizeUtils.screenHeight * 0.5,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withAlpha(26),
                  border: Border.all(
                    color: Colors.white.withAlpha(51),
                  ),
                ),
                child: Icon(
                  CupertinoIcons.exclamationmark,
                  size: 50,
                  color: Colors.white.withAlpha(204),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                'Something Went Wrong',
                style: TextStyle(
                  color: Colors.white.withAlpha(229),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Hmm, something’s not right. Tap below to retry or check back shortly!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white.withAlpha(178),
                  fontSize: 16,
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              GestureDetector(
                onTap: onRetry,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(26),
                    borderRadius: const BorderRadius.all(Radius.circular(30)),
                    border: Border.all(
                      color: Colors.white.withAlpha(51),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.refresh_rounded,
                        color: Colors.white.withAlpha(229),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Try Again',
                        style: TextStyle(
                          color: Colors.white.withAlpha(229),
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
