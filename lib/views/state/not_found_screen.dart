import 'package:flutter/material.dart';
import 'package:vibeo/utils/size_utils.dart';

class NoResultsFoundScreen extends StatelessWidget {
  final String title;
  final IconData icon;
  final String description;
  const NoResultsFoundScreen({
    required this.title,
    required this.icon,
    required this.description,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: SizeUtils.screenHeight * 0.7,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withAlpha(26),
              border: Border.all(
                color: Colors.white.withAlpha(51),
              ),
            ),
            child: Icon(
              icon,
              size: 50,
              color: Colors.white.withAlpha(204),
            ),
          ),
          const SizedBox(height: 10),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.with<PERSON><PERSON><PERSON>(229),
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            description,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withAlpha(178),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}
