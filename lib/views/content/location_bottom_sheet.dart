import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_google_maps_webservices/places.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:vibeo/config/config.dart';
import 'package:vibeo/models/location/auto_complete_prediction.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';

abstract class _ListItem {
  const _ListItem();
}

class _PredictionItem extends _ListItem {
  final AutoCompletePrediction prediction;
  _PredictionItem(this.prediction);
}

class _ResultItem extends _ListItem {
  final PlacesSearchResult result;
  _ResultItem(this.result);
}

class LocationBottomSheet extends StatefulWidget {
  final String selectedLoc;
  final Function onLocationSelected;

  const LocationBottomSheet({
    required this.onLocationSelected,
    required this.selectedLoc,
    super.key,
  });

  @override
  State<LocationBottomSheet> createState() => _LocationBottomSheetState();
}

class _LocationBottomSheetState extends State<LocationBottomSheet> {
  final TextEditingController _searchController = TextEditingController();
  List<AutoCompletePrediction> placePredictions = [];
  String? selectedPreciseLoc;
  final List<PlacesSearchResult> _searchResults = [];
  LatLng? _currentLocation;
  bool _isSelectingLocation = false;
  Timer? _debounceTimer;

  List<_ListItem> _displayList = [];

  @override
  void initState() {
    super.initState();
    selectedPreciseLoc = widget.selectedLoc;
    _loadInitialVenues();
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.dispose();

    placePredictions = [];
    _searchResults.clear();
    _displayList = [];

    super.dispose();
  }

  Future<void> _loadInitialVenues() async {
    if (_searchResults.isEmpty) {
      final venues = await _getNearbyVenues();

      if (!mounted) return;

      setState(() {
        _searchResults.addAll(venues);
        _displayList = _searchResults.map(_ResultItem.new).toList();
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final Position position = await Geolocator.getCurrentPosition();
      setState(() {
        _currentLocation = LatLng(position.latitude, position.longitude);
      });
    } on Exception catch (e) {
      AppLogger.error('Error getting current location: $e');
    }
  }

  Future<List<PlacesSearchResult>> _getNearbyVenues() async {
    if (_currentLocation == null) await _getCurrentLocation();
    if (_currentLocation == null) return [];

    try {
      final places = GoogleMapsPlaces(apiKey: Config.googleMapApiKey);
      final response = await places.searchNearbyWithRadius(
        Location(
          lat: _currentLocation!.latitude,
          lng: _currentLocation!.longitude,
        ),
        1500,
        type: 'establishment',
        keyword: 'bar OR club OR nightclub',
      );

      return response.results
          .where((result) => response.status == 'OK')
          .toList();
    } catch (e) {
      AppLogger.error('Error fetching nearby venues: $e');
      return [];
    }
  }

  String? getNeighborhood(List<dynamic> addressComponents) {
    for (final component in addressComponents) {
      final cmp = component as AddressComponent;
      if (cmp.types.contains('neighborhood')) {
        return cmp.longName;
      }
    }
    return null; // Return null if no neighborhood is found
  }

  Future<void> placeAutoComplete(String query) async {
    // Early return if already selecting location or if not mounted
    if (_isSelectingLocation || !mounted) return;

    // Store the current query to compare later
    final currentQuery = query;

    try {
      final places = GoogleMapsPlaces(apiKey: Config.googleMapApiKey);

      // Capture response but don't update state yet
      final response = await places.autocomplete(
        query,
        types: ['establishment'],
        components: [Component('country', 'us')],
      );

      // Check if widget is still mounted and query is still relevant
      if (!mounted) return;
      if (currentQuery != _searchController.text) {
        return;
      }

      // Only now update state safely
      if (response.status == 'OK') {
        setState(() {
          placePredictions = response.predictions
              .map(
                (p) => AutoCompletePrediction(
                  placeID: p.placeId,
                  description: p.description,
                  structuredFormatting: StructuredFormatting(
                    mainText: p.structuredFormatting?.mainText ?? '',
                    secondaryText: p.structuredFormatting?.secondaryText,
                  ),
                ),
              )
              .toList();
          _displayList = placePredictions.map(_PredictionItem.new).toList();
        });
      }
    } catch (e) {
      if (mounted) {
        // Only log if still mounted
        AppLogger.error('Error in place autocomplete: $e');
      }
    }
  }

  String? getVicinity(PlaceDetails place) {
    // Try to extract vicinity information as a fallback for neighborhood
    if (place.vicinity != null && place.vicinity!.isNotEmpty) {
      // Extract the first part of the vicinity which is usually the neighborhood or area
      final parts = place.vicinity!.split(',');
      if (parts.isNotEmpty) {
        return parts.first.trim();
      }
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
        child: Container(
          decoration: BoxDecoration(
            color: darkAppColors.secondaryBackgroundColor.withAlpha(178),
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: Column(
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withAlpha(128),
                    borderRadius: const BorderRadius.all(Radius.circular(2)),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Search for Venue',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    IconButton(
                      onPressed: () => RouteUtils.pop(context),
                      icon: const Icon(CupertinoIcons.xmark_circle_fill),
                      color: Colors.white60,
                    ),
                  ],
                ),
                TextField(
                  controller: _searchController,
                  style: const TextStyle(color: Colors.white),
                  onChanged: (value) {
                    _debounceTimer?.cancel();

                    if (!mounted) return;

                    if (value.isEmpty) {
                      // Immediately update UI for empty query
                      setState(() {
                        placePredictions = [];
                        _displayList =
                            _searchResults.map(_ResultItem.new).toList();
                      });
                    } else {
                      // Debounce for non-empty queries
                      _debounceTimer =
                          Timer(const Duration(milliseconds: 300), () {
                        if (mounted) {
                          // Check mounted before starting async work
                          placeAutoComplete(value);
                        }
                      });
                    }
                  },
                  decoration: const InputDecoration(
                    fillColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(vertical: 15, horizontal: 24),
                    hintText: 'Search venue',
                    prefixIcon: Icon(CupertinoIcons.search),
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.white),
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.white24),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: _displayList.length,
                    itemBuilder: (context, index) {
                      final item = _displayList[index];
                      String title;
                      String? subtitle;

                      if (item is _PredictionItem) {
                        title =
                            item.prediction.structuredFormatting?.mainText ??
                                '';
                        subtitle = item.prediction.description;
                      } else if (item is _ResultItem) {
                        title = item.result.name;
                        subtitle = item.result.formattedAddress;
                      } else {
                        title = '';
                        subtitle = '';
                      }

                      return Column(
                        children: [
                          ListTile(
                            onTap: () async {
                              // Prevent multiple taps while processing
                              if (_isSelectingLocation) return;

                              setState(() => _isSelectingLocation = true);
                              final String? placeId;

                              try {
                                // Get placeId based on item type
                                if (item is _PredictionItem) {
                                  placeId = item.prediction.placeID;
                                } else if (item is _ResultItem) {
                                  placeId = item.result.placeId;
                                } else {
                                  placeId = null;
                                }

                                // Exit early if no placeId available
                                if (placeId == null || placeId.isEmpty) {
                                  AppLogger.error(
                                    'No place ID available for selection',
                                  );
                                  return;
                                }

                                // Fetch consistent place details using the placeId
                                final places = GoogleMapsPlaces(
                                  apiKey: Config.googleMapApiKey,
                                );
                                final details =
                                    await places.getDetailsByPlaceId(placeId);

                                if (details.status == 'OK') {
                                  // Get neighborhood with fallback
                                  final neighborhood = getNeighborhood(
                                        details.result.addressComponents,
                                      ) ??
                                      getVicinity(details.result);

                                  // Use values directly from the details result
                                  final selectedName = details.result.name;
                                  final selectedAddress =
                                      details.result.formattedAddress ?? '';
                                  final selectedLat =
                                      details.result.geometry?.location.lat;
                                  final selectedLng =
                                      details.result.geometry?.location.lng;

                                  // Cache selected location to prevent immediate changes
                                  setState(() {
                                    selectedPreciseLoc = selectedName;
                                  });

                                  // Invoke callback with consistent data
                                  widget.onLocationSelected(
                                    selectedName,
                                    selectedAddress,
                                    selectedLat,
                                    selectedLng,
                                    placeId,
                                    neighborhood,
                                  );

                                  // Close the bottom sheet after successful selection
                                  if (context.mounted) RouteUtils.pop(context);
                                } else {
                                  AppLogger.error(
                                    'Failed to get place details: ${details.status}',
                                  );
                                }
                              } catch (e) {
                                AppLogger.error('Error selecting location: $e');
                              } finally {
                                // Always reset selection state
                                if (mounted) {
                                  setState(() => _isSelectingLocation = false);
                                }
                              }
                            },
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(26),
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(8)),
                              ),
                              child: const Icon(
                                CupertinoIcons.location_circle_fill,
                                color: Colors.white70,
                              ),
                            ),
                            title: Text(
                              title,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            subtitle: Text(
                              subtitle ?? '',
                              style: TextStyle(
                                color: Colors.white.withAlpha(178),
                                fontSize: 13,
                              ),
                            ),
                            trailing: selectedPreciseLoc == title
                                ? const Icon(
                                    CupertinoIcons.checkmark_circle_fill,
                                  )
                                : null,
                          ),
                          Divider(color: Colors.white.withAlpha(26), height: 1),
                        ],
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
