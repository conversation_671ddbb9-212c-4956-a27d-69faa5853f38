import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/models/feed/feed_model.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/feed/build_feed_item.dart';
import 'package:vibeo/widgets/global/no_content_page.dart';

class VenueDescVibes extends StatefulWidget {
  final String venueID;
  const VenueDescVibes({required this.venueID, super.key});

  @override
  State<VenueDescVibes> createState() => _VenueDescVibesState();
}

class _VenueDescVibesState extends State<VenueDescVibes> {
  @override
  void initState() {
    super.initState();
    final venueState = context.read<VenueBloc>().state;

    if (venueState.venues
            .firstWhere((venue) => venue.id == widget.venueID)
            .feeds ==
        null) {
      context.read<VenueBloc>().add(FetchVenueFeedsEvent(widget.venueID));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VenueBloc, VenueState>(
      builder: (context, state) {
        if (!state.venues.isNotEmpty) {
          return _buildShimmerGrid();
        }

        final feeds = state.venues
            .firstWhere((venue) => venue.id == widget.venueID)
            .feeds;

        // Show shimmer while loading feeds
        if (feeds == null) {
          return _buildShimmerGrid();
        }

        // Show empty state when feeds are loaded but empty
        if (feeds.isEmpty) {
          return _buildEmptyState();
        }

        return _buildFeedsGrid(feeds);
      },
    );
  }

  Widget _buildShimmerGrid() {
    return MasonryGridView.builder(
      gridDelegate: const SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
      ),
      padding: SizeUtils.horizontalPadding.copyWith(
        top: 20,
        bottom: 60,
      ),
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: 6,
      itemBuilder: _buildShimmerItem,
    );
  }

  Widget _buildEmptyState() {
    return const NoContentPage(
      icon: CupertinoIcons.photo_on_rectangle,
      title: 'No Posts Yet',
      description: 'No posts here right now. Check back soon for more vibes!',
    );
  }

  Widget _buildFeedsGrid(List<FeedModel> feeds) {
    return MasonryGridView.builder(
      gridDelegate: const SliverSimpleGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
      ),
      padding: SizeUtils.horizontalPadding.copyWith(
        top: 20,
        bottom: 60,
      ),
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: feeds.length,
      itemBuilder: (context, index) => buildFeedItem(
        context,
        index,
        feeds,
        isVenuePage: true,
      ),
    );
  }

  Widget _buildShimmerItem(BuildContext context, int index) {
    final List<double> heights = [240, 270, 300, 330, 360];
    final height = heights[index % heights.length];

    return ClipRRect(
      borderRadius: const BorderRadius.all(Radius.circular(12)),
      child: Shimmer.fromColors(
        baseColor: Colors.white12,
        highlightColor: Colors.white30,
        child: Container(
          height: height,
          decoration: const BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
      ),
    );
  }
}
