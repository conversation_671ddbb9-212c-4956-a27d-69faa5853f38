import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/cubits/filter/filter_cubit.dart';

import 'package:vibeo/data/filter_venues_data.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class FilterVenueSection extends StatelessWidget {
  const FilterVenueSection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          decoration: BoxDecoration(
            color: darkAppColors.secondaryBackgroundColor.withAlpha(153),
          ),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: filters.length,
            itemBuilder: (context, index) {
              final filterTitle = filters[index]['title'] as String;

              return BlocBuilder<FilterCubit, FilterState>(
                builder: (context, state) {
                  final isSelected = state.selectedFilter == filterTitle;

                  return HapticButton(
                    onTap: () async {
                      final analytics = AnalyticsService.instance;
                      await analytics.logFilterInteraction(
                        interactionType: filterTitle,
                      );
                      if (context.mounted) {
                        context.read<FilterCubit>().toggleFilter(filterTitle);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(
                        top: 12,
                      ),
                      width: 85,
                      child: Column(
                        children: [
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: isSelected
                                  ? darkAppColors.lightColor.withAlpha(80)
                                  : null,
                            ),
                            child: Image.asset(
                              filters[index]['icon'] as String,
                              width: 50,
                              height: 50,
                            ),
                          ),
                          Text(
                            filterTitle,
                            style: TextStyle(
                              color: isSelected
                                  ? Colors.white
                                  : darkAppColors.lightColor.withAlpha(200),
                              fontSize: 12,
                              fontWeight: isSelected
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
