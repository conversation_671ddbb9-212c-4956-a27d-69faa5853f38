import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/logic/cubit/perks_cubit_cubit.dart';

import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';

import 'package:vibeo/widgets/dialogs/info_dialog_box.dart';
import 'package:vibeo/widgets/offer/offer_tile.dart';

class OffersSection extends StatefulWidget {
  final String venueID;
  final void Function(Widget productWidget, Offset position)? onAddToCart;

  const OffersSection({
    required this.venueID,
    this.onAddToCart,
    super.key,
  });

  @override
  State<OffersSection> createState() => _OffersSectionState();
}

class _OffersSectionState extends State<OffersSection> {
  // Lists to hold categorized offers
  final List<OfferModel> _drops = [];
  final List<OfferModel> _timeBasedOffers = [];
  final List<OfferModel> _redeemingOffers = [];
  final List<OfferModel> _expiringOffers = [];
  final List<OfferModel> _regularOffers = [];
  final List<OfferModel> _moreOffers = [];

  void _categorizeOffers(List<OfferModel> offers) {
    // Clear previous categorizations
    _drops.clear();
    _timeBasedOffers.clear();
    _redeemingOffers.clear();
    _regularOffers.clear();
    _moreOffers.clear();

    for (final offer in offers) {
      AppLogger.debug(
        'Offers: ${offer.startTime} ${offer.endTime} ${offer.weekdays}',
      );
      // Skip redeemed offers
      if (offer.isLocked) {
        _moreOffers.add(offer);
        continue;
      } else if (offer.voucherType == VoucherType.REGULAR) {
        _regularOffers.add(offer);
        continue;
      } else {
        if (offer.voucherCategory == VoucherCategory.DROPS) {
          _drops.add(offer);
          continue;
        } else if (offer.voucherCategory == VoucherCategory.ONGOING) {
          _redeemingOffers.add(offer);
          continue;
        } else if (offer.voucherCategory == VoucherCategory.TIMEBASED) {
          _timeBasedOffers.add(offer);
          continue;
        } else if (offer.voucherCategory == VoucherCategory.LIMITED) {
          _expiringOffers.add(offer);
          continue;
        }
      }
    }

    // Sort offers by time constraints, then by proximity to current time, then by priority
    _sortOffersByTimeConstraints(_drops);
    _sortOffersByTimeConstraints(_timeBasedOffers);
    _sortOffersByTimeConstraints(_redeemingOffers);
    _sortOffersByTimeConstraints(_regularOffers);
    _sortOffersByTimeConstraints(_moreOffers);
  }

  @override
  Widget build(BuildContext context) {
    final String placeIdentifier = widget.venueID;

    return BlocBuilder<PerksCubit, PerksState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: LoadingWidget());
        }

        if (!state.perks.containsKey(placeIdentifier)) {
          return const SizedBox();
        }

        final List<OfferModel> offers =
            state.perks[placeIdentifier]!['offers'] as List<OfferModel>;
        final newOffers = offers;
        if (newOffers.isEmpty) {
          return const SizedBox();
        }

        // Categorize offers
        _categorizeOffers(newOffers);

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Offers', style: AppTextStyles.title),
                    GestureDetector(
                      onTap: () {
                        InfoDialog.showInfo(
                          context,
                          title: 'Info',
                          description:
                              'Offers may vary. Please confirm with the venue before visiting.',
                        );
                      },
                      child: const Icon(
                        CupertinoIcons.info_circle,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),

              // Drops section
              if (_drops.isNotEmpty)
                _buildSection(
                  'Drops',
                  _drops,
                  Icons.flash_on,
                  [Colors.purple.shade400, Colors.deepPurple.shade600],
                ),

              // Time-based Offers section
              if (_timeBasedOffers.isNotEmpty)
                _buildSection(
                  'Limited-Time Perks',
                  _timeBasedOffers,
                  CupertinoIcons.time,
                  [Colors.orange.shade400, Colors.deepOrange.shade600],
                ),

              // Redeeming Offers section
              if (_redeemingOffers.isNotEmpty)
                _buildSection(
                  'Ongoing Deals',
                  _redeemingOffers,
                  CupertinoIcons.gift,
                  [Colors.green.shade400, Colors.teal.shade600],
                ),

              // Regular Offers section
              if (_regularOffers.isNotEmpty)
                _buildSection(
                  'Spotted Deals',
                  _regularOffers,
                  CupertinoIcons.tag,
                  [Colors.blue.shade400, Colors.indigo.shade600],
                ),

              // Locked Offers section
              if (_moreOffers.isNotEmpty)
                _buildSection(
                  'Unlockable Offers',
                  _moreOffers,
                  CupertinoIcons.lock,
                  [Colors.pink.shade400, Colors.red.shade600],
                ),
            ],
          ),
        );
      },
    );
  }

  // Helper method to sort offers by time constraints and proximity to current time
  void _sortOffersByTimeConstraints(List<OfferModel> offers) {
    // Sort by priority first, then by time constraints
    offers
      ..sort((a, b) {
        // Check if offers have time constraints
        final aHasTimeConstraints =
            (a.startTime.isNotEmpty && a.endTime.isNotEmpty) ||
                a.weekdays.isNotEmpty;
        final bHasTimeConstraints =
            (b.startTime.isNotEmpty && b.endTime.isNotEmpty) ||
                b.weekdays.isNotEmpty;

        // If both have or both don't have time constraints, they're equal in this sort
        if (aHasTimeConstraints == bHasTimeConstraints) {
          // If both have time constraints, sort by proximity to current time
          if (aHasTimeConstraints) {
            return _getTimeProximityScore(a)
                .compareTo(_getTimeProximityScore(b));
          }
          return 0; // Both don't have time constraints, keep original order
        }

        // Offers with time constraints come first (return -1)
        return aHasTimeConstraints ? -1 : 1;
      })
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  // Calculate a score representing how close an offer is to the current time
  // Lower score means closer to current time
  int _getTimeProximityScore(OfferModel offer) {
    // If no time constraints, return a high score
    if ((offer.startTime.isEmpty || offer.endTime.isEmpty) &&
        offer.weekdays.isEmpty) {
      return 1000000;
    }

    final now = DateTime.now();
    int score = 0;

    // Check weekday proximity
    if (offer.weekdays.isNotEmpty) {
      final currentWeekday = _getCurrentWeekday(now);
      if (offer.weekdays.contains(currentWeekday)) {
        // Current weekday matches, lowest score
        score += 0;
      } else {
        // Calculate days until next valid weekday
        final weekdayOrder = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday',
        ];
        final currentWeekdayIndex = weekdayOrder.indexOf(currentWeekday);

        int minDaysAway = 7; // Maximum days away
        for (final weekday in offer.weekdays) {
          final offerWeekdayIndex = weekdayOrder.indexOf(weekday);
          final int daysAway = (offerWeekdayIndex - currentWeekdayIndex) % 7;
          if (daysAway < minDaysAway) {
            minDaysAway = daysAway;
          }
        }
        score += minDaysAway * 1440; // Convert days to minutes (24*60)
      }
    }

    // Check time proximity if we have time constraints
    if (offer.startTime.isNotEmpty && offer.endTime.isNotEmpty) {
      // Parse start and end times
      final startTimeParts = offer.startTime.split(':');
      final endTimeParts = offer.endTime.split(':');

      if (startTimeParts.length >= 2 && endTimeParts.length >= 2) {
        // Extract hours and minutes, handling AM/PM format
        int startHour = int.parse(startTimeParts[0]);
        final int startMinute = int.parse(startTimeParts[1].split(' ')[0]);
        final startPeriod = offer.startTime.contains('PM') ? 'PM' : 'AM';

        // Convert to 24-hour format
        if (startPeriod == 'PM' && startHour < 12) {
          startHour += 12;
        }
        if (startPeriod == 'AM' && startHour == 12) {
          startHour = 0;
        }

        // Calculate minutes from midnight
        final startTimeMinutes = startHour * 60 + startMinute;
        final currentTimeMinutes = now.hour * 60 + now.minute;

        // Calculate minutes until start time
        int minutesUntilStart = (startTimeMinutes - currentTimeMinutes) % 1440;
        if (minutesUntilStart < 0) {
          minutesUntilStart += 1440; // Add a day if negative
        }

        // Add to score
        score += minutesUntilStart;
      }
    }

    return score;
  }

  // Helper to get current weekday as string
  String _getCurrentWeekday(DateTime date) {
    switch (date.weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  Widget _buildSection(
    String title,
    List<OfferModel> offers,
    IconData icon,
    List<Color> gradientColors,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header with glassmorphic design and animation
        Container(
          margin: const EdgeInsets.only(bottom: 16, top: 8),

          // decoration: BoxDecoration(
          //   borderRadius: BorderRadius.circular(16),
          //   border: Border.all(
          //     color: gradientColors[0].withAlpha(76),
          //     width: 1,
          //   ),
          //   boxShadow: [
          //     BoxShadow(
          //       color: gradientColors[0].withAlpha(30),
          //       blurRadius: 8,
          //       offset: const Offset(0, 2),
          //     ),
          //   ],
          // ),
          child: Row(
            children: [
              // Icon with gradient background
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: gradientColors,
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: gradientColors[1].withAlpha(40),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: AppTextStyles.body.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              // Count badge
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: gradientColors[1].withAlpha(51),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: gradientColors[1].withAlpha(102),
                    width: 1,
                  ),
                ),
                child: Text(
                  '${offers.length}',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Offers list
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 4, bottom: 10),
          itemCount: offers.length,
          itemBuilder: (ctx, index) => OfferTile(
            offer: offers[index],
            canNavigate: false,
            canRedeem: false,
            onAddToCart: widget.onAddToCart,
            canAddToCart: !offers[index].isLocked,
          ),
        ),

        const SizedBox(
          height: 24,
        ),
      ],
    );
  }
}
