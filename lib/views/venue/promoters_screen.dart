import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/components/loading_widget.dart';
import 'package:vibeo/logic/cubit/perks_cubit_cubit.dart';

import 'package:vibeo/models/promoters/promoter_model.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/venue/promoter_tile.dart';

class PromoterSection extends StatelessWidget {
  final String venueID;

  const PromoterSection({
    required this.venueID,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final String placeIdentifier = venueID;

    return BlocBuilder<PerksCubit, PerksState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: LoadingWidget());
        }

        if (state.error != null) {
          return const SizedBox();
        }

        if (!state.perks.contains<PERSON><PERSON>(placeIdentifier)) {
          return const SizedBox();
        }

        final promoters =
            state.perks[placeIdentifier]!['promoters'] as List<PromoterModel>;

        if (promoters.isEmpty) {
          return const SizedBox();
        }

        return Padding(
          padding: const EdgeInsets.only(
            top: 20,
            bottom: 0,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: SizeUtils.horizontalPadding,
                child: Text(
                  'Reserve',
                  style: AppTextStyles.subtitle,
                ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                height: 180,
                child: PageView.builder(
                  itemCount: promoters.length,
                  pageSnapping: true,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (ctx, index) {
                    final promoter = promoters[index];
                    return Padding(
                      padding: SizeUtils.horizontalPadding,
                      child: PromoterTile(
                        promoter: promoter,
                        venueID: venueID,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
