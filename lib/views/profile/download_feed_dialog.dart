import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:ffmpeg_kit_flutter/ffmpeg_kit.dart';
// import 'package:ffmpeg_kit_flutter/return_code.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/utils/log/app_logger.dart';

class DownloadProgressDialog extends StatefulWidget {
  final String fileName;
  final String networkURL;
  final String watermarkText;

  const DownloadProgressDialog({
    required this.fileName,
    required this.networkURL,
    required this.watermarkText,
    super.key,
  });

  @override
  State<DownloadProgressDialog> createState() => _DownloadProgressDialogState();
}

class _DownloadProgressDialogState extends State<DownloadProgressDialog> {
  double _progress = 0;
  String _status = 'Downloading...';

  @override
  void initState() {
    super.initState();
    _startDownload();
  }

  Future<void> _startDownload() async {
    try {
      final appDocDir = await getTemporaryDirectory();
      final String downloadPath =
          '${appDocDir.path}/${widget.fileName}_original.mp4';
      final String outputPath =
          '${appDocDir.path}/${widget.fileName}_watermarked.mp4';

      await Dio().download(
        widget.networkURL,
        downloadPath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            setState(() => _progress = received / total);
          }
        },
      );

      setState(() {
        _status = 'Adding watermark...';
        _progress = 0;
      });

      await _addWatermark(downloadPath, outputPath);
      final result = await ImageGallerySaver.saveFile(outputPath);
      AppLogger.debug(result.toString());

      if (mounted) {
        RouteUtils.pop(context, true);
      }
    } catch (e) {
      AppLogger.error('Error: $e');
      if (mounted) {
        RouteUtils.pop(context, false);
      }
    }
  }

  Future<void> _addWatermark(String inputPath, String outputPath) async {
    final byteData = await rootBundle.load('assets/images/watermark.png');
    final tempDir = await getTemporaryDirectory();
    final watermarkPath = '${tempDir.path}/watermark.png';
    await File(watermarkPath).writeAsBytes(byteData.buffer.asUint8List());

    // final String ffmpegCommand =
    //     '-i $inputPath -i $watermarkPath -filter_complex "[1:v]scale=w=iw*0.5:h=-1[wm];[0:v][wm]overlay=x=W-w-20:y=H-h-20[outv]" -map "[outv]" -map 0:a -c:a copy $outputPath';

    // final session = await FFmpegKit.execute(ffmpegCommand);
    // final returnCode = await session.getReturnCode();

    // if (!ReturnCode.isSuccess(returnCode)) {
    //   throw BusinessLogicException(message: 'Failed to add watermark');
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 32),
        constraints: const BoxConstraints(
          maxWidth: 300,
          maxHeight: 200,
        ),
        child: CupertinoPopupSurface(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(14)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _status,
                  style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                const CupertinoActivityIndicator(),
                const SizedBox(height: 12),
                ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  child: SizedBox(
                    height: 4,
                    width: double.infinity,
                    child: CupertinoProgressBar(value: _progress),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${(_progress * 100).toStringAsFixed(0)}%',
                  style: const TextStyle(
                    fontSize: 13,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class CupertinoProgressBar extends StatelessWidget {
  final double value;

  const CupertinoProgressBar({required this.value, super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: CupertinoColors.systemGrey5,
      ),
      child: FractionallySizedBox(
        widthFactor: value,
        child: Container(
          decoration: const BoxDecoration(
            color: CupertinoColors.activeBlue,
          ),
        ),
      ),
    );
  }
}
