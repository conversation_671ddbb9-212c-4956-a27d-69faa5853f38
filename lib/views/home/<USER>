import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TabPagesBar extends StatelessWidget implements PreferredSizeWidget {
  const TabPagesBar({
    required this.controller,
    super.key,
  });

  final TabController controller;

  @override
  Size get preferredSize => const Size.fromHeight(kTextTabBarHeight);

  @override
  Widget build(BuildContext context) {
    return TabBar(
      controller: controller,
      indicatorColor: Colors.white,
      dividerHeight: 0.05,
      dividerColor: Colors.grey.withAlpha(140),
      labelColor: Colors.white,
      isScrollable: true,
      tabAlignment: TabAlignment.start,
      unselectedLabelColor: Colors.grey,
      padding: const EdgeInsets.only(left: 4),
      enableFeedback: true,
      indicatorWeight: 2,
      splashFactory: NoSplash.splashFactory,
      overlayColor: WidgetStateColor.transparent,
      physics: const ClampingScrollPhysics(),
      onTap: (index) {
        HapticFeedback.lightImpact();
      },
      tabs: const [
        Tab(text: 'For You'),
        Tab(text: 'Places'),
        Tab(text: 'Plan'),
      ],
    );
  }
}
