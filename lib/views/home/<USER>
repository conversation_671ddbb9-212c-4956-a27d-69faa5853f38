import 'package:flutter/material.dart';
import 'package:vibeo/screens/screen.dart';

class ExplorePages extends StatelessWidget {
  final TabController controller;
  const ExplorePages({required this.controller, super.key});

  void setPageIndex(int index) {
    controller.animateTo(1);
  }

  @override
  Widget build(BuildContext context) {
    return TabBarView(
      physics: const NeverScrollableScrollPhysics(),
      controller: controller,
      children: [
        ForYouPage(
          setPageIndex: () => setPageIndex(1),
        ),
        const VenuesPage(),
        const JourneyPlannerPage(),
      ],
    );
  }
}
