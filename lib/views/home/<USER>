import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

import 'package:vibeo/logic/feed/storage/feed_upload_rate_logic.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/screens/home/<USER>';
import 'package:vibeo/themes/constant_theme.dart';

import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/location/location_permission.dart';
import 'package:vibeo/utils/log/app_logger.dart';

import 'package:vibeo/views/home/<USER>';

import 'package:vibeo/widgets/dialogs/dialog_box.dart';

import 'package:video_compress/video_compress.dart';

class ExploreAppBar extends StatefulWidget {
  final TabController controller;
  const ExploreAppBar({required this.controller, super.key});

  @override
  State<ExploreAppBar> createState() => _ExploreAppBarState();
}

class _ExploreAppBarState extends State<ExploreAppBar> {
  Future<void> pickVideo() async {
    try {
      await showCupertinoModalPopup(
        context: context,
        builder: (BuildContext context) => CupertinoActionSheet(
          title: const Text('Select Video'),
          message:
              const Text('Choose a video from gallery or record a new one'),
          actions: <CupertinoActionSheetAction>[
            CupertinoActionSheetAction(
              onPressed: () async {
                RouteUtils.pop(context);
                final ImagePicker picker = ImagePicker();
                final XFile? video = await picker.pickVideo(
                  source: ImageSource.gallery,
                  maxDuration: const Duration(seconds: 20),
                );

                if (video != null) {
                  await handleVideoSelection(video);
                }
              },
              child: Text(
                'Choose from Gallery',
                style: AppTextStyles.heading4,
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () async {
                RouteUtils.pop(context);
                final ImagePicker picker = ImagePicker();
                final XFile? video = await picker.pickVideo(
                  source: ImageSource.camera,
                  maxDuration: const Duration(seconds: 20),
                  preferredCameraDevice: CameraDevice.rear,
                );

                if (video != null) {
                  await handleVideoSelection(video);
                }
              },
              child: Text(
                'Record Video',
                style: AppTextStyles.heading4,
              ),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              RouteUtils.pop(context);
            },
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontSize: 16,
              ),
            ),
          ),
        ),
      );
    } catch (e) {
      // Handle any errors
      AppLogger.error('Error picking video: $e');
    }
  }

  Future<void> handleVideoSelection(XFile video) async {
    if (!mounted) return;

    // Show loading dialog
    unawaited(
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return GlassmorphicDialog(
            title: 'Video Processing',
            description: 'Please wait while we process your video...',
            buttonText: 'Processing...',
            canPop: false,
            onPressed: () {},
          );
        },
      ),
    );

    try {
      final MediaInfo? mediaInfo = await VideoCompress.compressVideo(
        video.path,
        quality: VideoQuality.Res1280x720Quality,
        deleteOrigin: false,
      );

      if (mounted) {
        // Dismiss the dialog
        Navigator.of(context).pop();

        // Navigate to next page
        await RouteUtils.pushNamed(
          context,
          RoutePaths.addContentPage,
          arguments: mediaInfo,
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Dismiss dialog on error
        // Handle error appropriately
      }
    }
  }

  void _showSearchSheet(BuildContext context) {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.transparent,
      transitionDuration: const Duration(milliseconds: 200),
      pageBuilder: (context, animation1, animation2) {
        return Container(); // Transparent container to handle touches
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return SearchPage(
          animation: animation,
        );
      },
    );
  }

  Future<void> openAddOption() async {
    final FeedRateLimiter limiter = FeedRateLimiter();
    final Map<String, dynamic> limitInfo = await limiter.getRateLimitInfo(
      context.read<UserBloc>().state.user!,
    );
    if (limitInfo['canUpload'] as bool? ?? true) {
      await handleLocationPermission();
      await pickVideo();
    } else {
      if (mounted) {
        await GlassmorphicDialog.show(
          context,
          title: 'Daily Limit Reached',
          description:
              'Please try again after ${limitInfo['timeUntilNextUpload']}',
          buttonText: "I'll Wait",
          onPressed: () {
            RouteUtils.pop(context);
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      elevation: 0,
      floating: true,
      pinned: true,
      snap: true,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      forceMaterialTransparency: true,
      automaticallyImplyLeading: false,
      expandedHeight: 80,
      toolbarHeight: 40,
      title: const Text(
        'Vibeo',
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      centerTitle: false,
      shape: OutlineInputBorder(
        borderSide: BorderSide(
          width: 0.1,
          color: Colors.white.withAlpha(100),
        ),
      ),
      flexibleSpace: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 25, sigmaY: 25),
          child: FlexibleSpaceBar(
            background: ColoredBox(
              color: darkAppColors.secondaryBackgroundColor.withAlpha(100),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () {
            _showSearchSheet(context);
          },
          icon: const Icon(
            CupertinoIcons.search,
            color: Colors.white,
          ),
        ),
        if (context.read<UserBloc>().state.user!.contentCreator)
          IconButton(
            onPressed: openAddOption,
            icon: const Icon(
              CupertinoIcons.add,
              color: Colors.white,
            ),
          ),
      ],
      bottom: TabPagesBar(
        controller: widget.controller,
      ),
    );
  }
}
