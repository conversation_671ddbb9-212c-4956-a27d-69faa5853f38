import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';

import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/home/<USER>';

import 'package:vibeo/widgets/video_tiles/vibe_tags.dart';

class VibeTypeTiles extends StatefulWidget {
  const VibeTypeTiles({super.key});

  @override
  State<VibeTypeTiles> createState() => _VibeTypeTilesState();
}

class _VibeTypeTilesState extends State<VibeTypeTiles> {
  int selectedCategoryIndex = 0;
  late final ScrollController _tileScrollController;
  bool _isLoadingMore = false;
  bool _isLoading = false;
  DateTime _lastLoadTime = DateTime.now();

  final List<Map<String, String>> _defaultSubCategories = const [
    {'title': 'Lowkey Vibes 🤫', 'id': 'vt1a3f'},
    {'title': 'Link Up Spot 🔗', 'id': 'vt2b7k'},
    {'title': 'Party Mode 🔥', 'id': 'vt3c9p'},
  ];

  // Scene categories
  late List<Map<String, String>> subCategories = _defaultSubCategories;

  @override
  void initState() {
    super.initState();
    _tileScrollController = ScrollController();

    _tileScrollController.addListener(_scrollListener);

    Future.microtask(() {
      fetchVibeTypes().then((_) {
        _initializeFeeds();
      });
    });
  }

  void _initializeFeeds() {
    context.read<FeedBloc>().add(
          FetchVibeTypeFeedsEvent(
            subCategories[selectedCategoryIndex]['id']!,
            limit: 5,
          ),
        );
  }

  Future<void> fetchVibeTypes() async {
    final feedBloc = context.read<FeedBloc>();

    // Use cached scenes if available
    if (feedBloc.getVibeTypes.isNotEmpty) {
      if (mounted) {
        setState(() {
          subCategories = feedBloc.getVibeTypes;
        });
      }
      return;
    }

    // Otherwise fetch scenes
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
          // Use default categories while loading
          subCategories = _defaultSubCategories;
        });
      }

      final FeedRepository feedRepository = FeedRepository();
      final fetchedVibeTypeFeeds =
          await feedRepository.fetchVibeTypesForFeeds();

      // Only update state if widget is still mounted
      if (fetchedVibeTypeFeeds.isNotEmpty && mounted) {
        setState(() {
          subCategories = fetchedVibeTypeFeeds;
        });
      }
    } catch (e) {
      AppLogger.error('Error fetching scenes: $e');
    } finally {
      // Always check mounted in finally block
      if (mounted) {
        setState(() {
          _isLoading = false;
          feedBloc.setVibeTypes = subCategories;
        });
      }
    }
  }

  void _scrollListener() {
    if (!mounted) return;

    if (_isLoadingMore ||
        DateTime.now().difference(_lastLoadTime).inSeconds < 2) {
      return;
    }

    final state = context.read<FeedBloc>().state;
    final currentVibeTypeFeeds =
        state.vibeTypeFeeds[subCategories[selectedCategoryIndex]['id']!] ?? [];

    if (currentVibeTypeFeeds.isNotEmpty &&
        currentVibeTypeFeeds.last.isEndMarker) {
      return;
    }

    if (_tileScrollController.position.pixels >=
        _tileScrollController.position.maxScrollExtent - 120) {
      _loadMoreFeeds();
      return;
    }
  }

  Future<void> _loadMoreFeeds() async {
    if (!mounted) return;

    final state = context.read<FeedBloc>().state;
    if (state.vibeTypeLoading || _isLoadingMore) {
      return;
    }

    try {
      // Set loading state
      if (mounted) {
        setState(() => _isLoadingMore = true);
      }
      _lastLoadTime = DateTime.now();

      // Add event to load more
      context.read<FeedBloc>().add(
            FetchVibeTypeFeedsEvent(
              subCategories[selectedCategoryIndex]['id']!,
              limit: 5,
              skip: state
                      .vibeTypeFeeds[subCategories[selectedCategoryIndex]
                          ['id']!]
                      ?.length ??
                  0,
            ),
          );

      // Add a small delay to prevent rapid requests
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      AppLogger.error('Error loading more feeds: $e');
    } finally {
      // Always check mounted in finally
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  void _onCategoryTap(int index) {
    if (!mounted) return;

    HapticFeedback.mediumImpact();

    setState(() {
      selectedCategoryIndex = index;
      // Reset scroll position
      _tileScrollController.jumpTo(0);
    });

    // Request new feeds for selected category
    context.read<FeedBloc>().add(
          FetchVibeTypeFeedsEvent(
            subCategories[index]['id']!,
            limit: 5,
          ),
        );
  }

  @override
  void dispose() {
    _isLoadingMore = false;
    _isLoading = false;

    // Reset the selected category index to 0
    selectedCategoryIndex = 0;

    // Reset scroll position
    if (_tileScrollController.hasClients) {
      _tileScrollController.jumpTo(0);
    }

    _tileScrollController
      ..removeListener(_scrollListener)
      ..dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Column(
        key: const PageStorageKey('by_vibeType_tiles'),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.vibeTypeTitle != current.vibeTypeTitle,
            builder: (context, state) {
              return TitleWidget(
                isLoading: state.vibeTypeLoading,
                title: state.vibeTypeTitle,
              );
            },
          ),
          Container(
            height: 40,
            margin: const EdgeInsets.only(bottom: 24),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: subCategories.length,
              padding: SizeUtils.horizontalPadding,
              clipBehavior: Clip.none,
              itemBuilder: (ctx, index) => _isLoading
                  ? buildShimmerTag()
                  : buildVibeTag(
                      isSelected: selectedCategoryIndex == index,
                      title: subCategories[index]['title']!,
                      onTap: () async {
                        try {
                          final AnalyticsService analytics =
                              AnalyticsService.instance;
                          await analytics.logTonightHomeTab(
                            tonightType: subCategories[index]['title']!,
                          );
                        } catch (e) {
                          AppLogger.error('Error logging tonight tab: $e');
                        } finally {
                          if (context.mounted) {
                            _onCategoryTap(index);
                          }
                        }
                      },
                    ),
            ),
          ),
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.vibeTypeFeeds[subCategories[selectedCategoryIndex]
                    ['id']!] !=
                current
                    .vibeTypeFeeds[subCategories[selectedCategoryIndex]['id']!],
            builder: (context, state) {
              // Handle error or empty state
              if (state is FeedError &&
                  (state
                          .vibeTypeFeeds[subCategories[selectedCategoryIndex]
                              ['id']!]
                          ?.isEmpty ??
                      true)) {
                return const SizedBox();
              }

              // Build tiles view
              return TilesView(
                scrollController: _tileScrollController,
                state: state,
                stateLoading: state.vibeTypeLoading,
                title: state.vibeTypeTitle,
                vibes: state.vibeTypeFeeds[subCategories[selectedCategoryIndex]
                        ['id']!] ??
                    [],
                scene: subCategories[selectedCategoryIndex]['title'],
              );
            },
          ),
        ],
      ),
    );
  }
}
