import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';

import 'package:vibeo/widgets/home/<USER>';

import 'package:vibeo/widgets/home/<USER>';

class LiveTiles extends StatefulWidget {
  const LiveTiles({super.key});

  @override
  State<LiveTiles> createState() => _LiveTilesState();
}

class _LiveTilesState extends State<LiveTiles> {
  late final ScrollController _scrollController;
  bool _isLoadingMore = false;
  DateTime _lastLoadTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _initializeFeeds();
  }

  Future<void> _initializeFeeds() async {
    if (context.read<FeedBloc>().state.liveVibes == null) {
      context.read<FeedBloc>().add(
            const FetchLiveFeedsEvent(
              limit: 5,
            ),
          );
    }
  }

  void _scrollListener() {
    if (_isLoadingMore ||
        DateTime.now().difference(_lastLoadTime).inSeconds < 2) {
      return;
    }

    final state = context.read<FeedBloc>().state;
    if (state.liveVibes!.isNotEmpty && state.liveVibes!.last.isEndMarker) {
      return;
    }

    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 120) {
      _loadMoreFeeds();
    }
  }

  Future<void> _loadMoreFeeds() async {
    final state = context.read<FeedBloc>().state;
    if (!state.liveLoading && !_isLoadingMore) {
      setState(() => _isLoadingMore = true);
      _lastLoadTime = DateTime.now();

      context.read<FeedBloc>().add(
            FetchLiveFeedsEvent(
              limit: 5,
              skip: state.liveVibes!.length,
            ),
          );

      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FeedBloc, FeedState>(
      buildWhen: (previous, current) => previous.liveVibes != current.liveVibes,
      builder: (context, state) {
        if (state is FeedError && state.liveVibes == null) {
          return const SizedBox();
        }

        return (state is FeedsLoaded &&
                state.liveVibes != null &&
                state.liveVibes!.isNotEmpty)
            ? Padding(
                padding: const EdgeInsets.symmetric(vertical: 26),
                child: Column(
                  key: const PageStorageKey('live_tiles'),
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TitleWidget(
                      isLoading: state.liveLoading,
                      title: state.liveTitle,
                      isLive: state.liveVibes!.any(
                        (vibe) =>
                            vibe.feeds.any((feed) => feed.isLive ?? false),
                      ),
                    ),
                    TilesView(
                      scrollController: _scrollController,
                      state: state,
                      stateLoading: state.liveLoading,
                      vibes: state.liveVibes!,
                      title: 'Today',
                    ),
                  ],
                ),
              )
            : const SizedBox();
      },
    );
  }

  @override
  void dispose() {
    // Reset scroll position
    if (_scrollController.hasClients) {
      _scrollController.jumpTo(0);
    }

    _scrollController
      ..removeListener(_scrollListener)
      ..dispose();
    super.dispose();
  }
}
