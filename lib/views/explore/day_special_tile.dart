import 'dart:async';
import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/constants/video_tiles.dart';
import 'package:vibeo/cubits/daySpecialTile/dayspecial_cubit.dart';

import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/venue/special_venue_tile.dart';

class DaySpecialTile extends StatelessWidget {
  const DaySpecialTile({super.key});

  Future<void> logAnalytics(String venueID) async {
    final analytics = AnalyticsService.instance;
    await analytics.trackDaySpecialTileClicked(venueID);
  }

  String getWeekdayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DayspecialCubit, DayspecialState>(
      builder: (context, state) {
        if (state is DaySpecialInitial) {
          context.read<DayspecialCubit>().fetchSpecialVenues();
        }
        if (state is DayspecialError) {
          return const SizedBox();
        }
        return Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 26,
          ),
          child: Column(
            key: const PageStorageKey('day_special_tile'),
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TitleWidget(
                isLoading: state is DayspecialLoading,
                title: '${getWeekdayName(DateTime.now().weekday)} Specials',
              ),
              SizedBox(
                height: 200,
                child: _buildVenueList(state, context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildVenueList(DayspecialState state, BuildContext context) {
    if (state is DayspecialLoading || state is DaySpecialInitial) {
      return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 3,
        padding: SizeUtils.horizontalPadding,
        clipBehavior: Clip.none,
        itemBuilder: (ctx, index) => const HighlightVenueTileShimmer(),
      );
    } else if (state is DayspecialError) {
      return Center(child: Text(state.message));
    } else if (state is DayspecialLoaded) {
      return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: state.specialVenues.length,
        padding: SizeUtils.horizontalPadding,
        clipBehavior: Clip.none,
        itemBuilder: (ctx, index) => buildVenueItem(
          state.specialVenues[index],
          index,
          context,
          () async {
            try {
              final AnalyticsService analytics = AnalyticsService.instance;
              await analytics.logSpecialDaysTab(
                venueID: state.specialVenues[index].id,
              );
            } catch (e) {
              AppLogger.error('Error logging special days tab: $e');
            } finally {
              if (context.mounted) {
                await _handleVenueTap(state.specialVenues[index], context);
              }
            }
          },
        ),
      );
    }

    // Default empty state
    return const SizedBox();
  }

  Future<void> _handleVenueTap(VenueModel venue, BuildContext context) async {
    await logAnalytics(venue.id);
    if (venue.feeds != null && context.mounted) {
      await RouteUtils.pushNamed(
        context,
        RoutePaths.feedPlayingPage,
        arguments: {
          'feeds': venue.feeds,
          'title': 'Exclusive',
          'initialIndex': 0,
        },
      );
      return;
    }
    // Show loading indicator
    if (context.mounted) {
      unawaited(
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: LoadingWidget(),
          ),
        ),
      );
    }

    try {
      // Use a repository instance
      final feedRepo = FeedRepository();

      // Fetch feeds directly
      final feeds = await feedRepo.fetchVenueFeeds(venue.id, limit: 6);
      if (context.mounted) {
        // Update the venue in the cubit by emitting a new state
        final daySpecialCubit = context.read<DayspecialCubit>();
        final currentVenues =
            List<VenueModel>.from(daySpecialCubit.state.specialVenues);

        // Find the index of the venue to update
        final venueIndex = currentVenues.indexWhere((v) => v.id == venue.id);
        if (venueIndex != -1) {
          // Update the venue at the found index
          currentVenues[venueIndex] = venue.copyWith(feeds: feeds);
        }

        // Update the state with the new venues list
        if (daySpecialCubit.state is DayspecialLoaded) {
          daySpecialCubit.updateVenueFeeds(
            currentVenues,
            daySpecialCubit.state.totalCount,
          );
        }

        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        // Navigate with the fetched feeds
        await RouteUtils.pushNamed(
          context,
          RoutePaths.feedPlayingPage,
          arguments: {
            'feeds': feeds,
            'title': 'Exclusive',
            'initialIndex': 0,
          },
        );
      }
    } catch (e) {
      if (context.mounted) {
        // Close loading dialog
        Navigator.of(context, rootNavigator: true).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load content: $e')),
        );
      }
    }
  }
}

class HighlightVenueTileShimmer extends StatelessWidget {
  const HighlightVenueTileShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 12),
      width: 280,
      height: 200,
      decoration: BoxDecoration(
        boxShadow: AppShadowStyles.baseStyle,
        borderRadius: BorderRadius.all(
          Radius.circular(VideoTileSizeUtils.borderRadius),
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.all(
          Radius.circular(VideoTileSizeUtils.borderRadius),
        ),
        child: RepaintBoundary(
          child: Shimmer.fromColors(
            baseColor: darkAppColors.secondaryBackgroundColor,
            highlightColor: Colors.grey.withAlpha((255 * 0.2).toInt()),
            child: Stack(
              children: [
                // Gradient overlay (similar to glassmorphic effect)
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.centerRight,
                        end: Alignment.centerLeft,
                        colors: [
                          Colors.black12,
                          Colors.black12,
                          Colors.black12,
                          Colors.black.withAlpha(120),
                          Colors.black.withAlpha(160),
                        ],
                      ),
                    ),
                  ),
                ),

                // Perk badge placeholder
                Positioned(
                  top: 16,
                  left: 16,
                  child: Container(
                    width: 80,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),

                // Live indicator placeholder
                Positioned(
                  top: 16,
                  right: 16,
                  child: Container(
                    width: 50,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),

                // Content placeholders
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title placeholder
                        Container(
                          width: 180,
                          height: 24,
                          color: Colors.white,
                        ),

                        const SizedBox(height: 6),

                        // Location row placeholder
                        Row(
                          children: [
                            // Location icon placeholder
                            Container(
                              width: 15,
                              height: 15,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 4),
                            // Location text placeholder
                            Container(
                              width: 80,
                              height: 15,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
