import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/helper/get_current_location.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/home/<USER>';

class PlacesNearYouTiles extends StatefulWidget {
  const PlacesNearYouTiles({super.key});

  @override
  State<PlacesNearYouTiles> createState() => _PlacesNearYouTilesState();
}

class _PlacesNearYouTilesState extends State<PlacesNearYouTiles> {
  late final ScrollController _scrollController;
  bool _isLoadingMore = false; // Add this flag
  DateTime _lastLoadTime = DateTime.now(); // Add this for rate limiting

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    _initializeFeeds();
  }

  Future<void> _initializeFeeds() async {
    if (context.read<FeedBloc>().state.nearbyFeeds.isEmpty) {
      final location = context.read<UserBloc>().state.user!.location;

      context.read<FeedBloc>().add(
            FetchNearbyFeedsEvent(
              limit: 5,
              location: await getCurrentLocation() ?? location,
            ),
          );
    }
  }

  void _scrollListener() {
    // Check if we're already loading or if it's too soon for another request
    if (_isLoadingMore ||
        DateTime.now().difference(_lastLoadTime).inSeconds < 2) {
      // Add rate limiting
      return;
    }

    final state = context.read<FeedBloc>().state;
    if (state.nearbyFeeds.isNotEmpty && state.nearbyFeeds.last.isEndMarker) {
      return;
    }

    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 120) {
      _loadMoreFeeds();
    }
  }

  Future<void> _loadMoreFeeds() async {
    final state = context.read<FeedBloc>().state;
    if (!state.nearbyLoading && !_isLoadingMore) {
      setState(() => _isLoadingMore = true);
      _lastLoadTime = DateTime.now();

      final location = context.read<UserBloc>().state.user!.location;
      context.read<FeedBloc>().add(
            FetchNearbyFeedsEvent(
              limit: 5,
              skip: state.nearbyFeeds.length,
              location: location,
            ),
          );

      // Reset loading flag after a delay
      await Future.delayed(const Duration(seconds: 2));
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  @override
  void dispose() {
    _isLoadingMore = false;

    // Reset scroll position
    if (_scrollController.hasClients) {
      _scrollController.jumpTo(0);
    }

    _scrollController
      ..removeListener(_scrollListener)
      ..dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Column(
        key: const PageStorageKey('places_near_you_tiles'),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.nearbyTitle != current.nearbyTitle,
            builder: (context, state) {
              return TitleWidget(
                isLoading: state.nearbyLoading,
                title: state.nearbyTitle,
              );
            },
          ),
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.nearbyFeeds != current.nearbyFeeds,
            builder: (context, state) {
              return TilesView(
                scrollController: _scrollController,
                state: state,
                stateLoading: state.nearbyLoading,
                vibes: state.nearbyFeeds,
                title: state.nearbyTitle,
              );
            },
          ),
        ],
      ),
    );
  }
}
