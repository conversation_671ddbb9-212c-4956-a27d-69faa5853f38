import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';

import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/video_tiles/vibe_tags.dart';

class BySceneTiles extends StatefulWidget {
  const BySceneTiles({super.key});

  @override
  State<BySceneTiles> createState() => _BySceneTilesState();
}

class _BySceneTilesState extends State<BySceneTiles> {
  int selectedCategoryIndex = 0;
  late final ScrollController _tileScrollController;
  bool _isLoadingMore = false;
  bool _isLoading = false;
  DateTime _lastLoadTime = DateTime.now();

  final List<String> _defaultSubCategories = const [
    'Live Music',
    'Sports bar/pub',
    'Bar',
    'Speakeasy',
    'Barcade',
    'Nightclub',
    'Rooftop',
    'Lounge',
  ];

  // Scene categories
  late List<String> subCategories = _defaultSubCategories;

  @override
  void initState() {
    super.initState();
    _tileScrollController = ScrollController();

    _tileScrollController.addListener(_scrollListener);

    Future.microtask(() {
      fetchScenes().then((_) {
        _initializeFeeds();
      });
    });
  }

  void _initializeFeeds() {
    context.read<FeedBloc>().add(
          FetchSceneFeedsEvent(
            subCategories[selectedCategoryIndex],
            limit: 5,
          ),
        );
  }

  Future<void> fetchScenes() async {
    final feedBloc = context.read<FeedBloc>();

    // Use cached scenes if available
    if (feedBloc.getScenes.isNotEmpty) {
      if (mounted) {
        setState(() {
          subCategories = feedBloc.getScenes;
        });
      }
      return;
    }

    // Otherwise fetch scenes
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
          // Use default categories while loading
          subCategories = _defaultSubCategories;
        });
      }

      final FeedRepository feedRepository = FeedRepository();
      final fetchedScenes = await feedRepository.fetchScenesForFeeds();

      // Only update state if widget is still mounted
      if (fetchedScenes.isNotEmpty && mounted) {
        setState(() {
          subCategories = fetchedScenes;
        });
      }
    } catch (e) {
      AppLogger.error('Error fetching scenes: $e');
    } finally {
      // Always check mounted in finally block
      if (mounted) {
        setState(() {
          _isLoading = false;
          feedBloc.setScenes = subCategories;
        });
      }
    }
  }

  void _scrollListener() {
    if (!mounted) return;

    if (_isLoadingMore ||
        DateTime.now().difference(_lastLoadTime).inSeconds < 2) {
      return;
    }

    final state = context.read<FeedBloc>().state;
    final currentSceneFeeds =
        state.sceneFeeds[subCategories[selectedCategoryIndex]] ?? [];

    if (currentSceneFeeds.isNotEmpty && currentSceneFeeds.last.isEndMarker) {
      return;
    }

    if (_tileScrollController.position.pixels >=
        _tileScrollController.position.maxScrollExtent - 120) {
      _loadMoreFeeds();
      return;
    }
  }

  Future<void> _loadMoreFeeds() async {
    if (!mounted) return;

    final state = context.read<FeedBloc>().state;
    if (state.sceneLoading || _isLoadingMore) {
      return;
    }

    try {
      // Set loading state
      if (mounted) {
        setState(() => _isLoadingMore = true);
      }
      _lastLoadTime = DateTime.now();

      // Add event to load more
      context.read<FeedBloc>().add(
            FetchSceneFeedsEvent(
              subCategories[selectedCategoryIndex],
              limit: 5,
              skip: state.sceneFeeds[subCategories[selectedCategoryIndex]]
                      ?.length ??
                  0,
            ),
          );

      // Add a small delay to prevent rapid requests
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      AppLogger.error('Error loading more feeds: $e');
    } finally {
      // Always check mounted in finally
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  void _onCategoryTap(int index) {
    if (!mounted) return;

    HapticFeedback.mediumImpact();

    setState(() {
      selectedCategoryIndex = index;
      // Reset scroll position
      _tileScrollController.jumpTo(0);
    });

    // Request new feeds for selected category
    context.read<FeedBloc>().add(
          FetchSceneFeedsEvent(
            subCategories[index],
            limit: 5,
          ),
        );
  }

  @override
  void dispose() {
    _isLoadingMore = false;
    _isLoading = false;

    // Reset the selected category index to 0
    selectedCategoryIndex = 0;

    // Reset scroll position
    if (_tileScrollController.hasClients) {
      _tileScrollController.jumpTo(0);
    }

    _tileScrollController
      ..removeListener(_scrollListener)
      ..dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Column(
        key: const PageStorageKey('by_scene_tiles'),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.sceneTitle != current.sceneTitle,
            builder: (context, state) {
              return TitleWidget(
                isLoading: state.sceneLoading,
                title: state.sceneTitle,
              );
            },
          ),
          Container(
            height: 40,
            margin: const EdgeInsets.only(bottom: 24),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: subCategories.length,
              padding: SizeUtils.horizontalPadding,
              clipBehavior: Clip.none,
              itemBuilder: (ctx, index) => _isLoading
                  ? buildShimmerTag()
                  : buildVibeTag(
                      isSelected: selectedCategoryIndex == index,
                      title: subCategories[index],
                      onTap: () => _onCategoryTap(index),
                    ),
            ),
          ),
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.sceneFeeds[subCategories[selectedCategoryIndex]] !=
                current.sceneFeeds[subCategories[selectedCategoryIndex]],
            builder: (context, state) {
              // Handle error or empty state
              if (state is FeedError &&
                  (state.sceneFeeds[subCategories[selectedCategoryIndex]]
                          ?.isEmpty ??
                      true)) {
                return const SizedBox();
              }

              // Build tiles view
              return TilesView(
                scrollController: _tileScrollController,
                state: state,
                stateLoading: state.sceneLoading,
                title: state.sceneTitle,
                vibes: state.sceneFeeds[subCategories[selectedCategoryIndex]] ??
                    [],
                scene: subCategories[selectedCategoryIndex],
              );
            },
          ),
        ],
      ),
    );
  }
}
