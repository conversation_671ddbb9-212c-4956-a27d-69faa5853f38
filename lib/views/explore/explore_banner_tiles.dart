import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/cubits/filter/filter_cubit.dart';

import 'package:vibeo/screens/screen.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/utils/size_utils.dart';

class ExploreBannerTiles extends StatelessWidget {
  final VoidCallback? onTap;
  const ExploreBannerTiles({required this.onTap, super.key});

  static const List<Map<String, dynamic>> tiles = [
    {
      'title': 'Explore Exclusive offers from top venues',
      'image': 'assets/banner/offer.jpg',
      'action': 'Browse Offers',
      'route': '/offers',
    },
    {
      'title': 'Want personalized options? Check out Beo!',
      'image': 'assets/banner/beo.png',
      'action': 'Explore Beo',
      'route': '/beo',
    },
  ];

  Future<void> logAnalytics(String interactionType) async {
    final analytics = AnalyticsService.instance;
    await analytics.logBannerInteraction(interactionType: interactionType);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Container(
        height: 180,
        padding: SizeUtils.horizontalPadding,
        child: ListView.separated(
          key: const PageStorageKey('explore_banner_tiles'),
          scrollDirection: Axis.horizontal,
          clipBehavior: Clip.none,
          itemBuilder: (context, index) {
            return Container(
              width: MediaQuery.of(context).size.width *
                  0.8, // Set width to 80% of screen width
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: darkAppColors.secondaryBackgroundColor,
                borderRadius: const BorderRadius.all(
                  Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 2, // Adjust flex ratio for text portion
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tiles[index]['title'].toString(),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const Spacer(),
                          ElevatedButton(
                            onPressed: () {
                              logAnalytics(tiles[index]['route'].toString());
                              if (tiles[index]['route'] == '/beo') {
                                Navigator.pushAndRemoveUntil(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const HomePage(initialIndex: 1),
                                  ),
                                  (_) => false,
                                );
                              } else if (tiles[index]['route'] == '/offers') {
                                context
                                    .read<FilterCubit>()
                                    .selectFilter('Offers');
                                onTap!();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  darkAppColors.secondaryBackgroundColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(100),
                                side: const BorderSide(
                                  color: Colors.white38,
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Text(tiles[index]['action'].toString()),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1, // Adjust flex ratio for image portion
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(16),
                        bottomRight: Radius.circular(16),
                      ),
                      child: Image.asset(
                        tiles[index]['image'].toString(),
                        fit: BoxFit.cover,
                        height: double.infinity,
                        cacheHeight: 200,
                        width: 200,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
          separatorBuilder: (context, index) => const SizedBox(width: 16),
          itemCount: tiles.length,
        ),
      ),
    );
  }
}
