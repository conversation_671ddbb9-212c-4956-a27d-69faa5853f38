import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/data/genres.dart';
import 'package:vibeo/constants/video_tiles.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';

import 'package:vibeo/logic/feed/bloc/feed_state.dart';

import 'package:vibeo/routes/route.dart';

import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/shadow_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/utils/size_utils.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';
import 'package:vibeo/widgets/home/<USER>';

class ByMusicTiles extends StatelessWidget {
  const ByMusicTiles({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Column(
        key: const PageStorageKey('by_music_tiles'),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<FeedBloc, FeedState>(
            builder: (context, state) {
              return TitleWidget(
                isLoading: state.byMusicLoading,
                title: state.byMusicTitle,
              );
            },
          ),
          SizedBox(
            height: 150,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: genresList.length,
              padding: SizeUtils.horizontalPadding,
              clipBehavior: Clip.none,
              itemBuilder: (ctx, index) => BlocBuilder<FeedBloc, FeedState>(
                builder: (context, state) {
                  return _buildGenreItem(state, index, context);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenreItem(FeedState state, int index, BuildContext context) {
    final genre = genresList[index].name;
    return HapticButton(
      onTap: () async {
        try {
          final AnalyticsService analytics = AnalyticsService.instance;
          await analytics.logMusicTab(
            musicType: genre,
          );
        } catch (e) {
          AppLogger.error('Error logging music tab: $e');
        } finally {
          if (context.mounted) {
            _handleGenreTap(state, genre, context);
          }
        }
      },
      child: Container(
        margin: const EdgeInsets.only(right: 16),
        width: 150,
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor,
          boxShadow: AppShadowStyles.baseStyle,
          borderRadius: BorderRadius.all(
            Radius.circular(VideoTileSizeUtils.borderRadius),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.all(
            Radius.circular(VideoTileSizeUtils.borderRadius),
          ),
          child: RepaintBoundary(
            child: Image.asset(
              genresList[index].image,
              cacheHeight: 150,
              cacheWidth: 150,
              width: 200,
              height: 200,
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }

  void _handleGenreTap(FeedState state, String genre, BuildContext context) {
    RouteUtils.pushNamed(
      context,
      RoutePaths.feedPlayingPage,
      arguments: {
        'initialIndex': 0,
        'feeds': state.musicGenreFeeds[genre],
        'title': "What's Playing",
        'genre': genre,
      },
    );
  }
}
