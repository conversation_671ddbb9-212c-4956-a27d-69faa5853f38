import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';
import 'package:vibeo/themes/constant_theme.dart';

import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/home/<USER>';

import 'package:vibeo/widgets/video_tiles/vibe_tags.dart';

class BeoAIRecTiles extends StatefulWidget {
  const BeoAIRecTiles({super.key});

  @override
  State<BeoAIRecTiles> createState() => _BeoAIRecTilesState();
}

class _BeoAIRecTilesState extends State<BeoAIRecTiles> {
  int selectedCategoryIndex = 0;
  late final ScrollController _tileScrollController;
  bool _isLoadingMore = false;
  bool _isLoading = false;
  DateTime _lastLoadTime = DateTime.now();

  final List<Map<String, String>> _defaultSubCategories = const [
    {'title': 'Talk & Dance 💃🗣️', 'id': 'bt1a3f'},
    {'title': '30+ & Vibing 🧑‍💼', 'id': 'bt2b7k'},
    {'title': 'Girls Night 👯‍♀️', 'id': 'bt3c9p'},
    {'title': 'Date Night 💑', 'id': 'bt4d2q'},
    {'title': 'Game Night 🕹️🍻', 'id': 'bt5e6r'},
    {'title': 'Boys Night 🍻👬', 'id': 'bt6f8s'},
    {'title': 'College Crew 🧢🎓', 'id': 'bt7g1t'},
    {'title': 'Live Performers 🎤', 'id': 'bt8h4u'},
    {'title': 'Low Light 🌒', 'id': 'bt9j5v'},
    {'title': 'Festive 🎉', 'id': 'bt10kz'},
  ];

  // Scene categories
  late List<Map<String, String>> subCategories = _defaultSubCategories;

  @override
  void initState() {
    super.initState();
    _tileScrollController = ScrollController();

    _tileScrollController.addListener(_scrollListener);

    // Use Future.microtask to avoid calling setState during build
    Future.microtask(() async {
      if (!mounted) return;
      await fetchBeoTypes();
      if (mounted) {
        _initializeFeeds();
      }
    });
  }

  void _initializeFeeds() {
    if (!mounted) return;

    context.read<FeedBloc>().add(
          FetchBeoAIFeedsEvent(
            subCategories[selectedCategoryIndex]['id']!,
            limit: 5,
          ),
        );
  }

  Future<void> fetchBeoTypes() async {
    final feedBloc = context.read<FeedBloc>();

    // Use cached scenes if available
    if (feedBloc.getbeoTypes.isNotEmpty) {
      if (mounted) {
        setState(() {
          subCategories = feedBloc.getbeoTypes;
        });
      }
      return;
    }

    // Otherwise fetch scenes
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
          // Use default categories while loading
          subCategories = _defaultSubCategories;
        });
      }

      final FeedRepository feedRepository = FeedRepository();
      final fetchedBeoTypeFeeds = await feedRepository.fetchBeoTypesForFeeds();

      // Only update state if widget is still mounted
      if (fetchedBeoTypeFeeds.isNotEmpty && mounted) {
        setState(() {
          subCategories = fetchedBeoTypeFeeds;
        });
      }
    } catch (e) {
      AppLogger.error('Error fetching scenes: $e');
    } finally {
      // Always check mounted in finally block
      if (mounted) {
        setState(() {
          _isLoading = false;
          feedBloc.setBeoTypes = subCategories;
        });
      }
    }
  }

  void _scrollListener() {
    if (!mounted) return;

    if (_isLoadingMore ||
        DateTime.now().difference(_lastLoadTime).inSeconds < 2) {
      return;
    }

    final state = context.read<FeedBloc>().state;
    final currentBeoTypeFeeds =
        state.beoAIFeeds[subCategories[selectedCategoryIndex]['id']!] ?? [];

    if (currentBeoTypeFeeds.isNotEmpty &&
        currentBeoTypeFeeds.last.isEndMarker) {
      return;
    }

    if (_tileScrollController.position.pixels >=
        _tileScrollController.position.maxScrollExtent - 120) {
      _loadMoreFeeds();
      return;
    }
  }

  Future<void> _loadMoreFeeds() async {
    if (!mounted) return;

    final state = context.read<FeedBloc>().state;
    if (state.beoAILoading || _isLoadingMore) {
      return;
    }

    try {
      // Set loading state
      if (mounted) {
        setState(() => _isLoadingMore = true);
      }
      _lastLoadTime = DateTime.now();

      // Add event to load more
      context.read<FeedBloc>().add(
            FetchBeoAIFeedsEvent(
              subCategories[selectedCategoryIndex]['id']!,
              limit: 5,
              skip: state
                      .beoAIFeeds[subCategories[selectedCategoryIndex]['id']!]
                      ?.length ??
                  0,
            ),
          );

      // Add a small delay to prevent rapid requests
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      AppLogger.error('Error loading more feeds: $e');
    } finally {
      // Always check mounted in finally
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  void _onCategoryTap(int index) {
    if (!mounted) return;

    HapticFeedback.mediumImpact();

    setState(() {
      selectedCategoryIndex = index;
      // Reset scroll position
      _tileScrollController.jumpTo(0);
    });

    // Request new feeds for selected category
    context.read<FeedBloc>().add(
          FetchBeoAIFeedsEvent(
            subCategories[index]['id']!,
            limit: 5,
          ),
        );
  }

  @override
  void dispose() {
    _isLoadingMore = false;
    _isLoading = false;

    // Reset the selected category index to 0
    selectedCategoryIndex = 0;

    // Reset scroll position
    if (_tileScrollController.hasClients) {
      _tileScrollController.jumpTo(0);
    }

    _tileScrollController
      ..removeListener(_scrollListener)
      ..dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Stack(
        children: [
          Positioned(
            left: -200,
            top: -80,
            child: RadialGlowCircle(
              size: 500,
              color: darkAppColors.deepPurple.withAlpha(150),
            ),
          ),
          Positioned(
            top: 20,
            right: -200,
            child: RadialGlowCircle(
              size: 500,
              color: darkAppColors.deepPurple.withAlpha(150),
            ),
          ),
          Column(
            key: const PageStorageKey('by_beoAI_tiles'),
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BlocBuilder<FeedBloc, FeedState>(
                buildWhen: (previous, current) =>
                    previous.beoAITitle != current.beoAITitle,
                builder: (context, state) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      BlocBuilder<FeedBloc, FeedState>(
                        buildWhen: (previous, current) =>
                            previous.beoAITitle != current.beoAITitle,
                        builder: (context, state) {
                          return TitleWidget(
                            isLoading: state.beoAILoading,
                            title: state.beoAITitle,
                            isBeo: true,
                          );
                        },
                      ),
                      Padding(
                        padding: SizeUtils.horizontalPadding.copyWith(
                          bottom: 12,
                        ),
                        child: const Text(
                          'AI-curated. Experimental. Might miss, might nail it.',
                          maxLines: 1,
                          style: TextStyle(
                            color: Colors.white60,
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
              Container(
                height: 60,
                margin: const EdgeInsets.only(bottom: 18),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: subCategories.length,
                  padding: SizeUtils.horizontalPadding,
                  clipBehavior: Clip.none,
                  itemBuilder: (ctx, index) => _isLoading
                      ? buildShimmerTag()
                      : buildVibeTag(
                          isSelected: selectedCategoryIndex == index,
                          title: subCategories[index]['title']!,
                          onTap: () async {
                            try {
                              final AnalyticsService analytics =
                                  AnalyticsService.instance;
                              await analytics.logBeoAITab(
                                beoAIType: subCategories[index]['title']!,
                              );
                            } catch (e) {
                              AppLogger.error('Error logging beoAI tab: $e');
                            } finally {
                              if (context.mounted) {
                                _onCategoryTap(index);
                              }
                            }
                          },
                          isBeo: true,
                        ),
                ),
              ),
              BlocBuilder<FeedBloc, FeedState>(
                buildWhen: (previous, current) =>
                    previous.beoAIFeeds[subCategories[selectedCategoryIndex]
                        ['id']!] !=
                    current.beoAIFeeds[subCategories[selectedCategoryIndex]
                        ['id']!],
                builder: (context, state) {
                  // Handle error or empty state
                  if (state is FeedError &&
                      (state
                              .beoAIFeeds[subCategories[selectedCategoryIndex]
                                  ['id']!]
                              ?.isEmpty ??
                          true)) {
                    return const SizedBox();
                  }

                  // Build tiles view
                  return TilesView(
                    scrollController: _tileScrollController,
                    state: state,
                    stateLoading: state.beoAILoading,
                    title: state.beoAITitle,
                    vibes: state.beoAIFeeds[subCategories[selectedCategoryIndex]
                            ['id']!] ??
                        [],
                    scene: subCategories[selectedCategoryIndex]['title'],
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class RadialGlowCircle extends StatelessWidget {
  final double size;
  final Color color;

  const RadialGlowCircle({
    super.key,
    this.size = 200,
    this.color = Colors.red,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            color.withAlpha((255 * 0.6).toInt()), // Strong center
            color.withAlpha((255 * 0.2).toInt()), // Faded edge
            Colors.transparent, // Fully transparent outer edge
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
    );
  }
}
