import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_event.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/feed/bloc/feed_state.dart';

import 'package:vibeo/utils/utils.dart';
import 'package:vibeo/widgets/feed/explore_feed_tags.dart';

import 'package:vibeo/widgets/home/<USER>';
import 'package:vibeo/widgets/home/<USER>';

class ByAreaTiles extends StatefulWidget {
  const ByAreaTiles({super.key});

  @override
  State<ByAreaTiles> createState() => _ByAreaTilesState();
}

class _ByAreaTilesState extends State<ByAreaTiles> {
  int selectedLocationIndex = 0;
  late final ScrollController _tileScrollController;

  bool _isLoadingMore = false;
  bool _isLoading = false;
  DateTime _lastLoadTime = DateTime.now();

  late List<Map<String, dynamic>> areas;

  @override
  void initState() {
    super.initState();
    _tileScrollController = ScrollController();

    _tileScrollController.addListener(_scrollListener);

    // Reset the selected location index to ensure we start from the first area
    selectedLocationIndex = 0;

    fetchAreas().then((_) {
      _initializeFeeds();
    });
  }

  void _initializeFeeds() {
    if (areas.isNotEmpty && mounted) {
      context.read<FeedBloc>().add(
            FetchAreaFeedsEvent(
              areas[selectedLocationIndex]['area'] as String,
              limit: 5,
            ),
          );
    }
  }

  Future<void> _loadMoreFeeds() async {
    final state = context.read<FeedBloc>().state;
    if (!state.byAreaLoading && !_isLoadingMore) {
      if (!mounted) return; // Early return if widget is not mounted

      setState(() => _isLoadingMore = true);
      _lastLoadTime = DateTime.now();

      context.read<FeedBloc>().add(
            FetchAreaFeedsEvent(
              areas[selectedLocationIndex]['area'] as String,
              limit: 5,
              skip: state.areaFeeds[areas[selectedLocationIndex]['area']]
                      ?.length ??
                  0,
            ),
          );

      // Use a try-finally to ensure _isLoadingMore is reset
      try {
        await Future.delayed(const Duration(seconds: 2));
      } finally {
        if (mounted) {
          setState(() => _isLoadingMore = false);
        }
      }
    }
  }

  Future<void> fetchAreas() async {
    const defaultAreas = [
      {'area': 'River North', 'live': false},
      {'area': 'West Loop', 'live': false},
      {'area': 'Wrigleyville', 'live': false},
      {'area': 'Boystown', 'live': false},
      {'area': 'Wicker Park', 'live': false},
      {'area': 'Lincoln Park', 'live': false},
      {'area': 'Downtown', 'live': false},
      {'area': 'Old Town', 'live': false},
    ];

    final feedBloc = context.read<FeedBloc>();

    if (feedBloc.getAreas.isNotEmpty) {
      if (!mounted) return; // Check mounted before setState
      setState(() {
        areas = feedBloc.getAreas;
      });
      return;
    }

    try {
      if (!mounted) return; // Check mounted before setState
      setState(() {
        _isLoading = true;
        areas = defaultAreas;
      });

      final FeedRepository feedRepository = FeedRepository();
      final fetchedAreas = await feedRepository.fetchAreasForFeeds();

      if (fetchedAreas.isNotEmpty && mounted) {
        // Check mounted before setState
        setState(() {
          areas = fetchedAreas;
        });
      }
    } catch (e) {
      AppLogger.error(e.toString());
    } finally {
      if (mounted) {
        // Check mounted before setState in finally block
        setState(() {
          _isLoading = false;
          feedBloc.setAreas = areas;
        });
      }
    }
  }

  void _scrollListener() {
    if (!mounted) return;

    if (_isLoadingMore ||
        DateTime.now().difference(_lastLoadTime).inSeconds < 2) {
      return;
    }

    final state = context.read<FeedBloc>().state;
    final currentAreaFeeds =
        state.areaFeeds[areas[selectedLocationIndex]['area']] ?? [];

    if (currentAreaFeeds.isNotEmpty && currentAreaFeeds.last.isEndMarker) {
      return;
    }

    if (_tileScrollController.position.pixels >=
        _tileScrollController.position.maxScrollExtent - 120) {
      _loadMoreFeeds();
      return;
    }
  }

  @override
  void dispose() {
    _isLoadingMore = false;
    _isLoading = false;

    // Reset the selected location index to 0
    selectedLocationIndex = 0;

    // Reset scroll position
    if (_tileScrollController.hasClients) {
      _tileScrollController.jumpTo(0);
    }

    _tileScrollController
      ..removeListener(_scrollListener)
      ..dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      key: const PageStorageKey('by_area_tiles'),
      padding: const EdgeInsets.symmetric(vertical: 26),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.byAreaTitle != current.byAreaTitle,
            builder: (context, state) {
              return TitleWidget(
                isLoading: state.byAreaLoading,
                title: state.byAreaTitle,
              );
            },
          ),
          ExploreFeedTags(
            key: const PageStorageKey('by_area_tags'),
            tags: areas,
            isLoading: _isLoading,
            selectedIndex: selectedLocationIndex,
            onTap: (int index) {
              HapticFeedback.mediumImpact();

              setState(() {
                selectedLocationIndex = index;
                _tileScrollController.jumpTo(0);
              });
              context.read<FeedBloc>().add(
                    FetchAreaFeedsEvent(
                      areas[index]['area'] as String,
                      limit: 5,
                    ),
                  );
            },
          ),
          BlocBuilder<FeedBloc, FeedState>(
            buildWhen: (previous, current) =>
                previous.areaFeeds != current.areaFeeds,
            builder: (context, state) {
              if (state is FeedError &&
                  (state.areaFeeds[areas[selectedLocationIndex]['area']]
                          ?.isEmpty ??
                      true)) {
                return const SizedBox();
              }
              return TilesView(
                key: const PageStorageKey('by_area_tiles_view'),
                scrollController: _tileScrollController,
                state: state,
                stateLoading: state.byAreaLoading,
                title: state.byAreaTitle,
                vibes:
                    state.areaFeeds[areas[selectedLocationIndex]['area']] ?? [],
                area: areas[selectedLocationIndex]['area'] as String,
              );
            },
          ),
        ],
      ),
    );
  }
}
