import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/utils/size_utils.dart';

class FeedPlayingShimmerPage extends StatelessWidget {
  const FeedPlayingShimmerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkAppColors.backgroundColor,
      body: RepaintBoundary(
        child: Padding(
          padding: SizeUtils.horizontalPadding,
          child: Column(
            children: [
              // Animated Bars Shimmer
              Container(
                margin: const EdgeInsets.only(top: 60),
                child: Row(
                  children: List.generate(4, (index) {
                    return Expanded(
                      child: Container(
                        height: 2,
                        margin: const EdgeInsets.symmetric(horizontal: 2),
                        decoration: BoxDecoration(
                          color: darkAppColors.lightColor.withAlpha(50),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(100)),
                        ),
                      ),
                    );
                  }),
                ),
              ),

              const Spacer(),
              // Header Row Shimmer
              SafeArea(
                child: Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Shimmer.fromColors(
                            baseColor: darkAppColors.lightColor.withAlpha(50),
                            highlightColor: darkAppColors
                                .secondaryBackgroundColor
                                .withAlpha(100),
                            child: Container(
                              width: 100,
                              height: 20,
                              decoration: const BoxDecoration(
                                color: Colors.grey,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(100)),
                              ),
                            ),
                          ),
                          Shimmer.fromColors(
                            baseColor: darkAppColors.lightColor.withAlpha(50),
                            highlightColor: darkAppColors
                                .secondaryBackgroundColor
                                .withAlpha(100),
                            child: Row(
                              children: [
                                Container(
                                  width: 30,
                                  height: 30,
                                  decoration: const BoxDecoration(
                                    color: Colors.grey,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Container(
                                  width: 30,
                                  height: 30,
                                  decoration: const BoxDecoration(
                                    color: Colors.grey,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Title Shimmer
                      Shimmer.fromColors(
                        baseColor: darkAppColors.lightColor.withAlpha(50),
                        highlightColor: darkAppColors.secondaryBackgroundColor
                            .withAlpha(100),
                        child: Container(
                          width: SizeUtils.screenWidth * 0.5,
                          height: 30,
                          decoration: const BoxDecoration(
                            color: Colors.grey,
                            borderRadius:
                                BorderRadius.all(Radius.circular(100)),
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Music Section Shimmer
                      Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: darkAppColors.lightColor.withAlpha(50),
                            highlightColor: darkAppColors
                                .secondaryBackgroundColor
                                .withAlpha(100),
                            child: const Icon(
                              Icons.music_note_rounded,
                              color: Colors.grey,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 10),
                          Shimmer.fromColors(
                            baseColor: darkAppColors.lightColor.withAlpha(50),
                            highlightColor: darkAppColors
                                .secondaryBackgroundColor
                                .withAlpha(100),
                            child: Container(
                              width: MediaQuery.of(context).size.width * 0.6,
                              height: 20,
                              decoration: const BoxDecoration(
                                color: Colors.grey,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(100)),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Venue Section Shimmer
                      Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: darkAppColors.lightColor.withAlpha(50),
                            highlightColor: darkAppColors
                                .secondaryBackgroundColor
                                .withAlpha(100),
                            child: const Icon(
                              Icons.door_back_door_outlined,
                              color: Colors.grey,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 10),
                          Shimmer.fromColors(
                            baseColor: darkAppColors.lightColor.withAlpha(50),
                            highlightColor: darkAppColors
                                .secondaryBackgroundColor
                                .withAlpha(100),
                            child: Container(
                              width: 100,
                              height: 20,
                              decoration: const BoxDecoration(
                                color: Colors.grey,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(100)),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 50),
            ],
          ),
        ),
      ),
    );
  }
}
