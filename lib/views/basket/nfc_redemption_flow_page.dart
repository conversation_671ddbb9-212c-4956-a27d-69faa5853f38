import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/logic/nfc/nfc_operation.dart';
import 'package:vibeo/logic/redemption/cubit/redemption_cubit.dart';
import 'package:vibeo/logic/redemption/cubit/redemption_state.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/redemption/redemption_models.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/screens/basket/redemption_summary_page.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/widgets/basket/get_help_button.dart';
import 'package:vibeo/widgets/haptic_feedback.dart';

class NfcRedemptionFlowPage extends StatefulWidget {
  final OfferModel offer;
  final String venueID;

  const NfcRedemptionFlowPage({
    required this.offer,
    required this.venueID,
    super.key,
  });

  @override
  State<NfcRedemptionFlowPage> createState() => _NfcRedemptionFlowPageState();
}

class _NfcRedemptionFlowPageState extends State<NfcRedemptionFlowPage> {
  int _currentStep = 0;
  bool _isProcessing = false;
  String? _errorMessage;
  RedemptionConfirmation? _confirmation;
  String? _tagUID;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: darkAppColors.backgroundColor,
        appBar: CupertinoNavigationBar(
          automaticallyImplyLeading: false,
          backgroundColor: Colors.transparent,
          middle: const Text(
            'Redeem Offer',
            style: TextStyle(
              color: Colors.white,
            ),
          ),
          trailing: buildGetHelpButton(context),
        ),
        body: BlocListener<RedemptionCubit, RedemptionState>(
          listener: (context, state) {
            if (state is RedemptionInitiated) {
              // When redemption is initiated, stay on step 0 but start confirmation
              setState(() {
                _isProcessing = false;
              });

              // Automatically confirm the redemption
              _confirmRedemption(state.response.uniqueCode, _tagUID!);
            } else if (state is RedemptionConfirmed) {
              // When redemption is confirmed, move to step 1 (confirmation)
              setState(() {
                _currentStep = 1;
                _isProcessing = false;
                _confirmation = state.confirmation;
              });
            } else if (state is RedemptionError) {
              setState(() {
                _isProcessing = false;
                _errorMessage = state.message;
              });
            } else if (state is RedemptionLoading) {
              setState(() {
                _isProcessing = true;
                _errorMessage = null;
              });
            }
          },
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Redemption steps
                  _buildRedemptionSteps(),

                  const SizedBox(height: 24),

                  // Current step content
                  Expanded(
                    child: _buildCurrentStepContent(),
                  ),

                  // Error message if any
                  if (_errorMessage != null)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(51), // 0.2 * 255 = 51
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _errorMessage!,
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.red,
                        ),
                      ),
                    ),

                  // Action button
                  _buildActionButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRedemptionSteps() {
    return Row(
      children: [
        _buildStepIndicator(0, 'Tap on Vibeo Tag'),
        _buildStepConnector(_currentStep >= 1),
        _buildStepIndicator(1, 'Confirmation'),
      ],
    );
  }

  Widget _buildStepIndicator(int step, String label) {
    final isActive = _currentStep >= step;
    final isCurrentStep = _currentStep == step;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isActive ? darkAppColors.deepPurple : Colors.grey.shade800,
              border: isCurrentStep
                  ? Border.all(color: Colors.white, width: 2)
                  : null,
            ),
            child: Center(
              child: isActive
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 18,
                    )
                  : Text(
                      '${step + 1}',
                      style: AppTextStyles.bodysmallBold.copyWith(
                        color: Colors.white,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: isActive ? Colors.white : Colors.grey,
              fontWeight: isCurrentStep ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepConnector(bool isActive) {
    return Container(
      width: 24,
      height: 2,
      color: isActive ? darkAppColors.deepPurple : Colors.grey.shade800,
    );
  }

  Widget _buildCurrentStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildTapNfcStep();
      case 1:
        return _buildConfirmationStep();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildTapNfcStep() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.radiowaves_right,
            color: Colors.white,
            size: 80,
          ),
          const SizedBox(height: 24),
          Text(
            'Tap your phone on the Vibeo Tag',
            style: AppTextStyles.title.copyWith(
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            'Hold your phone near the Vibeo tag to initiate the redemption process',
            style: AppTextStyles.body.copyWith(
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Show congratulations dialog with two buttons
  void _showCongratulationsDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withAlpha(178), // ~0.7 opacity
      builder: (context) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(28),
                decoration: BoxDecoration(
                  color: darkAppColors.secondaryBackgroundColor.withAlpha(180),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: darkAppColors.deepPurple.withAlpha(100),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Sparkle animation with deep purple color
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0, end: 1),
                      duration: const Duration(milliseconds: 800),
                      curve: Curves.easeOutBack,
                      builder: (context, value, child) {
                        return Transform.scale(
                          scale: value,
                          child: child,
                        );
                      },
                      child: const Icon(
                        CupertinoIcons.checkmark_circle_fill,
                        color: Colors.green,
                        size: 70,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Congratulations text
                    Text(
                      'Congratulations!',
                      style: AppTextStyles.title.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Success message with animation
                    TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 0, end: 1),
                      duration: const Duration(milliseconds: 1000),
                      curve: Curves.easeOut,
                      builder: (context, value, child) {
                        return Opacity(
                          opacity: value,
                          child: Transform.scale(
                            scale: value,
                            child: child,
                          ),
                        );
                      },
                      child: Text(
                        "You've successfully redeemed your offer!",
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Explore More Offers button
                    // HapticButton(
                    //   onTap: () {
                    //     Navigator.pop(context); // Close dialog

                    //     // Navigate to venue description page with showOffersDirect=true
                    //     Navigator.pushNamedAndRemoveUntil(
                    //       context,
                    //       RoutePaths.venueDescPage,
                    //       (route) => false,
                    //       arguments: {
                    //         'venueID': widget.venueID,
                    //         'showOffersDirect': true,
                    //       },
                    //     );
                    //   },
                    //   child: Container(
                    //     width: double.infinity,
                    //     height: 50,
                    //     decoration: BoxDecoration(
                    //       color: darkAppColors.secondaryBackgroundColor,
                    //       borderRadius: BorderRadius.circular(12),
                    //       border: Border.all(
                    //         color: Colors.white.withAlpha(30),
                    //         width: 1,
                    //       ),
                    //     ),
                    //     child: Center(
                    //       child: Text(
                    //         'Explore More Offers',
                    //         style: AppTextStyles.bodysmallBold.copyWith(
                    //           color: Colors.white,
                    //         ),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    // const SizedBox(height: 16),

                    // Back to Homepage button
                    HapticButton(
                      onTap: () {
                        Navigator.pop(context); // Close dialog

                        // Navigate to home page
                        RouteUtils.pushNamedAndRemoveUntil(
                          context,
                          RoutePaths.home,
                          (route) => false,
                        );
                      },
                      child: Container(
                        width: double.infinity,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Center(
                          child: Text(
                            'Back to Homepage',
                            style: AppTextStyles.bodysmallBold.copyWith(
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildConfirmationStep() {
    // return Center(
    //   child: Column(
    //     mainAxisAlignment: MainAxisAlignment.center,
    //     children: [
    //       const Icon(
    //         CupertinoIcons.checkmark_circle_fill,
    //         color: Colors.green,
    //         size: 80,
    //       ),
    //       const SizedBox(height: 24),
    //       Text(
    //         'Redemption Successful!',
    //         style: AppTextStyles.title.copyWith(
    //           color: Colors.white,
    //         ),
    //         textAlign: TextAlign.center,
    //       ),
    //       const SizedBox(height: 16),
    //       Text(
    //         'Your offer has been successfully redeemed',
    //         style: AppTextStyles.body.copyWith(
    //           color: Colors.grey,
    //         ),
    //         textAlign: TextAlign.center,
    //       ),
    //       const SizedBox(height: 32),
    //       if (_confirmation != null)
    //         Container(
    //           padding: const EdgeInsets.all(16),
    //           decoration: BoxDecoration(
    //             color: Colors.green.withAlpha(51), // 0.2 * 255 = 51
    //             borderRadius: BorderRadius.circular(12),
    //             border: Border.all(
    //               color: Colors.green.withAlpha(128), // 0.5 * 255 = 128
    //               width: 1,
    //             ),
    //           ),
    //           child: Column(
    //             crossAxisAlignment: CrossAxisAlignment.start,
    //             children: [
    //               Text(
    //                 'Redemption Details',
    //                 style: AppTextStyles.bodysmallBold.copyWith(
    //                   color: Colors.white,
    //                 ),
    //               ),
    //               const SizedBox(height: 8),
    //               _buildConfirmationDetail(
    //                 'Offer',
    //                 _confirmation!.title,
    //               ),
    //               _buildConfirmationDetail(
    //                 'Venue',
    //                 _confirmation!.venueName,
    //               ),
    //               _buildConfirmationDetail(
    //                 'Date',
    //                 _confirmation!.redemptionDate,
    //               ),
    //               _buildConfirmationDetail(
    //                 'ID',
    //                 _confirmation!.redemptionId,
    //               ),
    //               _buildConfirmationDetail(
    //                 'Price',
    //                 '\$${_confirmation!.price.toStringAsFixed(2)}',
    //               ),
    //             ],
    //           ),
    //         ),
    //     ],
    //   ),
    // );

    return RedemptionSummaryPage(
      offer: widget.offer,
      venueID: widget.venueID,
      confirmation: _confirmation!,
      showDoneButton: false,
    );
  }

  Widget _buildActionButton() {
    if (_isProcessing) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    switch (_currentStep) {
      case 0:
        return Column(
          children: [
            HapticButton(
              onTap: _startNfcRedemption,
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      darkAppColors.deepPurple,
                      darkAppColors.deepPurple
                          .withAlpha(179), // 0.7 * 255 = 179
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    'Scan to Redeem',
                    style: AppTextStyles.bodysmallBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            HapticButton(
              child: Container(
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.white12,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    'Cancel',
                    style: AppTextStyles.bodysmallBold.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              onTap: () {
                RouteUtils.pop(context);
              },
            ),
          ],
        );
      case 1:
        return HapticButton(
          onTap: _showCongratulationsDialog,
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                'Done',
                style: AppTextStyles.bodysmallBold.copyWith(
                  color: Colors.black,
                ),
              ),
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Future<void> _startNfcRedemption() async {
    // Store necessary data before async operation
    final RedemptionCubit redemptionCubit = context.read<RedemptionCubit>();
    final String offerId = widget.offer.id;

    // Get user information from UserBloc
    final userState = context.read<UserBloc>().state;
    if (userState.user == null) {
      setState(() {
        _errorMessage = 'User not logged in';
      });
      return;
    }

    final String userId = userState.user!.uid;
    final String userEmail = userState.user!.email;

    setState(() {
      _isProcessing = true;
      _errorMessage = null;
    });

    final NFCFunctions nfcFunctions = NFCFunctions();

    try {
      final tagId = await nfcFunctions.startNFCOperation(
        operation: NFCOperation.Read,
      );

      if (tagId != null && mounted) {
        setState(() {
          _tagUID = tagId;
        });
        AppLogger.debug('Tag UID: $_tagUID');

        // Initiate redemption process
        await redemptionCubit.initiateRedemption(
          offerId: offerId,
          userId: userId,
          userEmail: userEmail,
        );
      } else if (mounted) {
        setState(() {
          _isProcessing = false;
          _errorMessage = 'Failed to read NFC tag';
        });
      }
    } catch (e) {
      AppLogger.error('NFC error: $e');
      if (mounted) {
        String errorMessage;

        if (e.toString().contains('can only be purchased once per day')) {
          errorMessage = 'You have already redeemed this offer today.';
        } else if (e.toString().contains('NFC')) {
          errorMessage = 'Error reading Vibeo tag. Please try again.';
        } else {
          // Use a more user-friendly message
          errorMessage = 'Failed to redeem offer. Please try again.';
        }

        setState(() {
          _isProcessing = false;
          _errorMessage = errorMessage;
        });
      }
    }
  }

  Future<void> _confirmRedemption(
    String uniqueCode,
    String tagUID,
  ) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      if (mounted) {
        AppLogger.debug('Confirming redemption with NFC');
        AppLogger.debug('Unique code: $uniqueCode, Tag UID: $tagUID');
        await context.read<RedemptionCubit>().confirmRedemptionNFC(
              uniqueCode: uniqueCode,
              tagUID: tagUID,
            );
      }
    } catch (e) {
      AppLogger.error('Confirmation error: $e');
      if (mounted) {
        String errorMessage;

        if (e.toString().contains('not found') ||
            e.toString().contains('404')) {
          errorMessage = 'Redemption code not found or expired.';
        } else if (e.toString().contains('invalid') ||
            e.toString().contains('400')) {
          errorMessage = 'Invalid confirmation details. Please try again.';
        } else {
          // Use a more user-friendly message
          errorMessage = 'Failed to confirm redemption. Please try again.';
        }

        setState(() {
          _isProcessing = false;
          _errorMessage = errorMessage;
        });
      }
    }
  }
}
