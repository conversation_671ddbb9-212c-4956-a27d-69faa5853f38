import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/components/component.dart';
import 'package:vibeo/logic/basket/cubit/basket_cubit.dart';
import 'package:vibeo/logic/basket/cubit/basket_state.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/routes/route.dart';

import 'package:vibeo/screens/basket/categorized_offers_page.dart';

import 'package:vibeo/services/basket/basket_storage_service.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/themes/text_theme.dart';

import 'package:vibeo/utils/size_utils.dart';

import 'package:vibeo/widgets/basket/basket_offer_tile.dart';

class RedeemOfferScreen extends StatefulWidget {
  final String searchQuery;

  const RedeemOfferScreen({
    this.searchQuery = '',
    super.key,
  });

  @override
  State<RedeemOfferScreen> createState() => _RedeemOfferScreenState();
}

class _RedeemOfferScreenState extends State<RedeemOfferScreen> {
  late BasketStorageService _basketStorageService;
  List<VenueModel> _filteredBasketItems = [];

  @override
  void initState() {
    super.initState();
    final userID = context.read<UserBloc>().state.user!.uid;
    _basketStorageService = BasketStorageService(userID);

    // Initialize _filteredBasketItems to an empty list
    _filteredBasketItems = [];

    // Load basket items
    context.read<BasketCubit>().loadBasketItems();
  }

  @override
  void didUpdateWidget(RedeemOfferScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.searchQuery != widget.searchQuery) {
      _updateFilteredItems();
    }
  }

  void _updateFilteredItems() {
    final basketCubit = context.read<BasketCubit>();
    final filteredVenues = basketCubit.filterVenues(widget.searchQuery);

    setState(() {
      _filteredBasketItems = filteredVenues;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BasketCubit, BasketState>(
      listener: (context, state) {
        if (state.status == BasketStatus.loaded) {
          _updateFilteredItems();
        }
      },
      builder: (context, state) {
        // Determine which condition is being met
        final bool showNoResults = _filteredBasketItems.isEmpty &&
            widget.searchQuery.trim().isNotEmpty;

        return Scaffold(
          body: state.status == BasketStatus.loading
              ? const Center(child: LoadingWidget())
              : state.status == BasketStatus.error
                  ? _buildErrorView(state.errorMessage)
                  : state.basketVenues.isEmpty
                      ? _buildEmptyBasket()
                      : showNoResults
                          ? _buildNoSearchResults()
                          : Padding(
                              padding: SizeUtils.horizontalPadding,
                              child: ListView.builder(
                                padding: const EdgeInsets.only(top: 18),
                                itemCount: _filteredBasketItems.length,
                                itemBuilder: (BuildContext context, int index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(
                                      bottom: 12,
                                    ),
                                    child: BasketOfferTile(
                                      venue: _filteredBasketItems[index],
                                      offersAvailable: _basketStorageService
                                          .getBasketItemCountForVenue(
                                        _filteredBasketItems[index].id,
                                      ),
                                      onViewBasket: () {
                                        Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                CategorizedOffersPage(
                                              venue:
                                                  _filteredBasketItems[index],
                                            ),
                                          ),
                                        );
                                      },
                                      onViewVenue: () async {
                                        final VenueRepository venueRepository =
                                            VenueRepository();
                                        final venue = await venueRepository
                                            .fetchVenueByID(
                                          id: _filteredBasketItems[index].id,
                                        );
                                        if (context.mounted) {
                                          context.read<VenueBloc>().add(
                                                AddVenueEvent(venue),
                                              );

                                          await RouteUtils.pushNamed(
                                            context,
                                            RoutePaths.venueDescPage,
                                            arguments: {
                                              'venue': venue,
                                              'showOffersDirectly': true,
                                            },
                                          );
                                        }
                                      },
                                    ),
                                  );
                                },
                              ),
                            ),
        );
      },
    );
  }

  Widget _buildErrorView([String? errorMessage]) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        margin: const EdgeInsets.symmetric(horizontal: 24),
        decoration: BoxDecoration(
          color: darkAppColors.secondaryBackgroundColor.withAlpha(150),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withAlpha(25),
            width: 0.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.exclamationmark_circle,
              size: 64,
              color: Color.fromRGBO(255, 100, 100, 0.7),
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load basket items',
              style: AppTextStyles.body.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage ??
                  'There was a problem connecting to the server. Please check your internet connection and try again.',
              style: AppTextStyles.bodySmall.copyWith(
                color: const Color.fromRGBO(255, 255, 255, 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context
                  .read<BasketCubit>()
                  .loadBasketItems(forceRefresh: true),
              style: ElevatedButton.styleFrom(
                backgroundColor: darkAppColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyBasket() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.shopping_cart,
            size: 64,
            color: Color.fromRGBO(255, 255, 255, 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Your basket is empty',
            style: AppTextStyles.bodySmall.copyWith(
              color: const Color.fromRGBO(255, 255, 255, 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add offers from venues to see them here',
            style: AppTextStyles.bodySmaller.copyWith(
              color: const Color.fromRGBO(255, 255, 255, 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildNoSearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.search,
            size: 64,
            color: Color.fromRGBO(255, 255, 255, 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No venues found',
            style: AppTextStyles.bodySmall.copyWith(
              color: const Color.fromRGBO(255, 255, 255, 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try a different search term',
            style: AppTextStyles.bodySmaller.copyWith(
              color: const Color.fromRGBO(255, 255, 255, 0.5),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }
}
