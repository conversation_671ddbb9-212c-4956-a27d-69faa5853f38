// import 'dart:ui';

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:shimmer/shimmer.dart';
// import 'package:vibeo/helper/helper.dart';
// import 'package:vibeo/logic/offer/controller/offer_controller.dart';
// import 'package:vibeo/logic/user/bloc/user_bloc.dart';
// import 'package:vibeo/models/offer/offer_model.dart';
// import 'package:vibeo/routes/route.dart';
// import 'package:vibeo/themes/constant_theme.dart';
// import 'package:vibeo/themes/text_theme.dart';
// import 'package:vibeo/utils/utils.dart';
// import 'package:vibeo/widgets/venue/countdown_widget.dart';
// import 'package:vibeo/widgets/venue/perks_tag.dart';
// import 'package:vibeo/widgets/haptic_feedback.dart';

// class OfferInfoPage extends StatefulWidget {
//   final OfferModel offer;

//   const OfferInfoPage({required this.offer, super.key});

//   @override
//   State<OfferInfoPage> createState() => _OfferInfoPageState();
// }

// class _OfferInfoPageState extends State<OfferInfoPage> {
//   late OfferController offerController;
//   bool _isSaved = false;

//   @override
//   void initState() {
//     super.initState();
//     offerController = OfferController(context.read<UserBloc>().state.user!.uid);
//     _checkSavedStatus();
//   }

//   Future<void> _checkSavedStatus() async {
//     final isSaved = await offerController.checkIfOfferSaved(widget.offer.id);
//     if (mounted) {
//       setState(() {
//         _isSaved = isSaved;
//       });
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final bool isExclusive = widget.offer.exclusive;

//     return Scaffold(
//       backgroundColor: darkAppColors.backgroundColor,
//       appBar: AppBar(
//         backgroundColor: Colors.transparent,
//         elevation: 0,
//         centerTitle: false,
//         automaticallyImplyLeading: false,
//         title: Container(
//           height: 50,
//           width: 50,
//           constraints: const BoxConstraints(
//             maxHeight: 50,
//             maxWidth: 50,
//           ),
//           child: ClipRRect(
//             borderRadius: const BorderRadius.all(Radius.circular(100)),
//             child: BackdropFilter(
//               filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
//               child: HapticButton(
//                 onTap: () => RouteUtils.pop(context),
//                 child: Container(
//                   padding: const EdgeInsets.all(12),
//                   decoration: const BoxDecoration(
//                     color: Colors.black54,
//                     shape: BoxShape.circle,
//                   ),
//                   child: const Icon(
//                     CupertinoIcons.chevron_back,
//                     color: Colors.white,
//                     size: 20,
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ),
//         actions: [
//           Padding(
//             padding: const EdgeInsets.only(right: 16),
//             child: _buildBookmarkButton(),
//           ),
//         ],
//       ),
//       extendBodyBehindAppBar: true,
//       body: SingleChildScrollView(
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             // Product Image with gradient overlay
//             Stack(
//               children: [
//                 CachedNetworkImage(
//                   imageUrl: widget.offer.imageLink,
//                   width: double.infinity,
//                   height: 300,
//                   fit: BoxFit.cover,
//                   placeholder: (context, url) => Shimmer.fromColors(
//                     baseColor: Colors.grey[800]!,
//                     highlightColor: Colors.grey[600]!,
//                     child: Container(
//                       height: 300,
//                       color: Colors.black,
//                     ),
//                   ),
//                   errorWidget: (context, url, error) => Container(
//                     height: 300,
//                     color: Colors.grey[900],
//                     child: const Icon(Icons.error, color: Colors.white),
//                   ),
//                 ),
//                 // Gradient overlay for better text visibility
//                 Positioned.fill(
//                   child: Container(
//                     decoration: BoxDecoration(
//                       gradient: LinearGradient(
//                         begin: Alignment.topCenter,
//                         end: Alignment.bottomCenter,
//                         colors: [
//                           Colors.transparent,
//                           Colors.black.withOpacity(0.7),
//                         ],
//                         stops: const [0.6, 1.0],
//                       ),
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//             Padding(
//               padding: const EdgeInsets.all(16),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   // Exclusive tag if applicable
//                   if (isExclusive)
//                     Padding(
//                       padding: const EdgeInsets.only(bottom: 12),
//                       child: PerksTag(
//                         colors: [
//                           darkAppColors.deepPurple.withAlpha(204),
//                           darkAppColors.purpleShade.withAlpha(204),
//                         ],
//                         icon: CupertinoIcons.star_fill,
//                         title: 'EXCLUSIVE',
//                       ),
//                     ),

//                   // Product Title
//                   Text(
//                     widget.offer.title,
//                     style: AppTextStyles.title,
//                   ),

//                   // Calories or Info (if available)
//                   if (widget.offer.info != null)
//                     Text(
//                       widget.offer.info!,
//                       style: AppTextStyles.bodySmaller.copyWith(
//                         color: Colors.white70,
//                       ),
//                     ),
//                   const SizedBox(height: 8),

//                   // Price section with discount if applicable
//                   _buildPriceSection(),

//                   if (widget.offer.endDate != '' && widget.offer.showCountdown)
//                     Padding(
//                       padding: const EdgeInsets.symmetric(vertical: 12),
//                       child: CountdownWidget(
//                         expiryDate: DateTime.parse(widget.offer.endDate),
//                       ),
//                     ),

//                   const SizedBox(height: 16),

//                   Row(
//                     children: [
//                       Container(
//                         padding: const EdgeInsets.symmetric(
//                           vertical: 6,
//                           horizontal: 12,
//                         ),
//                         decoration: BoxDecoration(
//                           color: darkAppColors.deepPurple.withOpacity(0.2),
//                           borderRadius: BorderRadius.circular(16),
//                           border: Border.all(
//                             color: darkAppColors.deepPurple.withOpacity(0.3),
//                           ),
//                         ),
//                         child: Text(
//                           '#${widget.offer.priority} most liked',
//                           style: AppTextStyles.bodySmaller.copyWith(
//                             color: Colors.white,
//                           ),
//                         ),
//                       ),
//                       const SizedBox(width: 8),
//                       Text(
//                         'Most popular',
//                         style: AppTextStyles.bodySmaller.copyWith(
//                           color: Colors.white70,
//                         ),
//                       ),
//                     ],
//                   ),
//                   const SizedBox(height: 16),

//                   // Description
//                   Text(
//                     widget.offer.description,
//                     style: AppTextStyles.bodySmall.copyWith(
//                       color: Colors.white70,
//                     ),
//                   ),

//                   const SizedBox(height: 100), // Space for bottom button
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//       bottomNavigationBar: SafeArea(
//         child: Container(
//           padding: const EdgeInsets.symmetric(horizontal: 16),
//           decoration: BoxDecoration(
//             color: darkAppColors.backgroundColor.withOpacity(0.9),
//             boxShadow: [
//               BoxShadow(
//                 color: Colors.black.withOpacity(0.3),
//                 blurRadius: 10,
//                 offset: const Offset(0, -5),
//               ),
//             ],
//           ),
//           child: HapticButton(
//             child: Container(
//               height: 60,
//               padding: const EdgeInsets.symmetric(vertical: 16),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(12),
//               ),
//               child: Center(
//                 child: Text(
//                   widget.offer.canRedeem ? 'Redeem Now' : 'Add to Basket',
//                   style: AppTextStyles.bodysmallBold.copyWith(
//                     color: darkAppColors.secondaryBackgroundColor,
//                   ),
//                 ),
//               ),
//             ),
//             onTap: () {
//               if (widget.offer.externalLink != null) {
//                 openEventLink(widget.offer.externalLink!);
//               } else {
//                 // Handle add to basket functionality
//                 ScaffoldMessenger.of(context).showSnackBar(
//                   const SnackBar(
//                     content: Text('Added to basket'),
//                     duration: Duration(seconds: 2),
//                   ),
//                 );
//               }
//             },
//           ),
//         ),
//       ),
//       persistentFooterButtons: [
//         // if (widget.offer.redemptionNote != null)
//         Container(
//           padding: const EdgeInsets.symmetric(vertical: 8),
//           alignment: Alignment.center,
//           width: double.infinity,
//           child: const Text(
//             'This offer must be redeemed at the venue.',
//             style: TextStyle(
//               fontWeight: FontWeight.bold,
//               color: Colors.red,
//               fontSize: 14,
//             ),
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildBookmarkButton() {
//     return HapticButton(
//       onTap: () async {
//         try {
//           await offerController.onOfferSaved(widget.offer.id);
//           if (mounted) {
//             setState(() {
//               _isSaved = !_isSaved;
//             });
//           }
//         } catch (e) {
//           // Handle error appropriately
//           AppLogger.error('Error toggling bookmark: $e');
//         }
//       },
//       child: ClipRRect(
//         borderRadius: const BorderRadius.all(Radius.circular(100)),
//         child: BackdropFilter(
//           filter: ImageFilter.blur(
//             sigmaX: 10,
//             sigmaY: 10,
//           ),
//           child: Container(
//             height: 40,
//             width: 40,
//             decoration: BoxDecoration(
//               borderRadius: const BorderRadius.all(Radius.circular(100)),
//               color: Colors.white.withAlpha((255 * 0.2).toInt()),
//               border: Border.all(
//                 color: Colors.white.withAlpha((255 * 0.3).toInt()),
//                 width: 1,
//               ),
//             ),
//             child: Icon(
//               _isSaved ? CupertinoIcons.bookmark_fill : CupertinoIcons.bookmark,
//               color: Colors.white,
//               size: 22,
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   Widget _buildPriceSection() {
//     if (widget.offer.discountedValue == null &&
//         widget.offer.offerValue == null) {
//       return const SizedBox.shrink();
//     }

//     return Row(
//       children: [
//         if (widget.offer.discountedValue! > 0 && widget.offer.offerValue! > 0)
//           Row(
//             children: [
//               Text(
//                 '\$${widget.offer.offerValue}',
//                 style: AppTextStyles.bodySmall.copyWith(
//                   decoration: TextDecoration.lineThrough,
//                   color: Colors.white60,
//                 ),
//               ),
//               const SizedBox(width: 8),
//             ],
//           ),
//         ShaderMask(
//           blendMode: BlendMode.srcIn,
//           shaderCallback: (bounds) => const LinearGradient(
//             colors: [
//               Colors.greenAccent,
//               Colors.lightGreenAccent,
//             ],
//           ).createShader(
//             Rect.fromLTWH(0, 0, bounds.width, bounds.height),
//           ),
//           child: Text(
//             widget.offer.discountedValue! > 0
//                 ? '\$${widget.offer.discountedValue}'
//                 : '\$${widget.offer.offerValue}',
//             style: AppTextStyles.bodysmallBold.copyWith(
//               fontSize: 24,
//             ),
//           ),
//         ),
//       ],
//     );
//   }
// }
