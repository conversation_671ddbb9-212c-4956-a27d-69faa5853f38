import 'package:flutter/material.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/logic/redemption/provider/redemption_provider.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/themes/constant_theme.dart';
import 'package:vibeo/views/basket/nfc_redemption_flow_page.dart';
import 'package:vibeo/widgets/basket/redemption_timer_button.dart';

/// Shows a bottom sheet with a button to start the NFC redemption flow
Future<dynamic> showRedemptionDisclaimer(
  BuildContext context,
  OfferModel offer,
  String venueID,
) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: darkAppColors.backgroundColor,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    builder: (context) => _RedemptionDisclaimerContent(
      offer: offer,
      venueID: venueID,
    ),
  );
}

class _RedemptionDisclaimerContent extends StatelessWidget {
  final OfferModel offer;
  final String venueID;

  const _RedemptionDisclaimerContent({
    required this.offer,
    required this.venueID,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RedemptionTimerButton(
            offer: offer,
            onTap: () async {
              final AnalyticsService analytics = AnalyticsService.instance;
              await analytics.trackClickedRedeemNowButton(offer.id);

              if (context.mounted) {
                // Close the bottom sheet
                Navigator.pop(context);

                // Navigate to the NFC redemption flow page
                await Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => RedemptionProvider(
                      child:
                          NfcRedemptionFlowPage(offer: offer, venueID: venueID),
                    ),
                  ),
                );
              }
            },
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
