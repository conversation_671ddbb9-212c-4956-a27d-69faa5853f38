// import 'dart:math';

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:vibeo/models/offer/offer_model.dart';
// import 'package:vibeo/routes/route.dart';
// import 'package:vibeo/themes/constant_theme.dart';
// import 'package:vibeo/utils/utils.dart';
// import 'package:vibeo/widgets/offer/offer_tile.dart';

// class AllOffersPage extends StatefulWidget {
//   const AllOffersPage({super.key});

//   @override
//   State<AllOffersPage> createState() => _AllOffersPageState();
// }

// class _AllOffersPageState extends State<AllOffersPage>
//     with SingleTickerProviderStateMixin {
//   // Sample offers to demonstrate all 5 types
//   late List<OfferModel> offers;
//   late AnimationController _controller;

//   // Animation status
//   bool _isAnimating = false;

//   // Store the starting position of the product
//   Offset? _startPosition;

//   // Store the target position (cart position)
//   Offset? _endPosition;

//   // The widget that will fly to the cart
//   Widget? _flyingWidget;

//   // Cart item count
//   int _cartItemCount = 0;

//   // Key to get cart position
//   final GlobalKey _cartKey = GlobalKey();

//   @override
//   void initState() {
//     super.initState();

//     // Create sample offers for each type
//     offers = [
//       // 1. Regular offer (just for info)
//       OfferModel(
//         id: '1',
//         title: 'Happy Hour Special',
//         description: 'Enjoy 2-for-1 drinks from 5-7pm daily',
//         imageLink:
//             'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
//         redemptionType: OfferRedemptionType.regular,
//         info: 'This offer is available to all customers during happy hour.',
//         isRedeemed: true, // Example of a redeemed offer
//         isLocked: false,
//       ),
//       OfferModel(
//         id: '1',
//         title: 'Happy Hour Special',
//         description: 'Enjoy 2-for-1 drinks from 5-7pm daily',
//         imageLink:
//             'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
//         redemptionType: OfferRedemptionType.regular,
//         info: 'This offer is available to all customers during happy hour.',
//         isRedeemed: false,
//         isLocked: false,
//       ),

//       // 2. Free redeem offer
//       OfferModel(
//         id: '2',
//         title: 'Free Welcome Drink',
//         description: 'Get a complimentary welcome drink on your first visit',
//         imageLink:
//             'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
//         redemptionType: OfferRedemptionType.freeRedeem,
//         info:
//             'One free drink per customer. Valid for first-time visitors only.',
//         isRedeemed: false,
//         isLocked: false,
//       ),

//       // 3. VibePoints redemption offer
//       OfferModel(
//         id: '3',
//         title: 'VIP Lounge Access',
//         description: 'Redeem for exclusive access to our VIP lounge',
//         imageLink:
//             'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
//         redemptionType: OfferRedemptionType.vibePointsRedeem,
//         vibePointsCost: 100,
//         info:
//             'Access includes complimentary bottle service and premium seating.',
//         isLocked: true,
//       ),

//       // 4. VibePoints unlock offer (locked)
//       OfferModel(
//         id: '4',
//         title: r'Spend $20, Get 20% Off Tab',
//         description:
//             'Learn to make signature cocktails with our master mixologist',
//         imageLink:
//             'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
//         redemptionType: OfferRedemptionType.vibePointsUnlock,
//         vibePointsCost: 200,
//         isLocked: true,
//         info: 'Class includes hands-on training and take-home recipe book.',
//       ),

//       // 5. Refer & unlock offer (locked)
//       OfferModel(
//         id: '5',
//         title: 'Private Party Discount',
//         description: 'Get 25% off private event bookings',
//         imageLink:
//             'https://images.pexels.com/photos/1564534/pexels-photo-1564534.jpeg?auto=compress&cs=tinysrgb&dpr=1&w=500',
//         redemptionType: OfferRedemptionType.referUnlock,
//         requiredReferrals: 3,
//         isLocked: true,
//         info:
//             'Valid for events with 10+ guests. Booking must be made 2 weeks in advance.',
//       ),
//     ];

//     offers.sort(
//       (p1, p2) => (p1.isRedeemed ? 1 : 0).compareTo(p2.isRedeemed ? 1 : 0),
//     );

//     _controller = AnimationController(
//       duration: const Duration(milliseconds: 800),
//       vsync: this,
//     );

//     _controller.addStatusListener((status) {
//       if (status == AnimationStatus.completed) {
//         setState(() {
//           _isAnimating = false;
//           _cartItemCount++;
//         });
//       }
//     });
//   }

//   @override
//   void dispose() {
//     _controller.dispose();
//     super.dispose();
//   }

//   void _runAddToCartAnimation(
//     BuildContext context,
//     Widget productWidget,
//     Offset productPosition,
//   ) {
//     // Get the cart position
//     final RenderBox? cartBox =
//         _cartKey.currentContext?.findRenderObject() as RenderBox?;
//     if (cartBox == null) return;

//     final Offset cartPosition = cartBox.localToGlobal(Offset.zero);

//     // Center of the cart icon
//     final double cartCenterX = cartPosition.dx;
//     final double cartCenterY = cartPosition.dy - 100;

//     setState(() {
//       _startPosition = productPosition;
//       _endPosition = Offset(cartCenterX, cartCenterY);
//       _flyingWidget = productWidget;
//       _isAnimating = true;
//       _controller
//         ..reset()
//         ..forward();
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: CupertinoNavigationBar(
//         backgroundColor: darkAppColors.secondaryBackgroundColor.withAlpha(100),
//         middle: const Text(
//           'Gamified Offers Demo',
//           style: TextStyle(
//             color: Colors.white,
//           ),
//         ),
//         previousPageTitle: 'Back',
//       ),
//       body: Stack(
//         children: [
//           Padding(
//             padding: SizeUtils.horizontalPadding,
//             child: Column(
//               children: [
//                 Padding(
//                   padding: const EdgeInsets.only(top: 20, bottom: 10),
//                   child: Row(
//                     children: [
//                       Expanded(
//                         child: Container(
//                           padding: const EdgeInsets.all(16),
//                           decoration: BoxDecoration(
//                             color: Colors.purple.withAlpha(50),
//                             borderRadius: BorderRadius.circular(12),
//                             border: Border.all(
//                               color: Colors.purpleAccent.withAlpha(100),
//                             ),
//                           ),
//                           child: const Row(
//                             children: [
//                               Icon(
//                                 CupertinoIcons.sparkles,
//                                 color: Colors.purpleAccent,
//                               ),
//                               SizedBox(width: 8),
//                               Text(
//                                 'Your VibePoints: 150',
//                                 style: TextStyle(
//                                   color: Colors.white,
//                                   fontWeight: FontWeight.bold,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                       TextButton(
//                         onPressed: () {
//                           RouteUtils.pushNamed(
//                             context,
//                             RoutePaths.offerAnimationDemo,
//                           );
//                         },
//                         child: const Text(
//                           'Animation Demo',
//                           style: TextStyle(
//                             color: Colors.purpleAccent,
//                           ),
//                         ),
//                       ),
//                       Stack(
//                         alignment: Alignment.center,
//                         children: [
//                           IconButton(
//                             key: _cartKey,
//                             icon: const Icon(Icons.shopping_bag_rounded),
//                             color: Colors.white,
//                             onPressed: () {},
//                           ),
//                           if (_cartItemCount > 0)
//                             Positioned(
//                               right: 6,
//                               top: 6,
//                               child: Container(
//                                 padding: const EdgeInsets.all(2),
//                                 decoration: BoxDecoration(
//                                   color: darkAppColors.deepPurple,
//                                   borderRadius: BorderRadius.circular(10),
//                                 ),
//                                 constraints: const BoxConstraints(
//                                   minWidth: 16,
//                                   minHeight: 16,
//                                 ),
//                                 child: Text(
//                                   '$_cartItemCount',
//                                   style: const TextStyle(
//                                     color: Colors.white,
//                                     fontSize: 10,
//                                   ),
//                                   textAlign: TextAlign.center,
//                                 ),
//                               ),
//                             ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),
//                 Expanded(
//                   child: ListView.builder(
//                     padding: const EdgeInsets.only(top: 10),
//                     itemBuilder: (context, index) {
//                       return OfferTile(
//                         // canAddToCart: !offers[index].isRedeemed &&
//                         //     !offers[index].isLocked,
//                         canAddToCart: false,
//                         canRedeem: true,
//                         canNavigate: true,
//                         offer: offers[index],
//                         onAddToCart: (productWidget, productPosition) {
//                           _runAddToCartAnimation(
//                             context,
//                             productWidget,
//                             productPosition,
//                           );
//                         },
//                       );
//                     },
//                     itemCount: offers.length,
//                   ),
//                 ),
//               ],
//             ),
//           ), // Flying item animation
//           if (_isAnimating &&
//               _startPosition != null &&
//               _endPosition != null &&
//               _flyingWidget != null)
//             AnimatedBuilder(
//               animation: _controller,
//               builder: (context, child) {
//                 // Calculate current position with a curve
//                 final curvedValue =
//                     Curves.easeInOut.transform(_controller.value);

//                 // Add a slight arc to the path
//                 final double arcX = _startPosition!.dx +
//                     (_endPosition!.dx - _startPosition!.dx) * curvedValue;
//                 final double arcY = _startPosition!.dy +
//                     (_endPosition!.dy - _startPosition!.dy) * curvedValue -
//                     sin(curvedValue * 3.14) * 100; // Arc height

//                 // Scale down as it approaches the cart
//                 final scale = 1.0 - (curvedValue * 0.4);

//                 // Rotate slightly during flight
//                 final rotate = curvedValue * 0.5;

//                 return Positioned(
//                   left: arcX - 20 * scale, // Adjust based on your widget size
//                   top: arcY - 20 * scale, // Adjust based on your widget size
//                   child: Transform.scale(
//                     scale: scale,
//                     child: Transform.rotate(
//                       angle: rotate,
//                       child: Opacity(
//                         opacity: 1.0 - (curvedValue * 0.3),
//                         child: ClipRRect(
//                           borderRadius:
//                               const BorderRadius.all(Radius.circular(100)),
//                           child: SizedBox(
//                             width: 40,
//                             height: 40,
//                             child: _flyingWidget,
//                           ),
//                         ),
//                       ),
//                     ),
//                   ),
//                 );
//               },
//             ),
//         ],
//       ),
//     );
//   }
// }
