import 'package:firebase_analytics/firebase_analytics.dart';

class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Singleton instance
  static final AnalyticsService instance = AnalyticsService._();
  AnalyticsService._();

  Future<void> setUserId(String userId) async {
    await _analytics.setUserId(id: userId);
  }

  // 1. Registration Tracking
  Future<void> logUserRegistration({
    required String userId,
  }) async {
    await setUserId(userId);

    await _analytics.logEvent(
      name: 'user_registration',
      parameters: {
        'user_id': userId,
      },
    );
  }

  // 2. Homepage Engagement
  Future<void> logFirstTileOpen(String tileName) async {
    await _analytics.logEvent(
      name: 'first_tile_opened',
      parameters: {
        'tile_name': tileName,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  // 3. Venue Section
  Future<void> logVenueInteraction({
    required String venueId,
    required String userId,
  }) async {
    await _analytics.logEvent(
      name: 'venue_interaction',
      parameters: {
        'venue_id': venueId,
        'user_id': userId,
      },
    );
  }

  // 4. Search Page
  Future<void> logSearch({
    required String queryText,
    bool? isTag = false,
  }) async {
    await _analytics.logEvent(
      name: 'search_action',
      parameters: {
        'query_text': queryText,
        'search_type': (isTag ?? false).toString(),
      },
    );
  }

  // 6. Profile Section
  Future<void> logProfileMetrics({
    required String userId,
    required String metricType,
  }) async {
    await _analytics.logEvent(
      name: 'profile_metrics',
      parameters: {
        'user_id': userId,
        'metric_type': metricType, // vibes_watched, vibes_liked, saved_venues
      },
    );
  }

  // 7. Additional Homepage Metrics
  Future<void> logHomepageInteraction({
    required String interactionType,
  }) async {
    await _analytics.logEvent(
      name: 'homepage_interaction',
      parameters: {
        'interaction_type': interactionType, // music_genre, venue, live_video
      },
    );
  }

  Future<void> logBannerInteraction({
    required String interactionType,
  }) async {
    await _analytics.logEvent(
      name: 'banner_interaction',
      parameters: {
        'interaction_type': interactionType,
      },
    );
  }

  Future<void> logFilterInteraction({
    required String interactionType,
  }) async {
    await _analytics.logEvent(
      name: 'filter_interaction',
      parameters: {
        'interaction_type': interactionType,
      },
    );
  }

  Future<void> logHomepageTab({
    required String interactionType,
  }) async {
    await _analytics.logEvent(
      name: 'homepage_interaction',
      parameters: {
        'interaction_type': interactionType, // music_genre, venue, live_video
      },
    );
  }

  Future<void> logMusicTab({
    required String musicType,
  }) async {
    await _analytics.logEvent(
      name: 'music_tab_interaction',
      parameters: {
        'interaction_type': musicType,
      },
    );
  }

  Future<void> logBeoAITab({
    required String beoAIType,
  }) async {
    await _analytics.logEvent(
      name: 'beoAI_tab_interaction',
      parameters: {
        'interaction_type': beoAIType,
      },
    );
  }

  Future<void> logSpecialOfferHomeInteraction({
    required String? offerId,
  }) async {
    await _analytics.logEvent(
      name: 'special_offer_home_interaction',
      parameters: {
        'offer_id': offerId ?? 'more',
      },
    );
  }

  Future<void> logTonightHomeTab({
    required String tonightType,
  }) async {
    await _analytics.logEvent(
      name: 'tonight_tab_interaction',
      parameters: {
        'interaction_type': tonightType,
      },
    );
  }

  Future<void> logSpecialDaysTab({
    required String venueID,
  }) async {
    await _analytics.logEvent(
      name: 'special_days_tab_interaction',
      parameters: {
        'venueID': venueID,
      },
    );
  }

  Future<void> trackVideoView(String videoId) async {
    await _analytics.logEvent(
      name: 'video_view',
      parameters: {
        'video_id': videoId,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  Future<void> trackVideoShare(String videoId) async {
    await _analytics.logEvent(
      name: 'video_share',
      parameters: {
        'video_id': videoId,
      },
    );
  }

  Future<void> trackViewBasket(String venueID) async {
    await _analytics.logEvent(
      name: 'view_basket',
      parameters: {
        'venueID': venueID,
      },
    );
  }

  Future<void> trackOfferRedeemedClicked(String offerId) async {
    await _analytics.logEvent(
      name: 'offer_redeem_clicked',
      parameters: {
        'offer_id': offerId,
      },
    );
  }

  Future<void> trackClickedRedeemNowButton(String offerId) async {
    await _analytics.logEvent(
      name: 'redeem_now_button_clicked',
      parameters: {
        'offer_id': offerId,
      },
    );
  }

  Future<void> trackCancelRedemption(String offerId) async {
    await _analytics.logEvent(
      name: 'cancel_redemption',
      parameters: {
        'offer_id': offerId,
      },
    );
  }

  Future<void> trackOfferSaved(String offerId) async {
    await _analytics.logEvent(
      name: 'offer_saved',
      parameters: {
        'offer_id': offerId,
      },
    );
  }

  Future<void> trackSponsorTileClicked(String venueID) async {
    await _analytics.logEvent(
      name: 'highlight_tile_clicked',
      parameters: {
        'venue_id': venueID,
      },
    );
  }

  Future<void> trackDaySpecialTileClicked(String venueID) async {
    await _analytics.logEvent(
      name: 'day_special_tile_clicked',
      parameters: {
        'venue_id': venueID,
      },
    );
  }

  Future<void> trackPromoterDM(String venueId, String promoterId) async {
    await _analytics.logEvent(
      name: 'promoter_dm',
      parameters: {
        'venue_id': venueId,
        'promoter_id': promoterId,
      },
    );
  }

  Future<void> trackMoreButtonClick(String feedID) async {
    await _analytics.logEvent(
      name: 'video_more',
      parameters: {
        'feed_id': feedID,
      },
    );
  }

  Future<void> trackNavigationChoice(
    String feedID,
    String transportOption,
  ) async {
    await _analytics.logEvent(
      name: 'navigation_choice',
      parameters: {
        'feed_id': feedID,
        'transport_option':
            transportOption, // 'uber', 'apple_maps', 'google_maps', 'lyft'
      },
    );
  }

  void setUserType(String userType) {
    _analytics.setUserProperty(name: 'user_type', value: userType);
  }
}
