import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:vibeo/cubits/promotion/promotion_state.dart';
import 'package:vibeo/logic/feed/bloc/feed_repository.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';
import 'package:vibeo/models/models.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class PromotionCubit extends Cubit<PromotionState> {
  final VenueRepository _venueRepository;
  final FeedRepository _feedRepository;

  PromotionCubit({
    VenueRepository? venueRepository,
    FeedRepository? feedRepository,
  })  : _venueRepository = venueRepository ?? VenueRepository(),
        _feedRepository = feedRepository ?? FeedRepository(),
        super(PromotionInitial());
  void updateVenueFeeds(List<VenueModel> updatedVenues, int totalCount) {
    final currentState = state;
    if (currentState is PromotionLoaded) {
      emit(
        PromotionLoaded(
          highlightedVenues: updatedVenues,
          highlightedOffers: currentState.highlightedOffers,
          totalCount: totalCount,
        ),
      );
    } else {
      emit(
        PromotionLoaded(
          highlightedVenues: updatedVenues,
          totalCount: totalCount,
        ),
      );
    }
  }

  void updateHighlightedOffers(List<OfferModel> offers) {
    final currentState = state;
    if (currentState is PromotionLoaded) {
      emit(
        PromotionLoaded(
          highlightedVenues: currentState.highlightedVenues,
          highlightedOffers: offers,
          totalCount: currentState.totalCount,
        ),
      );
    }
  }

  void addOffersToHighlighted(List<OfferModel> newOffers) {
    final currentState = state;
    if (currentState is PromotionLoaded) {
      final currentOffers =
          List<OfferModel>.from(currentState.highlightedOffers);
      emit(
        PromotionLoaded(
          highlightedVenues: currentState.highlightedVenues,
          highlightedOffers: [...currentOffers, ...newOffers],
          totalCount: currentState.totalCount,
        ),
      );
    }
  }

  Future<void> fetchHighlightedVenues({int skip = 0, int limit = 10}) async {
    try {
      // Emit loading state
      emit(PromotionLoading());

      // Fetch highlighted venues from repository
      final result = await _venueRepository.fetchHighlightedVenues(
        skip: skip,
        limit: limit,
      );

      // Extract venues and total count
      final venues = result['venues'] as List<VenueModel>;
      final totalCount = result['totalCount'] as int;

      // Emit loaded state with venues
      // Preserve any existing offers when updating venues
      final currentState = state;
      final List<OfferModel> existingOffers = (currentState is PromotionLoaded)
          ? currentState.highlightedOffers
          : [];

      emit(
        PromotionLoaded(
          highlightedVenues: venues,
          highlightedOffers: existingOffers,
          totalCount: totalCount,
        ),
      );
    } catch (e) {
      AppLogger.error('Failed to fetch highlighted venues: $e');
      emit(const PromotionError('Failed to load highlighted venues'));
    }
  }

  // Optional: Method to load more venues for pagination
  Future<void> loadMoreHighlightedVenues({required int limit}) async {
    try {
      // Only proceed if we're in a loaded state
      if (state is PromotionLoaded) {
        final currentState = state as PromotionLoaded;
        final currentVenues = currentState.highlightedVenues;

        // Fetch more venues starting from current count
        final result = await _venueRepository.fetchHighlightedVenues(
          skip: currentVenues.length,
          limit: limit,
        );

        // Extract and combine with existing venues
        final newVenues = result['venues'] as List<VenueModel>;
        final totalCount = result['totalCount'] as int;

        emit(
          PromotionLoaded(
            highlightedVenues: [...currentVenues, ...newVenues],
            highlightedOffers: currentState.highlightedOffers,
            totalCount: totalCount,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Failed to load more highlighted venues: $e');
      // Keep existing state but don't emit error to maintain current data
    }
  }

  // Optional: Method to refresh the venues list
  Future<void> refreshHighlightedVenues({int limit = 10}) async {
    try {
      final result = await _venueRepository.fetchHighlightedVenues(
        skip: 0,
        limit: limit,
      );

      final venues = result['venues'] as List<VenueModel>;
      final totalCount = result['totalCount'] as int;

      // Preserve any existing offers when refreshing venues
      final currentState = state;
      final List<OfferModel> existingOffers = (currentState is PromotionLoaded)
          ? currentState.highlightedOffers
          : [];

      emit(
        PromotionLoaded(
          highlightedVenues: venues,
          highlightedOffers: existingOffers,
          totalCount: totalCount,
        ),
      );
    } catch (e) {
      AppLogger.error('Failed to refresh highlighted venues: $e');
      // If refresh fails, keep existing state
      if (state is! PromotionLoaded) {
        emit(const PromotionError('Failed to refresh highlighted venues'));
      }
    }
  }

  /// Fetch offers for a specific venue and store them in the state
  Future<List<OfferModel>> fetchOffersForVenue(
    String venueId,
    String userId,
    String userEmail,
  ) async {
    try {
      AppLogger.info('Fetching offers for venue: $venueId');

      // Check if we already have offers for this venue in the state
      if (state is PromotionLoaded) {
        final currentState = state as PromotionLoaded;
        final existingOffers = currentState.highlightedOffers;

        // Check if we already have offers for this venue
        final venueOffers =
            existingOffers.where((offer) => offer.venueID == venueId).toList();
        if (venueOffers.isNotEmpty) {
          AppLogger.info('Using cached offers for venue: $venueId');
          return venueOffers;
        }
      }

      // Fetch offers from the repository
      final offers = await _feedRepository.fetchPerks(
        venueId: venueId,
        userId: userId,
        userEmail: userEmail,
      );

      // Extract the offers list
      final offersList = offers['offers']['data'] as List<OfferModel>;

      // Add the offers to the state
      if (state is PromotionLoaded) {
        addOffersToHighlighted(offersList);
      }

      return offersList;
    } catch (e) {
      AppLogger.error('Failed to fetch offers for venue: $e');
      return [];
    }
  }
}
