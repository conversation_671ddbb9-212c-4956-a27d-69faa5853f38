import 'package:equatable/equatable.dart';
import 'package:vibeo/models/models.dart';

abstract class PromotionState extends Equatable {
  final List<VenueModel> highlightedVenues;
  final List<OfferModel> highlightedOffers;
  final int totalCount;
  const PromotionState({
    this.highlightedVenues = const [],
    this.highlightedOffers = const [],
    this.totalCount = 0,
  });

  @override
  List<Object?> get props => [];
}

class PromotionInitial extends PromotionState {}

class PromotionLoading extends PromotionState {}

class PromotionLoaded extends PromotionState {
  const PromotionLoaded({
    super.highlightedVenues,
    super.highlightedOffers,
    super.totalCount,
  });

  @override
  List<Object?> get props => [highlightedVenues, highlightedOffers, totalCount];
}

class PromotionError extends PromotionState {
  final String message;

  const PromotionError(this.message);

  @override
  List<Object?> get props => [message];
}
