import 'package:flutter_bloc/flutter_bloc.dart';

class FilterState {
  final String? selectedFilter;

  FilterState({this.selectedFilter});

  FilterState copyWith({String? selectedFilter}) {
    return FilterState(
      selectedFilter: selectedFilter,
    );
  }
}

class FilterCubit extends Cubit<FilterState> {
  FilterCubit() : super(FilterState());

  void toggleFilter(String filterTitle) {
    if (state.selectedFilter == filterTitle) {
      emit(FilterState(selectedFilter: null));
    } else {
      emit(FilterState(selectedFilter: filterTitle));
    }
  }

  void selectFilter(String filterTitle) {
    emit(FilterState(selectedFilter: filterTitle));
  }

  void deselectFilter() {
    emit(FilterState(selectedFilter: null));
  }

  void clearFilters() {
    emit(FilterState(selectedFilter: null));
  }

  bool isSelected(String filterTitle) {
    return state.selectedFilter == filterTitle;
  }
}
