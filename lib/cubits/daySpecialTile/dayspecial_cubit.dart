import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:vibeo/logic/venue/bloc/venue_repo_impl.dart';

import 'package:vibeo/models/models.dart';
import 'package:vibeo/utils/log/app_logger.dart';

part 'dayspecial_state.dart';

class DayspecialCubit extends Cubit<DayspecialState> {
  final VenueRepository _venueRepository;

  DayspecialCubit({VenueRepository? venueRepository})
      : _venueRepository = venueRepository ?? VenueRepository(),
        super(DaySpecialInitial());
  void updateVenueFeeds(List<VenueModel> updatedVenues, int totalCount) {
    emit(
      DayspecialLoaded(
        specialVenues: updatedVenues,
        totalCount: totalCount,
      ),
    );
  }

  Future<void> fetchSpecialVenues({int skip = 0, int limit = 10}) async {
    try {
      // Emit loading state
      emit(DayspecialLoading());

      // Fetch highlighted venues from repository
      final result = await _venueRepository.fetchSpecialVenues(
        dateTime: DateTime.now().toString(),
        skip: skip,
        limit: limit,
      );

      // Extract venues and total count
      final venues = result['venues'] as List<VenueModel>;
      final totalCount = result['totalCount'] as int;

      // Emit loaded state with venues
      emit(
        DayspecialLoaded(
          specialVenues: venues,
          totalCount: totalCount,
        ),
      );
    } catch (e) {
      AppLogger.error('Failed to fetch highlighted venues: $e');
      emit(const DayspecialError('Failed to load highlighted venues'));
    }
  }

  // Optional: Method to load more venues for pagination
  Future<void> loadMoreSpecialVenues({required int limit}) async {
    try {
      // Only proceed if we're in a loaded state
      if (state is DayspecialLoaded) {
        final currentState = state as DayspecialLoaded;
        final currentVenues = currentState.specialVenues;

        // Fetch more venues starting from current count
        final result = await _venueRepository.fetchSpecialVenues(
          dateTime: DateTime.now().toString(),
          skip: currentVenues.length,
          limit: limit,
        );

        // Extract and combine with existing venues
        final newVenues = result['venues'] as List<VenueModel>;
        final totalCount = result['totalCount'] as int;

        emit(
          DayspecialLoaded(
            specialVenues: [...currentVenues, ...newVenues],
            totalCount: totalCount,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Failed to load more highlighted venues: $e');
      // Keep existing state but don't emit error to maintain current data
    }
  }

  // Optional: Method to refresh the venues list
  Future<void> refreshSpecialVenues({int limit = 10}) async {
    try {
      final result = await _venueRepository.fetchSpecialVenues(
        dateTime: DateTime.now().toString(),
        skip: 0,
        limit: limit,
      );

      final venues = result['venues'] as List<VenueModel>;
      final totalCount = result['totalCount'] as int;

      emit(
        DayspecialLoaded(
          specialVenues: venues,
          totalCount: totalCount,
        ),
      );
    } catch (e) {
      AppLogger.error('Failed to refresh highlighted venues: $e');
      // If refresh fails, keep existing state
      if (state is! DayspecialLoaded) {
        emit(const DayspecialError('Failed to refresh highlighted venues'));
      }
    }
  }
}
