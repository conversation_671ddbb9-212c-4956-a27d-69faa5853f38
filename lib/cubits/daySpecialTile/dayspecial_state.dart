part of 'dayspecial_cubit.dart';

abstract class DayspecialState extends Equatable {
  final List<VenueModel> specialVenues;
  final int totalCount;
  const DayspecialState({
    this.specialVenues = const [],
    this.totalCount = 0,
  });

  @override
  List<Object?> get props => [];
}

class DaySpecialInitial extends DayspecialState {}

class DayspecialLoading extends DayspecialState {}

class DayspecialLoaded extends DayspecialState {
  const DayspecialLoaded({
    super.specialVenues,
    super.totalCount,
  });

  @override
  List<Object?> get props => [specialVenues, totalCount];
}

class DayspecialError extends DayspecialState {
  final String message;

  const DayspecialError(this.message);

  @override
  List<Object?> get props => [message];
}
