import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/config/config.dart';
import 'package:vibeo/cubits/daySpecialTile/dayspecial_cubit.dart';
import 'package:vibeo/cubits/filter/filter_cubit.dart';
import 'package:vibeo/cubits/promotion/promotion_cubit.dart';
import 'package:vibeo/firebase_options.dart';

import 'package:vibeo/logic/app/app.bloc.oberver.dart';
import 'package:vibeo/logic/app/bloc/app_lifecycle_bloc.dart';
import 'package:vibeo/logic/artists/bloc/artists_bloc.dart';
import 'package:vibeo/logic/auth/bloc/auth_bloc.dart';
import 'package:vibeo/logic/cart/bloc/cart_bloc.dart';

import 'package:vibeo/logic/content/bloc/content_bloc.dart';
import 'package:vibeo/logic/cubit/perks_cubit_cubit.dart';

import 'package:vibeo/logic/exception/exception_bloc.dart';
import 'package:vibeo/logic/feed/bloc/feed_bloc.dart';
import 'package:vibeo/logic/preload/bloc/preload_bloc.dart';

import 'package:vibeo/logic/registration/registration_cubit.dart';
import 'package:vibeo/logic/search/search_bloc.dart';
import 'package:vibeo/logic/user/bloc/user_bloc.dart';
import 'package:vibeo/logic/venue/bloc/venue_bloc.dart';
import 'package:vibeo/notifications/onesignal_handler.dart';
import 'package:vibeo/routes/route.dart';
import 'package:vibeo/logic/auth/bloc/auth_repo_impl.dart';
import 'package:vibeo/themes/app_theme.dart';

import 'package:vibeo/utils/global_exception_handler.dart';
import 'package:vibeo/utils/log/app_logger.dart';
import 'package:vibeo/utils/size_utils.dart';

import 'package:onesignal_flutter/onesignal_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Config.initialize();

  FlutterError.onError = FlutterError.dumpErrorToConsole;
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    await FirebaseAnalytics.instance
        .setAnalyticsCollectionEnabled(kReleaseMode);
  } catch (err) {
    AppLogger.error(err.toString());
  }

  try {
    OneSignal.initialize(Config.oneSignalAppID);

    if (await OneSignal.Notifications.canRequest()) {
      await OneSignal.Notifications.requestPermission(true);
    }
  } catch (e) {
    AppLogger.error(e.toString());
  }

  await SystemChrome.setPreferredOrientations(
    [
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ],
  );

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.black,
      statusBarBrightness: Brightness.dark,
    ),
  );

  // final rateMyApp = RateMyApp(
  //   preferencesPrefix: 'vibeo',
  //   minDays: 0,
  //   minLaunches: 0,
  //   remindDays: 0,
  //   remindLaunches: 0,
  //   appStoreIdentifier: Config.appStoreID,
  // );

  // await rateMyApp.init();
  SentryWidgetsFlutterBinding.ensureInitialized();
  await SentryFlutter.init(
    (options) {
      options
        ..dsn = Config.sentryAppID
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        ..tracesSampleRate = 0.01
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        ..profilesSampleRate = 1.0;
    },
    appRunner: () => runApp(
      // RateMyAppBuilder(
      //   rateMyApp: rateMyApp,
      //   onInitialized: (context, rateMyApp) {
      //     for (final condition in rateMyApp.conditions) {
      //       if (condition is MinimumDaysCondition) {
      //         AppLogger.debug('Days installed: $condition');
      //       } else if (condition is MinimumAppLaunchesCondition) {
      //         AppLogger.debug('App launches: $condition');
      //       }
      //     }
      //     WidgetsBinding.instance.addPostFrameCallback((_) {
      //       // AppRatingManager.showRatingDialog(context, rateMyApp);
      //       if (rateMyApp.shouldOpenDialog) {
      //         rateMyApp.showStarRateDialog(context);
      //       }
      //     });
      //   },
      //   builder: (context) =>
      DefaultAssetBundle(
        bundle: SentryAssetBundle(
          enableStructuredDataTracing: true,
        ),
        child: Builder(
          builder: (context) {
            Bloc.observer = AppBlocObserver(context);
            return const MyApp();
          },
        ),
      ),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  static final AppRouteObserver routeObserver = AppRouteObserver();
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        // BlocProvider<InternetBloc>(
        //   create: (context) => InternetBloc()..add(CheckInternetConnection()),
        // ),
        BlocProvider(
          create: (context) => GlobalErrorBloc(),
        ),
        BlocProvider(
          create: (context) => AppLifecycleBloc(),
        ),
        BlocProvider(
          create: (context) => AuthBloc(
            authRepository: AuthRepository(),
          ),
        ),
        BlocProvider(
          create: (context) => RegistrationCubit(),
        ),
        BlocProvider<UserBloc>(
          create: (context) => UserBloc(),
        ),
        BlocProvider<FeedBloc>(
          create: (context) => FeedBloc(),
        ),
        BlocProvider<VenueBloc>(
          create: (context) => VenueBloc(),
        ),
        BlocProvider<SearchBloc>(
          create: (context) => SearchBloc(),
        ),
        BlocProvider<ContentBloc>(
          create: (context) => ContentBloc(),
        ),

        // BlocProvider<OfferBloc>(
        //   create: (context) => OfferBloc(),
        // ),
        // BlocProvider<PromoterBloc>(
        //   create: (context) => PromoterBloc(),
        // ),
        // BlocProvider<EventBloc>(
        //   create: (context) => EventBloc(),
        // ),
        BlocProvider(
          create: (context) => ArtistBloc(),
        ),
        BlocProvider(
          create: (context) => PerksCubit(),
        ),
        BlocProvider(
          create: (context) => FilterCubit(),
        ),
        BlocProvider(
          create: (context) => PromotionCubit(),
        ),
        BlocProvider(
          create: (context) => DayspecialCubit(),
        ),
        BlocProvider(
          create: (context) => PreloadBloc(),
        ),
        BlocProvider(
          create: (context) => CartBloc(),
        ),
      ],
      child: MaterialApp(
        navigatorKey: MyApp.navigatorKey,
        debugShowCheckedModeBanner: false,
        title: 'Vibeo',
        themeMode: ThemeMode.dark,
        darkTheme: AppTheme.darkTheme,
        initialRoute: RoutePaths.wrapper,
        onGenerateRoute: AppRouter.generateRoute,
        navigatorObservers: [MyApp.routeObserver],
        builder: (context, child) {
          SizeUtils.init(context);

          return NotificationHandlerWidget(
            navigatorKey: MyApp.navigatorKey,
            child: ScrollConfiguration(
              behavior: const ScrollBehavior().copyWith(
                physics: const BouncingScrollPhysics(),
                platform: TargetPlatform.iOS,
              ),
              child: GlobalExceptionHandler(
                child: child!,
              ),
            ),
          );
        },
      ),
    );
  }
}
