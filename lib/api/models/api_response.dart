import 'package:vibeo/utils/log/app_logger.dart';

class ApiResponse<T> {
  final bool status;
  final int statusCode;
  final String message;
  final T? data;
  final String timestamp;
  final String path;
  final String method;
  final String version;
  final ApiError? error;

  ApiResponse({
    required this.status,
    required this.statusCode,
    required this.message,
    required this.timestamp,
    required this.path,
    required this.method,
    required this.version,
    this.data,
    this.error,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    AppLogger.info('Parsing JSON: $json');

    try {
      final dynamic rawData = json['data'];
      T? parsedData;

      if (rawData != null) {
        if (rawData is Map<String, dynamic>) {
          parsedData = fromJson(rawData);
        } else if (rawData is bool || rawData is String || rawData is num) {
          parsedData = rawData as T;
        }
      }

      return ApiResponse(
        status: json['status'] as bool? ?? false,
        statusCode: json['statusCode'] as int? ?? 500,
        message: json['message']?.toString() ?? 'Unknown message',
        data: parsedData,
        timestamp: json['timestamp']?.toString() ?? '',
        path: json['path']?.toString() ?? '',
        method: json['method']?.toString() ?? '',
        version: json['version']?.toString() ?? '',
        error: json['error'] != null
            ? ApiError.fromJson(json['error'] as Map<String, dynamic>)
            : null,
      );
    } catch (e, stackTrace) {
      AppLogger.error('Error parsing response: $e\n$stackTrace');
      rethrow;
    }
  }
}

class ApiError {
  final String name;
  final dynamic details; // Change type to dynamic to handle both String and Map

  ApiError({
    required this.name,
    required this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      name: json['name']?.toString() ?? '',
      details: json['details'] ?? '',
    );
  }

  String get detailsAsString {
    if (details is String) {
      return details as String;
    } else if (details is Map) {
      return (details as Map)['message']?.toString() ?? '';
    }
    return details?.toString() ?? '';
  }
}
