abstract class BaseApiServices {
  Future<T> getAPI<T>(
    String url,
    T Function(Map<String, dynamic>) fromJson,
  );
  Future<T> postAPI<T>(
    String url,
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  );
  Future<T> putAPI<T>(
    String url,
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  );
  Future<T> deleteAPI<T>(
    String url,
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  );
}
