import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/config/config.dart';
import 'package:vibeo/logic/registration/save_user_info.dart';
import 'package:vibeo/api/models/api_response.dart';
import 'package:vibeo/utils/exceptions/app_exceptions.dart';
import 'package:vibeo/api/network/base_api_services.dart';
import 'package:vibeo/utils/log/app_logger.dart';

class NetworkServicesApi implements BaseApiServices {
  late final Dio _dio;
  final Duration timeoutDuration = const Duration(seconds: 30);

  NetworkServicesApi() {
    _dio = _createDio();
  }

  Dio _createDio() {
    return Dio(
      BaseOptions(
        baseUrl: Config.localURL,
        connectTimeout: timeoutDuration,
        receiveTimeout: timeoutDuration,
        responseType: ResponseType.json,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      ),
    )..interceptors.addAll([
        _LoggerInterceptor(),
        _AuthInterceptor(),
      ]);
  }

  @override
  Future<T> getAPI<T>(
    String url,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    try {
      final response = await _dio.get(url);
      AppLogger.info('Response data: ${response.data}');

      // Special handling for voucher API
      if (url.contains('testvouchersticketing') &&
          url.contains('vouchers/byVenueWithStatus')) {
        return _handleVoucherResponse<T>(response, fromJson);
      }

      return _handleResponse(response, fromJson);
    } on DioException catch (e) {
      if (e.response!.statusCode != 404) {
        await Sentry.captureException(e);
      }
      throw _handleDioError(e) as AppException;
    } catch (e) {
      await Sentry.captureException(e);
      throw BusinessLogicException(message: 'An unexpected error occurred: $e');
    }
  }

  // Special handler for voucher API that returns a List directly
  T _handleVoucherResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      final responseData = response.data;
      AppLogger.info('Voucher response data: $responseData');

      // If the response is a List, return it directly for List<dynamic> type
      if (responseData['data'] is List) {
        return responseData as T;
      }

      // If we can't handle the response, throw an exception
      throw BusinessLogicException(
        message:
            'Unexpected response format for voucher API: ${responseData.runtimeType}',
      );
    } catch (e, stackTrace) {
      AppLogger.error('Error handling voucher response: $e\n$stackTrace');
      rethrow;
    }
  }

  @override
  Future<T> postAPI<T>(
    String url,
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    try {
      final response = await _dio.post(url, data: data);
      AppLogger.info('Response data: ${response.data}');

      // Special handling for voucher API
      if (url.contains('testvouchersticketing') &&
          url.contains('vouchers/byUniqueCodeWithStatus')) {
        return _handleVoucherResponse<T>(response, fromJson);
      }
      // Special handling for the process-video endpoint
      if (url.contains('process-video')) {
        return _handleProcessVideoResponse<T>(response, fromJson);
      }

      return _handleResponse(response, fromJson);
    } on DioException catch (e) {
      if (e.response!.statusCode != 404) {
        await Sentry.captureException(e);
      }
      throw _handleDioError(e) as AppException;
    } catch (e) {
      await Sentry.captureException(e);
      throw BusinessLogicException(message: 'An unexpected error occurred: $e');
    }
  }

  // Special handler for process-video endpoint that can return List or Map
  T _handleProcessVideoResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      final responseData = response.data;

      // If the response is a List, extract the first item
      if (responseData is List && responseData.isNotEmpty) {
        final firstItem = responseData[0];

        // If the first item is a Map, use it directly
        if (firstItem is Map<String, dynamic>) {
          // If T is dynamic, return the whole map
          if (T == dynamic) {
            return firstItem as T;
          }

          // If T is Map<String, dynamic>, return the map
          if (T.toString() == 'Map<String, dynamic>') {
            return firstItem as T;
          }

          // If T is bool, check for data field
          if (T == bool) {
            if (firstItem.containsKey('data') && firstItem['data'] is bool) {
              return (firstItem['data'] as bool) as T;
            }
            // If there's a result field, consider it a success
            if (firstItem.containsKey('result') &&
                firstItem['result'] != null) {
              return true as T;
            }
            return false as T;
          }

          // Otherwise, try to use the fromJson function
          return fromJson(firstItem);
        }
      }

      // If the response is a Map, use it directly
      if (responseData is Map<String, dynamic>) {
        // If T is dynamic or Map<String, dynamic>, return the whole map
        if (T == dynamic || T.toString() == 'Map<String, dynamic>') {
          return responseData as T;
        }

        // If T is bool, check for data field
        if (T == bool) {
          if (responseData.containsKey('data') &&
              responseData['data'] is bool) {
            return (responseData['data'] as bool) as T;
          }
          // If there's a result field, consider it a success
          if (responseData.containsKey('result') &&
              responseData['result'] != null) {
            return true as T;
          }
          return false as T;
        }

        // Otherwise, try to use the fromJson function
        return fromJson(responseData);
      }

      // If we can't handle the response, throw an exception
      throw BusinessLogicException(
        message: 'Unexpected response format: ${responseData.runtimeType}',
      );
    } catch (e, stackTrace) {
      AppLogger.error('Error handling process-video response: $e\n$stackTrace');
      if (T == bool) {
        return false as T;
      }
      rethrow;
    }
  }

  @override
  Future<T> putAPI<T>(
    String url,
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    try {
      final response = await _dio.put(url, data: data);
      return _handleResponse(response, fromJson);
    } on DioException catch (e) {
      if (e.response!.statusCode != 404) {
        await Sentry.captureException(e);
      }
      throw _handleDioError(e) as AppException;
    } catch (e) {
      await Sentry.captureException(e);
      throw BusinessLogicException(message: 'An unexpected error occurred: $e');
    }
  }

  @override
  Future<T> deleteAPI<T>(
    String url,
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    try {
      final response = await _dio.delete(url, data: data);
      return _handleResponse(response, fromJson);
    } on DioException catch (e) {
      await Sentry.captureException(e);
      throw _handleDioError(e) as AppException;
    } catch (e) {
      await Sentry.captureException(e);
      throw BusinessLogicException(message: 'An unexpected error occurred: $e');
    }
  }

  T _handleResponse<T>(
    Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    final apiResponse = ApiResponse.fromJson(
      response.data as Map<String, dynamic>,
      (data) {
        // Add logging to debug the data structure
        AppLogger.info('Data being parsed: $data');
        return fromJson(data);
      },
    );

    if (!apiResponse.status) {
      throw _handleErrorResponse(apiResponse);
    }

    if (apiResponse.data == null) {
      throw BusinessLogicException(message: 'No data received from server');
    }

    return apiResponse.data as T;
  }

  AppException _handleErrorResponse(ApiResponse response) {
    switch (response.statusCode) {
      case 400:
        throw BadRequestException(
          message: _getErrorMessage(response.data),
        );
      case 401:
        throw UnauthorizedException(
          message: _getErrorMessage(response.data),
        );
      case 403:
        throw PermissionDeniedAppException(
          message: _getErrorMessage(response.data),
        );
      case 404:
        throw NotFoundException(
          message: _getErrorMessage(response.data),
        );
      case 408:
        throw TimeoutException(
          message: _getErrorMessage(response.data),
        );
      case 409:
        throw ConflictException(
          message: _getErrorMessage(response.data),
        );
      case 429:
        throw TooManyRequestsException(
          message: _getErrorMessage(response.data),
        );
      case 500:
        throw ServerException(
          message: _getErrorMessage(response.data),
        );
      case 503:
        throw ServiceUnavailableException(
          message: _getErrorMessage(response.data),
        );
      default:
        throw BusinessLogicException(
          message: 'Received invalid status code: ${response.statusCode}',
        );
    }
  }

  dynamic _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return TimeoutException(
          message: 'Connection timed out. Please try again.',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.sendTimeout:
        return TimeoutException(
          message: 'Send timeout. Please try again.',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.receiveTimeout:
        return TimeoutException(
          message: 'Receive timeout. Please try again.',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.badResponse:
        if (error.response != null) {
          return _handleResponse(error.response!, (_) => null);
        }
        return ServerException(
          message: 'Server error occurred',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.cancel:
        return NetworkException(
          message: 'Request cancelled',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.connectionError:
        return NetworkException(
          message: error.message ?? 'Connection error occurred',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.badCertificate:
        return NetworkException(
          message: 'Invalid certificate',
          stackTrace: error.stackTrace.toString(),
        );

      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          return NetworkException(
            message: 'No internet connection',
            stackTrace: error.stackTrace.toString(),
          );
        }
        return BusinessLogicException(
          message: error.message ?? 'An unexpected error occurred',
          stackTrace: error.stackTrace.toString(),
        );
    }
  }

  String _getErrorMessage(dynamic error) {
    if (error is Map) {
      return (error['message'] ?? error['error'] ?? 'Unknown error occurred')
          .toString();
    }
    return error?.toString() ?? 'Unknown error occurred';
  }
}

// Logger Interceptor
class _LoggerInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    AppLogger.info('REQUEST[${options.method}] => PATH: ${options.path}');
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    AppLogger.info(
      'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}',
    );
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppLogger.error(
      'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}',
    );
    super.onError(err, handler);
  }
}

// Auth Interceptor
class _AuthInterceptor extends Interceptor {
  @override
  Future<void> onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      final token = await getToken();
      final backup = Config.backup;
      options.headers['Authorization'] = 'Bearer ${token ?? backup}';
      handler.next(options);
    } catch (error) {
      handler.reject(
        DioException(
          requestOptions: options,
          error: 'Failed to get token',
        ),
      );
    }
  }

  Future<String?> getToken() async {
    final token = await SecureStorage().getTokens();
    return token?.accessToken;
  }
}
