import 'dart:math';

import 'package:flutter/material.dart';

class GradientAnimationText extends StatefulWidget {
  final String text;
  final bool isSelected;

  const GradientAnimationText({
    required this.text,
    this.isSelected = false,
    super.key,
  });

  @override
  State<GradientAnimationText> createState() => _GradientAnimationTextState();
}

class _GradientAnimationTextState extends State<GradientAnimationText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              colors: const [
                Color.fromARGB(255, 151, 135, 255),
                Color.fromARGB(255, 151, 109, 215),
                Color.fromARGB(255, 237, 123, 255),
              ],
              stops: const [0.0, 0.5, 1.0],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              transform: GradientRotation(_controller.value * 2 * pi),
            ).createShader(bounds);
          },
          child: Text(
            widget.text,
            style: TextStyle(
              fontSize: 16,
              fontWeight:
                  widget.isSelected ? FontWeight.bold : FontWeight.normal,
              color: Colors.white,
            ),
          ),
        );
      },
    );
  }
}
