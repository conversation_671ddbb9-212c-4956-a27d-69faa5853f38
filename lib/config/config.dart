import 'package:flutter_dotenv/flutter_dotenv.dart' as dotenv;

class Config {
  static String get shazamApiKey => _get('SHAZAM_API_KEY');
  static String get googleMapApiKey => _get('GOOGLE_MAP_API_KEY');
  static String get cloudRunURL => _get('CLOUD_RUN_URL');
  static String get devCloudRun => _get('DEV_CLOUD_RUN_URL');
  static String get localURL => _get('LOCAL_URL');
  static String get emulatorURL => _get('EMULATOR_URL');
  static String get backup => _get('BACKUP');
  static String get oneSignalAppID => _get('ONESIGNAL_APP_ID');
  static String get oneSignalAppTestID => _get('ONESIGNAL_APP_TEST_ID');
  static String get sentryAppID => _get('SENTRY_APP_ID');
  static String get appStoreID => _get('APP_STORE_ID');
  static String get voucherApiKey => _get('VOUCHER_API_KEY');

  static String _get(String name) => dotenv.dotenv.env[name] ?? '';

  static Future<void> initialize() async {
    await dotenv.dotenv.load(fileName: _getEnvFileName());
  }

  static String _getEnvFileName() {
    if (const bool.fromEnvironment('dart.vm.product')) {
      return '.prod.env';
    }
    return '.dev.env';
  }
}
