import 'dart:io';
import 'dart:typed_data';

import 'package:vibeo/utils/utils.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

Future<List<Uint8List>?> getSegmentThumbnails(String videoPath) async {
  try {
    final videoFile = File(videoPath);
    final video = VideoPlayerController.file(videoFile);
    await video.initialize();

    final duration = video.value.duration.inMilliseconds;
    final segmentDuration = duration ~/ 3;

    final firstSegmentTime = segmentDuration ~/ 2;
    final secondSegmentTime = segmentDuration + (segmentDuration ~/ 2);
    final thirdSegmentTime = (segmentDuration * 2) + (segmentDuration ~/ 2);

    final List<Uint8List> thumbnails = [];

    for (final timeMs in [
      firstSegmentTime,
      secondSegmentTime,
      thirdSegmentTime,
    ]) {
      final thumbnail = await VideoThumbnail.thumbnailData(
        video: videoPath,
        imageFormat: ImageFormat.PNG,
        timeMs: timeMs,
        quality: 20,
      );

      if (thumbnail != null) {
        thumbnails.add(thumbnail);
      }
    }

    // Cleanup
    await video.dispose();
    if (await videoFile.exists()) {
      await videoFile.delete();
    }

    return thumbnails;
  } catch (e) {
    AppLogger.error(e.toString());
    return null;
  }
}
