import 'package:flutter/services.dart';
import 'package:gcloud/storage.dart';
import 'package:googleapis_auth/auth_io.dart' as auth;
import 'package:mime/mime.dart';

class CloudApi {
  static CloudApi? _instance;
  auth.ServiceAccountCredentials? _credentials;
  bool _isInitialized = false;

  // Private constructor
  CloudApi._();

  // Factory constructor for singleton
  static Future<CloudApi> getInstance() async {
    _instance ??= CloudApi._();

    if (!_instance!._isInitialized) {
      await _instance!._initialize();
    }

    return _instance!;
  }

  // Initialize credentials
  Future<void> _initialize() async {
    try {
      final credentialsJson =
          await rootBundle.loadString('assets/credentials.json');
      _credentials = auth.ServiceAccountCredentials.fromJson(credentialsJson);
      _isInitialized = true;
    } catch (e) {
      throw Exception('Failed to initialize CloudApi: $e');
    }
  }

  Future<ObjectInfo> save(String name, Uint8List imgBytes) async {
    if (!_isInitialized || _credentials == null) {
      throw Exception('CloudApi not initialized');
    }

    final client =
        await auth.clientViaServiceAccount(_credentials!, Storage.SCOPES);
    final storage = Storage(client, 'Vibeo');
    final bucket = storage.bucket('vibeofeeds');
    final path = 'videos/$name';

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final type = lookupMimeType(name);

    return bucket.writeBytes(
      path,
      imgBytes,
      metadata: ObjectMetadata(
        contentType: type,
        custom: {
          'timestamp': '$timestamp',
        },
      ),
    );
  }

  Future<ObjectInfo> saveThumbnail(String name, Uint8List imgBytes) async {
    if (!_isInitialized || _credentials == null) {
      throw Exception('CloudApi not initialized');
    }

    final client =
        await auth.clientViaServiceAccount(_credentials!, Storage.SCOPES);
    final storage = Storage(client, 'Vibeo');
    final bucket = storage.bucket('vibeothumbnails');

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final type = lookupMimeType(name);

    return bucket.writeBytes(
      name,
      imgBytes,
      metadata: ObjectMetadata(
        contentType: type,
        custom: {
          'timestamp': '$timestamp',
        },
      ),
    );
  }
}
