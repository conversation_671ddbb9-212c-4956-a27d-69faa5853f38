import 'dart:io';
import 'package:dio/dio.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:vibeo/services/feed/gcp_upload.dart';
import 'package:vibeo/utils/utils.dart';

class VideoUploader {
  late final CloudApi _cloudApi;
  final Dio _dio;

  VideoUploader() : _dio = Dio() {
    _dio.options.headers = {
      'content-type': 'text/plain',
      'X-RapidAPI-Host': 'shazam.p.rapidapi.com',
    };
    _initializeCloudApi();
  }

  Future<void> _initializeCloudApi() async {
    _cloudApi = await CloudApi.getInstance();
  }

  Future<Map<String, String>?> uploadVideo(
    String feedID,
    String? videoPath,
    void Function(double) onProgress,
  ) async {
    try {
      if (videoPath == null) {
        throw Exception('Video compression failed');
      }

      onProgress(0.5);
      final videoFileName = '$feedID.mp4';
      final videoFile = File(videoPath);
      final videoBytes = await videoFile.readAsBytes();

      final videoInfo = await _cloudApi.save(
        videoFileName,
        videoBytes,
      );
      onProgress(1);

      await _deleteFile(videoPath);
      return {
        'videoURL': videoInfo.downloadLink.toString(),
      };
    } catch (e, stackTrace) {
      AppLogger.error('Failed to upload video: $e\n$stackTrace');
      await Sentry.captureException(e);
      return null;
    }
  }

  Future<void> _deleteFile(String filePath) async {
    final file = File(filePath);
    final doesExist = await file.exists();
    if (doesExist) {
      await file.delete();
    }
  }
}
