import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:vibeo/utils/utils.dart';

class ReferralManager {
  static const String baseUrl = 'https://your-api-endpoint.com';

  // Process referral after successful registration
  Future<bool> processReferral(String newUserId, String referrerUserId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/referrals/process'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'new_user_id': newUserId,
          'referrer_user_id': referrerUserId,
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['success'] as bool? ?? false;
      }
      return false;
    } catch (e) {
      AppLogger.error('Error processing referral: $e');
      return false;
    }
  }

  // Get user's referral stats
  Future<Map<String, dynamic>> getReferralStats(String userId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/referrals/stats/$userId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body as String? ?? '')
            as Map<String, dynamic>;
      }
      return {};
    } catch (e) {
      AppLogger.error('Error getting referral stats: $e');
      return {};
    }
  }

  // Award referral bonus
  Future<bool> awardReferralBonus(String userId, double bonusAmount) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/referrals/award-bonus'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_id': userId,
          'bonus_amount': bonusAmount,
          'bonus_type': 'referral_reward',
          'timestamp': DateTime.now().toIso8601String(),
        }),
      );

      return response.statusCode == 200;
    } catch (e) {
      AppLogger.error('Error awarding bonus: $e');
      return false;
    }
  }
}
