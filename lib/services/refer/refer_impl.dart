import 'package:flutter_branch_sdk/flutter_branch_sdk.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BranchService {
  static final BranchService _instance = BranchService._internal();
  factory BranchService() => _instance;
  BranchService._internal();

  // Initialize Branch
  Future<void> initBranch() async {
    await FlutterBranchSdk.init();

    // Listen for deep link data
    FlutterBranchSdk.listSession().listen((data) {
      if (data.containsKey('+clicked_branch_link') &&
          data['+clicked_branch_link'] == true) {
        _handleReferralLink(data);
      }
    });
  }

  // Handle incoming referral links
  Future<void> _handleReferralLink(Map<dynamic, dynamic> data) async {
    final referrerUserId = data['referrer_user_id'];
    final referralCode = data['referral_code'];

    if (referrerUserId != null) {
      // Store referral info locally
      await _storeReferralInfo(
          referrerUserId.toString(), referralCode.toString());
    }
  }

  // Store referral information
  Future<void> _storeReferralInfo(
    String referrerUserId,
    String? referralCode,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('referrer_user_id', referrerUserId);
    if (referralCode != null) {
      await prefs.setString('referral_code', referralCode);
    }
  }

  // Get stored referral info
  Future<Map<String, String?>> getReferralInfo() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'referrer_user_id': prefs.getString('referrer_user_id'),
      'referral_code': prefs.getString('referral_code'),
    };
  }

  // Clear referral info after processing
  Future<void> clearReferralInfo() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('referrer_user_id');
    await prefs.remove('referral_code');
  }

  // Generate referral link
  Future<String> generateReferralLink(String userId, String userName) async {
    final buo = BranchUniversalObject(
      canonicalIdentifier: 'referral_$userId',
      title: '$userName invited you to join our app!',
      contentDescription: 'Join our amazing app and get exclusive benefits',
      contentMetadata: BranchContentMetaData()
        ..addCustomMetadata('referrer_user_id', userId)
        ..addCustomMetadata('referral_code', 'REF_$userId')
        ..addCustomMetadata('referrer_name', userName),
    );

    final lp = BranchLinkProperties(
      channel: 'referral',
      feature: 'sharing',
      campaign: 'user_referral',
    );

    final BranchResponse response = await FlutterBranchSdk.getShortUrl(
      buo: buo,
      linkProperties: lp,
    );

    if (response.success) {
      return response.result.toString();
    } else {
      throw Exception(
        'Failed to generate referral link: ${response.errorMessage}',
      );
    }
  }
}
