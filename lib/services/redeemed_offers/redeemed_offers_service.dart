import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:vibeo/models/offer/offer_model.dart';
import 'package:vibeo/models/offer/redeemed_offer_data.dart';
import 'package:vibeo/models/utils/parse_utils.dart';
import 'package:vibeo/utils/log/app_logger.dart';

/// Service for managing redeemed offers
class RedeemedOffersService {
  static const _redeemedOffersKey = 'redeemed_offers';
  final _storage = const FlutterSecureStorage();
  final String userID;

  RedeemedOffersService(this.userID);

  Future<String> _getUserSpecificKey() async {
    return '${_redeemedOffersKey}_$userID';
  }

  /// Save a redeemed offer with additional redemption details
  Future<void> saveRedeemedOffer(
    OfferModel offer,
    String venueID,
    String redemptionCode, {
    // VIBEPOINTS FEATURE COMMENTED OUT
    int? vibePointsEarned,
  }) async {
    try {
      final key = await _getUserSpecificKey();
      final redeemed = await getRedeemedOffers();

      // Check if this offer is already redeemed
      final existingIndex = redeemed.indexWhere(
        (item) => item['offerId'] == offer.id && item['venueId'] == venueID,
      );

      if (existingIndex >= 0) {
        // Update existing redemption
        redeemed[existingIndex] = _createRedemptionRecord(
          offer,
          venueID,
          redemptionCode,
          vibePointsEarned: vibePointsEarned,
        );
      } else {
        // Add new redemption
        redeemed.add(
          _createRedemptionRecord(
            offer,
            venueID,
            redemptionCode,
            vibePointsEarned: vibePointsEarned,
          ),
        );
      }

      // Save to storage
      await _storage.write(
        key: key,
        value: jsonEncode(redeemed),
      );
    } catch (e) {
      AppLogger.error('Error saving redeemed offer: $e');
    }
  }

  /// Calculate vibe points earned based on offer category and type
  // VIBEPOINTS FEATURE COMMENTED OUT
  /*
  int _calculateVibePointsEarned(OfferModel offer) {
    // Determine points based on offer category and type according to the points system
    switch (offer.voucherCategory) {
      case VoucherCategory.ONGOING:
        // Base Campaign - Everyday Perk
        return 75;
      case VoucherCategory.TIMEBASED:
        // Time Boost - Limited Hour Deal
        return 100;
      case VoucherCategory.DROPS:
        // Flash/Mystery Drop
        return 150;
      default:
        // Check if it's a platform unlockable (costs vibe points)
        if (offer.redemptionType == OfferRedemptionType.vibePointsRedeem) {
          // Platform Unlockable - Vibeo Exclusive (costs points)
          return -offer.vibePointsCost; // Negative because user spends points
        }

        // Default to base campaign value if no specific category matches
        return 75;
    }
  }
  */

  /// Create a redemption record with all necessary details
  Map<String, dynamic> _createRedemptionRecord(
    OfferModel offer,
    String venueID,
    String redemptionCode, {
    // VIBEPOINTS FEATURE COMMENTED OUT
    int? vibePointsEarned,
  }) {
    // VIBEPOINTS FEATURE COMMENTED OUT
    // Calculate vibe points earned based on offer type and value if not provided
    // final int points = vibePointsEarned ?? _calculateVibePointsEarned(offer);
    const int points = 0; // Default to 0 since vibepoints feature is disabled

    return {
      'offerId': offer.id,
      'venueId': venueID,
      'redemptionCode': redemptionCode,
      'redemptionDate': DateTime.now().toIso8601String(),
      // VIBEPOINTS FEATURE COMMENTED OUT
      'vibePointsEarned': points,
      // Store essential offer details for display
      'offerDetails': {
        'title': offer.title,
        'description': offer.description,
        'venueName': offer.venueName,
        'imageLink': offer.imageLink,
        'info': offer.info,
        'offerValue': offer.offerValue,
        'discountedValue': offer.discountedValue,
        'redemptionType': offer.redemptionType.index,
        // VIBEPOINTS FEATURE COMMENTED OUT
        'vibePointsEarned': points,
      },
    };
  }

  /// Get all redeemed offers
  Future<List<Map<String, dynamic>>> getRedeemedOffers() async {
    try {
      final key = await _getUserSpecificKey();
      final redeemedStr = await _storage.read(key: key);

      if (redeemedStr != null) {
        final List<dynamic> decoded = jsonDecode(redeemedStr) as List<dynamic>;
        return List<Map<String, dynamic>>.from(decoded);
      }
    } catch (e) {
      AppLogger.error('Error getting redeemed offers: $e');
    }

    return [];
  }

  /// Convert stored redemption records to OfferModel objects
  Future<List<OfferModel>> getRedeemedOffersAsModels() async {
    final records = await getRedeemedOffers();
    final offers = <OfferModel>[];

    for (final record in records) {
      try {
        final offerDetails = record['offerDetails'] as Map<String, dynamic>;

        // Create the offer model
        final offer = OfferModel(
          id: record['offerId'] as String,
          venueID: record['venueId'] as String,
          title: offerDetails['title'] as String,
          description: offerDetails['description'] as String,
          venueName: offerDetails['venueName'] as String,
          imageLink: offerDetails['imageLink'] as String,
          info: offerDetails['info'] as String?,
          offerValue: offerDetails['offerValue'] as double,
          discountedValue:
              ParseUtils.parseNumToDouble(offerDetails['discountedValue']),
          redemptionType:
              OfferRedemptionType.values[offerDetails['redemptionType'] as int],
          isRedeemedToday: true,
        );

        // Store the offer
        offers.add(offer);
      } catch (e) {
        AppLogger.error('Error converting redeemed offer record: $e');
      }
    }

    return offers;
  }

  /// Get redeemed offers with additional metadata
  Future<List<RedeemedOfferData>> getRedeemedOffersWithMetadata() async {
    final records = await getRedeemedOffers();
    final offers = <RedeemedOfferData>[];

    for (final record in records) {
      try {
        final offerDetails = record['offerDetails'] as Map<String, dynamic>;
        // VIBEPOINTS FEATURE COMMENTED OUT
        // final vibePointsEarned = record['vibePointsEarned'] as int? ?? 0;
        final redemptionDate = record['redemptionDate'] as String;
        final redemptionCode = record['redemptionCode'] as String;

        // Create the offer model
        final offer = OfferModel(
          id: record['offerId'] as String,
          venueID: record['venueId'] as String,
          title: offerDetails['title'] as String,
          description: offerDetails['description'] as String,
          venueName: offerDetails['venueName'] as String,
          imageLink: offerDetails['imageLink'] as String,
          info: offerDetails['info'] as String?,
          offerValue: offerDetails['offerValue'] as double,
          discountedValue:
              ParseUtils.parseNumToDouble(offerDetails['discountedValue']),
          redemptionType:
              OfferRedemptionType.values[offerDetails['redemptionType'] as int],
          isRedeemedToday: true,
        );

        // Create redeemed offer data with metadata
        offers.add(
          RedeemedOfferData(
            offer: offer,
            // VIBEPOINTS FEATURE COMMENTED OUT
            // vibePointsEarned: vibePointsEarned,
            redemptionDate: redemptionDate,
            redemptionCode: redemptionCode,
          ),
        );
      } catch (e) {
        AppLogger.error('Error converting redeemed offer record: $e');
      }
    }

    return offers;
  }

  /// Get redeemed offers grouped by venue
  Future<List<VenueRedeemedOffers>> getRedeemedOffersGroupedByVenue() async {
    final redeemedOffers = await getRedeemedOffersWithMetadata();

    // Debug: Print all redeemed offers
    AppLogger.info('Total redeemed offers: ${redeemedOffers.length}');
    for (final offer in redeemedOffers) {
      AppLogger.info(
        'Offer ID: ${offer.offer.id}, Venue ID: ${offer.offer.venueID}, Title: ${offer.offer.title}',
      );
    }

    final venueMap = <String, VenueRedeemedOffers>{};

    for (final redeemedOffer in redeemedOffers) {
      final venueId = redeemedOffer.offer.venueID;
      final venueName = redeemedOffer.offer.venueName;

      if (venueMap.containsKey(venueId)) {
        // Add to existing venue group
        AppLogger.info(
          'Adding offer ${redeemedOffer.offer.id} to existing venue group $venueId',
        );
        venueMap[venueId]!.offers.add(redeemedOffer);

        // Update latest redemption date if this one is newer
        if (redeemedOffer.redemptionDate
                .compareTo(venueMap[venueId]!.latestRedemptionDate) >
            0) {
          venueMap[venueId]!.latestRedemptionDate =
              redeemedOffer.redemptionDate;
        }
      } else {
        // Create new venue group
        AppLogger.info(
          'Creating new venue group for $venueId with offer ${redeemedOffer.offer.id}',
        );
        venueMap[venueId] = VenueRedeemedOffers(
          venueId: venueId,
          venueName: venueName,
          offers: [redeemedOffer],
          latestRedemptionDate: redeemedOffer.redemptionDate,
        );
      }
    }

    // Debug: Print grouped offers
    for (final venueId in venueMap.keys) {
      final venue = venueMap[venueId]!;
      AppLogger.info(
        'Venue ${venue.venueName} (${venue.venueId}) has ${venue.offers.length} offers',
      );
      for (final offer in venue.offers) {
        AppLogger.info(
          '  - Offer ID: ${offer.offer.id}, Title: ${offer.offer.title}',
        );
      }
    }

    // Convert map to list and sort by latest redemption date (newest first)
    final result = venueMap.values.toList()
      ..sort(
        (a, b) => b.latestRedemptionDate.compareTo(a.latestRedemptionDate),
      );

    return result;
  }

  /// Get a specific redeemed offer by ID
  Future<Map<String, dynamic>?> getRedeemedOfferById(
    String offerId,
    String venueId,
  ) async {
    final redeemed = await getRedeemedOffers();

    try {
      return redeemed.firstWhere(
        (item) => item['offerId'] == offerId && item['venueId'] == venueId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Remove a redeemed offer
  Future<void> removeRedeemedOffer(String offerId, String venueId) async {
    try {
      final key = await _getUserSpecificKey();
      final redeemed = await getRedeemedOffers();

      redeemed.removeWhere(
        (item) => item['offerId'] == offerId && item['venueId'] == venueId,
      );

      await _storage.write(
        key: key,
        value: jsonEncode(redeemed),
      );
    } catch (e) {
      AppLogger.error('Error removing redeemed offer: $e');
    }
  }

  /// Clear all redeemed offers
  Future<void> clearRedeemedOffers() async {
    try {
      final key = await _getUserSpecificKey();
      await _storage.delete(key: key);
    } catch (e) {
      AppLogger.error('Error clearing redeemed offers: $e');
    }
  }
}
