import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:vibeo/analytics/analytics.dart';
import 'package:vibeo/utils/log/app_logger.dart';

/// Service for managing basket items using Flutter secure storage
/// Stores offer IDs mapped to venue IDs along with timestamps for sorting
class BasketStorageService {
  static const _basketKey = 'basket_items';
  final FlutterSecureStorage _storage;
  final String userID;

  BasketStorageService(this.userID, {FlutterSecureStorage? storage})
      : _storage = storage ?? const FlutterSecureStorage();

  /// Get the user-specific key for basket storage
  String _getUserSpecificKey() {
    return '${_basketKey}_$userID';
  }

  /// Add an offer to the basket under its venue ID with a timestamp
  Future<void> addOfferToBasket(String venueID, String offerID) async {
    try {
      if (venueID.isEmpty || offerID.isEmpty) {
        AppLogger.error(
          'Cannot add offer to basket: venueID or offerID is empty',
        );
        return;
      }

      final basket = await getBasketItems();

      // Initialize venue entry if it doesn't exist
      if (!basket.containsKey(venueID)) {
        basket[venueID] = [];
      }

      // Check if offer already exists in basket
      final offerExists = basket[venueID]!
          .any((item) => item is Map ? item['id'] == offerID : item == offerID);

      // Only add if it doesn't already exist
      if (!offerExists) {
        // Add offer with timestamp
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        basket[venueID]!.add({
          'id': offerID,
          'timestamp': timestamp,
        });
        await _saveBasketToStorage(basket);
        final AnalyticsService analytics = AnalyticsService.instance;
        await analytics.trackOfferSaved(offerID);
      }
    } catch (e) {
      AppLogger.error('Error adding offer to basket: $e');
    }
  }

  /// Remove an offer from the basket
  Future<void> removeOfferFromBasket(String venueID, String offerID) async {
    try {
      if (venueID.isEmpty || offerID.isEmpty) {
        AppLogger.error(
          'Cannot remove offer from basket: venueID or offerID is empty',
        );
        return;
      }

      final basket = await getBasketItems();

      if (basket.containsKey(venueID)) {
        // Remove the offer with the matching ID
        basket[venueID]!.removeWhere(
          (item) => item is Map ? item['id'] == offerID : item == offerID,
        );

        // Remove venue entry if empty
        if (basket[venueID]!.isEmpty) {
          basket.remove(venueID);
        }

        await _saveBasketToStorage(basket);
      }
    } catch (e) {
      AppLogger.error('Error removing offer from basket: $e');
    }
  }

  /// Check if an offer is in the basket
  Future<bool> isOfferInBasket(String venueID, String offerID) async {
    try {
      if (venueID.isEmpty || offerID.isEmpty) {
        return false;
      }

      final basket = await getBasketItems();

      if (basket.containsKey(venueID)) {
        return basket[venueID]!.any(
          (item) => item is Map ? item['id'] == offerID : item == offerID,
        );
      }

      return false;
    } catch (e) {
      AppLogger.error('Error checking if offer is in basket: $e');
      return false;
    }
  }

  /// Get all items in the basket
  /// Returns a map of venue IDs to lists of offer items (either strings or maps with id and timestamp)
  Future<Map<String, List<dynamic>>> getBasketItems() async {
    try {
      final key = _getUserSpecificKey();
      final storedData = await _storage.read(key: key);

      if (storedData == null || storedData.isEmpty) {
        return {};
      }

      final dynamic decoded = jsonDecode(storedData);

      // Handle case where the data might be in the old format or invalid
      if (decoded is! Map) {
        return {};
      }

      final Map<String, List<dynamic>> basket = {};

      decoded.forEach((venueID, offers) {
        if (offers is List) {
          // Process each item in the list
          final List<dynamic> offerItems = [];
          for (final item in offers) {
            if (item is String) {
              // Legacy format: just the offer ID as a string
              // Convert to new format with current timestamp
              offerItems.add({
                'id': item,
                'timestamp': DateTime.now()
                    .millisecondsSinceEpoch, // Use current time for legacy items
              });
            } else if (item is Map) {
              if (item.containsKey('id')) {
                // New format with timestamp or partial format
                final id = item['id'];
                if (id is String && id.isNotEmpty) {
                  // Ensure it has a timestamp
                  final timestamp = item['timestamp'] ??
                      DateTime.now().millisecondsSinceEpoch;
                  offerItems.add({
                    'id': id,
                    'timestamp': timestamp,
                  });
                }
              }
            }
          }
          if (offerItems.isNotEmpty) {
            basket[venueID.toString()] = offerItems;
          }
        }
      });

      return basket;
    } catch (e) {
      // If there's any error, return an empty map
      AppLogger.error('Error reading basket items: $e');
      return {};
    }
  }

  Future<int> getBasketItemCountForVenue(String venueID) async {
    final basket = await getBasketItems();
    final items = basket[venueID] ?? [];
    return items.length;
  }

  /// Get offer IDs for a specific venue
  Future<List<String>> getOfferIDsForVenue(String venueID) async {
    final basket = await getBasketItems();
    final items = basket[venueID] ?? [];

    // Extract just the IDs from the items
    return items
        .map<String>(
          (item) => item is Map ? item['id'] as String : item as String,
        )
        .toList();
  }

  /// Get the count of all offers in the basket
  Future<int> getBasketItemCount() async {
    final basket = await getBasketItems();
    final int count = basket.length;

    return count;
  }

  /// Get basket items sorted by timestamp (newest first)
  Future<Map<String, List<dynamic>>> getBasketItemsSorted() async {
    final basket = await getBasketItems();

    // Sort each venue's offers by timestamp (descending)
    basket.forEach((venueID, offers) {
      offers
        ..sort((a, b) {
          final int timestampA = a is Map ? (a['timestamp'] as int? ?? 0) : 0;
          final int timestampB = b is Map ? (b['timestamp'] as int? ?? 0) : 0;
          return timestampB.compareTo(timestampA); // Descending order
        })
        ..sort((a, b) {
          final int priorityA = a is Map ? (a['priority'] as int? ?? 1) : 1;
          final int priorityB = b is Map ? (b['priority'] as int? ?? 1) : 1;

          return priorityB.compareTo(priorityA);
        });
    });

    return basket;
  }

  /// Save the basket to secure storage
  Future<void> _saveBasketToStorage(Map<String, List<dynamic>> basket) async {
    final key = _getUserSpecificKey();
    await _storage.write(
      key: key,
      value: jsonEncode(basket),
    );
  }

  /// Clear the entire basket
  Future<void> clearBasket() async {
    final key = _getUserSpecificKey();
    await _storage.delete(key: key);
  }
}
