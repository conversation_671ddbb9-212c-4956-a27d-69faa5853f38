<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Vibeo</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Vibeo</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.988175768093-3s572blckpm5st306tors9dreeuo07d6</string>
			</array>
		</dict>
		<dict/>
		<dict/>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>uber</string>
		<string>lyft</string>
		<string>comgooglemaps</string>
		<string>maps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSCameraUsageDescription</key>
	<string>Vibeo requires camera access to enable users to record and upload videos to the platform.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Location access is required to provide personalized recommendations and real-time updates based on your location.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We use your location to show nearby places and provide accurate directions and personalized experiences.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Microphone access is needed to capture audio when recording videos within the app.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This permission is required to add media files directly to your device’s photo library.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Access to your photo library is needed to save and share media content through the app.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>itms</string>
	</array>
<key>NFCReaderUsageDescription</key>
    <string>We use NFC to validate offers and redemption.</string>
    <key>com.apple.developer.nfc.readersession.felica.systemcodes</key>
     <array>
       <string>8005</string>
       <string>8008</string>
       <string>0003</string>
       <string>fe00</string>
       <string>90b7</string>
       <string>927a</string>
       <string>86a7</string>
     </array>
    <key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
    <array>
        <string>A0000002471001</string>
        <string>A000000003101001</string>
        <string>A000000003101002</string>
        <string>A0000000041010</string>
        <string>A0000000042010</string>
        <string>A0000000044010</string>
        <string>44464D46412E44466172653234313031</string>
        <string>D2760000850100</string>
        <string>D2760000850101</string>
        <string>00000000000000</string>
    </array>
</dict>
</plist>
