Welcome to firepit v1.1.0!
Doing JSON parses for version checks at /snapshot/firepit/vendor/node_modules/firebase-tools/package.json
is-ci,mime,rc,yaml,abbrev,abort-controller,accepts,agent-base,aggregate-error,ajv,ajv-formats,ansi-align,ansi-escapes,ansi-regex,ansi-styles,any-promise,anymatch,archiver,archiver-utils,argparse,array-flatten,arrify,as-array,ast-types,async,async-lock,asynckit,b4a,balanced-match,bare-events,base64-js,basic-auth,basic-auth-connect,basic-ftp,bignumber.js,binary-extensions,bl,body-parser,boxen,brace-expansion,braces,buffer,buffer-crc32,buffer-equal-constant-time,bytes,cacache,call-bind,call-me-maybe,camelcase,chalk,char-regex,chardet,chokidar,chownr,ci-info,cjson,clean-stack,cli-boxes,cli-cursor,cli-highlight,cli-spinners,cli-table,cli-table3,cli-width,cliui,clone,color,color-convert,color-name,color-string,colorette,colors,colorspace,combined-stream,commander,compress-commons,compressible,compression,concat-map,config-chain,configstore,connect,content-disposition,content-type,cookie,cookie-signature,core-util-is,cors,crc-32,crc32-stream,cross-env,cross-spawn,crypto-random-string,csv-parse,data-uri-to-buffer,debug,deep-equal-in-any-order,deep-extend,deep-freeze,deep-is,defaults,define-data-property,degenerator,delayed-stream,depd,destroy,discontinuous-range,dot-prop,duplexify,eastasianwidth,ecdsa-sig-formatter,ee-first,emoji-regex,emojilib,enabled,encodeurl,encoding,end-of-stream,env-paths,environment,err-code,es-define-property,es-errors,escalade,escape-goat,escape-html,escape-string-regexp,escodegen,esprima,estraverse,esutils,etag,event-target-shim,events,events-listener,exegesis,exegesis-express,exponential-backoff,express,extend,external-editor,fast-deep-equal,fast-fifo,fast-json-stable-stringify,fast-uri,fast-url-parser,fecha,figures,filesize,fill-range,finalhandler,firebase-tools,fn.name,foreground-child,form-data,forwarded,fresh,fs-extra,fs-minipass,function-bind,fuzzy,gaxios,gcp-metadata,get-caller-file,get-intrinsic,get-stdin,get-uri,glob,glob-parent,glob-slash,glob-slasher,global-dirs,google-auth-library,google-gax,googleapis-common,gopd,graceful-fs,gtoken,has-flag,has-property-descriptors,has-proto,has-symbols,has-yarn,hasown,heap-js,highlight.js,http-cache-semantics,http-errors,http-proxy-agent,https-proxy-agent,iconv-lite,ieee754,import-lazy,imurmurhash,indent-string,inherits,ini,inquirer,inquirer-autocomplete-prompt,install-artifact-from-github,ip-address,ip-regex,ipaddr.js,is-arrayish,is-binary-path,is-extglob,is-fullwidth-code-point,is-glob,is-installed-globally,is-interactive,is-lambda,is-npm,is-number,is-obj,is-path-inside,is-stream,is-stream-ended,is-typedarray,is-unicode-supported,is-url,is-wsl,is-yarn-global,is2,isarray,isexe,isomorphic-fetch,jackspeak,jju,join-path,js-yaml,jsbn,json-bigint,json-parse-helpfulerror,json-ptr,json-schema-traverse,jsonfile,jsonwebtoken,jwa,jws,kuler,lazystream,leven,libsodium,libsodium-wrappers,lodash,lodash._objecttypes,lodash.camelcase,lodash.includes,lodash.isboolean,lodash.isinteger,lodash.isnumber,lodash.isobject,lodash.isplainobject,lodash.isstring,lodash.mapvalues,lodash.once,lodash.snakecase,log-symbols,logform,long,lru-cache,make-dir,make-fetch-happen,marked,marked-terminal,media-typer,merge-descriptors,methods,mime-db,mime-types,mimic-fn,minimatch,minimist,minipass,minipass-collect,minipass-fetch,minipass-flush,minipass-pipeline,minipass-sized,minizlib,mkdirp,moo,morgan,ms,mute-stream,mz,nan,nearley,negotiator,netmask,nice-try,node-emoji,node-fetch,node-gyp,nopt,normalize-path,object-assign,object-hash,object-inspect,on-finished,on-headers,once,one-time,onetime,open,openapi3-ts,ora,os-tmpdir,p-defer,p-limit,p-map,p-throttle,pac-proxy-agent,pac-resolver,package-json-from-dist,parse5,parse5-htmlparser2-tree-adapter,parseurl,path-key,path-scurry,path-to-regexp,pg,pg-cloudflare,pg-connection-string,pg-int8,pg-pool,pg-protocol,pg-types,pgpass,picocolors,picomatch,portfinder,postgres-array,postgres-bytea,postgres-date,postgres-interval,proc-log,process,process-nextick-args,progress,promise-breaker,promise-retry,proto-list,proto3-json-serializer,protobufjs,proxy-addr,proxy-agent,proxy-from-env,pump,punycode,pupa,qs,queue-tick,railroad-diagrams,randexp,range-parser,raw-body,re2,readable-stream,readdir-glob,readdirp,registry-auth-token,registry-url,require-directory,require-from-string,restore-cursor,ret,retry,retry-request,rimraf,router,run-async,rxjs,safe-buffer,safe-stable-stringify,safer-buffer,semver,semver-diff,send,serve-static,set-function-length,setprototypeof,shebang-command,shebang-regex,side-channel,signal-exit,simple-swizzle,skin-tone,smart-buffer,socks,socks-proxy-agent,sort-any,source-map,split2,sprintf-js,sql-formatter,ssri,stack-trace,statuses,stream-chain,stream-events,stream-json,stream-shift,streamx,string-width,string-width-cjs,string_decoder,strip-ansi,strip-ansi-cjs,strip-json-comments,stubs,superstatic,supports-color,supports-hyperlinks,tar,tar-stream,tcp-port-used,teeny-request,text-decoder,text-hex,thenify,thenify-all,through,tmp,to-regex-range,toidentifier,toxic,tr46,triple-beam,tslib,type-fest,type-is,typedarray-to-buffer,undici-types,unicode-emoji-modifier-base,unique-filename,unique-slug,unique-string,universal-analytics,universalify,unpipe,update-notifier-cjs,uri-js,url-join,url-template,util-deprecate,utils-merge,uuid,valid-url,vary,wcwidth,webidl-conversions,whatwg-fetch,whatwg-url,which,widest-line,winston,winston-transport,wrap-ansi,wrap-ansi-cjs,wrappy,write-file-atomic,ws,xdg-basedir,xtend,y18n,yallist,yargs,yargs-parser,yocto-queue,zip-stream,@apidevtools,@colors,@dabh,@google-cloud,@googleapis,@grpc,@isaacs,@js-sdsl,@jsdevtools,@npmcli,@opentelemetry,@pkgjs,@pnpm,@protobufjs,@sindresorhus,@tootallnate,@types
Installed ft@13.15.4 and packaged ft@13.15.4
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
ShellJSInternalError: ENOENT: no such file or directory, unlink '/Users/<USER>/.cache/firebase/runtime/npm'
Runtime binaries created.
/usr/local/bin/firebase
/usr/local/bin/firebase,/snapshot/firepit/firepit.js,--version
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/lib/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /Users/<USER>/.cache/firebase/tools/node_modules/npm/bin/npm-cli
Checking for npm/bin/npm-cli install at /snapshot/firepit/node_modules/npm/bin/npm-cli
Found npm/bin/npm-cli install.
ShellJSInternalError: ENOENT: no such file or directory, stat '/Users/<USER>/.cache/firebase/runtime/shell'